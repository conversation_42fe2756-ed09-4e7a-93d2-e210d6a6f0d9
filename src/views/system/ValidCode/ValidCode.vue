<template>
  <el-card :shadow="'never'">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <el-form :inline="true" class="search-form">
        <el-row :gutter="48">
          <el-col :md="8" :sm="24">
            <el-form-item label="请求方式" class="w-full">
              <el-select
                v-model="queryParam.method"
                placeholder="请选择请求方式"
                clearable
              >
                <el-option value="GET" label="GET" />
                <el-option value="POST" label="POST" />
                <el-option value="PUT" label="PUT" />
                <el-option value="DELETE" label="DELETE" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="8" :sm="24">
            <el-form-item label="验证码类型" class="w-full">
              <el-select
                v-model="queryParam.type"
                placeholder="请选择验证码类型"
                clearable
              >
                <el-option value="image" label="图片验证码" />
                <el-option value="sms" label="短信验证码" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="
                (advanced && { float: 'right', overflow: 'hidden' }) || {}
              "
            >
              <el-button type="primary" @click="refresh(true)">查询</el-button>
              <el-button
                style="margin-left: 8px"
                @click="() => (queryParam = {})"
                >重置</el-button
              >
              <el-button text style="margin-left: 8px" @click="toggleAdvanced">
                {{ advanced ? '收起' : '展开' }}
                <el-icon
                  ><component :is="advanced ? 'ArrowUpIcon' : 'ArrowDownIcon'"
                /></el-icon>
              </el-button>
            </span>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator flex mt-2">
      <div class="left-part">
        <el-button type="primary" @click="handleAdd">
          <el-icon><PlusIcon /></el-icon>新建
        </el-button>
        <el-dropdown v-if="selectedRowKeys.length > 0">
          <el-button style="margin-left: 8px">
            批量操作
            <el-icon><ArrowDownIcon /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="deleteBatchItems">
                <el-icon><DeleteIcon /></el-icon>
                删除
              </el-dropdown-item>
              <el-dropdown-item>
                <el-icon><LockIcon /></el-icon>
                锁定
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- table区域-begin -->
    <div>
      <el-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="modelId"
        @selection-change="onSelectChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="#" width="80">
          <template #default="{ $index }">
            {{ (pagination.current - 1) * pagination.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="模式key" prop="modelKey" />
        <el-table-column label="拦截地址" prop="url" />
        <el-table-column label="请求方式" prop="method" />
        <el-table-column label="验证码类型" prop="type">
          <template #default="{ row }">
            <span v-if="row.type === 'image'">图片验证码</span>
            <span v-else-if="row.type === 'sms'">短信验证码</span>
            <span v-else>{{ row.type }}</span>
          </template>
        </el-table-column>
        <el-table-column label="延迟验证" prop="delay">
          <template #default="{ row }">
            {{ row.delay ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="{ row }">
            <el-popconfirm
              title="确定要删除吗？"
              @confirm="() => deleteOne(row.modelId)"
            >
              <template #reference>
                <el-button text type="danger" size="small">
                  <el-icon><DeleteIcon /></el-icon> 删除
                </el-button>
              </template>
            </el-popconfirm>
            <el-divider direction="vertical" />
            <el-button text size="small" @click="handleEdit(row)">
              <el-icon><EditIcon /></el-icon> 编辑
            </el-button>
            <el-divider direction="vertical" />
            <el-button text size="small" @click="handleDetail(row)">
              <el-icon><SearchIcon /></el-icon> 详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        style="margin-top: 20px; text-align: right"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- table区域-end -->

    <ValidCodeModal ref="modalForm" @cancel="handleCancel" @ok="handleOk" />
  </el-card>
</template>

<script>
import {
  Plus as PlusIcon,
  ArrowDown as ArrowDownIcon,
  ArrowUp as ArrowUpIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Search as SearchIcon,
  Lock as LockIcon
} from '@element-plus/icons-vue'
import { del, get } from '@/utils/http/request'
import ValidCodeModal from './components/ValidCodeModal.vue'

export default {
  name: 'ValidCodeList',
  components: {
    ValidCodeModal,
    PlusIcon,
    ArrowDownIcon,
    ArrowUpIcon,
    DeleteIcon,
    EditIcon,
    SearchIcon,
    LockIcon
  },
  data() {
    return {
      // 查询参数
      queryParam: {},
      // 高级搜索 展开/关闭
      advanced: false,
      // 表格数据
      tableData: [],
      // 分页配置
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      // 加载状态
      loading: false,
      // 选中的行
      selectedRowKeys: [],
      selectedRows: [],
      // URL配置
      url: {
        list: '/voucher/s/validate/list',
        delete: id => `/voucher/s/validate/${id}`
      }
    }
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    }
  },
  mounted() {
    this.loadTableData()
  },
  methods: {
    // 加载表格数据
    async loadTableData() {
      this.loading = true
      try {
        const parameter = {
          pageNo: this.pagination.current,
          pageSize: this.pagination.pageSize,
          ...this.queryParam
        }
        const res = await this.loadData(parameter)
        if (res && res.success) {
          this.tableData = res.data || res.result || []
          this.pagination.total = res.totalRecords || 0
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 数据加载方法
    loadData(parameter) {
      return get(this.url.list, parameter)
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.current = 1
      this.loadTableData()
    },

    // 当前页改变
    handleCurrentChange(current) {
      this.pagination.current = current
      this.loadTableData()
    },

    // 选择改变
    onSelectChange(selection) {
      this.selectedRowKeys = selection.map(item => item.modelId)
      this.selectedRows = selection
    },

    // 刷新表格
    refresh(bool = false) {
      if (bool) {
        this.pagination.current = 1
      }
      this.loadTableData()
    },

    // 切换高级搜索
    toggleAdvanced() {
      this.advanced = !this.advanced
    },

    // 新增
    handleAdd() {
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = '新增'
    },

    // 编辑
    handleEdit(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
    },

    // 详情
    handleDetail(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '详情'
      this.$refs.modalForm.disabled = true
    },

    // 删除单个
    async deleteOne(id) {
      try {
        const res = await del(this.url.delete(id))
        if (res.success) {
          this.$message.success(res.message || '删除成功')
          this.refresh()
        } else {
          this.$message.error(res.message || '删除失败')
        }
      } catch (error) {
        this.$message.error('删除失败')
      }
    },

    // 批量删除
    async deleteBatchItems() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      try {
        const res = await del(this.url.deleteBatch, this.selectedRowKeys)
        if (res.success) {
          this.$message.success(res.message || '删除成功')
          this.selectedRowKeys = []
          this.selectedRows = []
          this.refresh()
        } else {
          this.$message.error(res.message || '删除失败')
        }
      } catch (error) {
        this.$message.error('删除失败')
      }
    },

    // 模态框确定
    handleOk() {
      this.refresh()
    },

    // 模态框取消
    handleCancel() {
      // 取消操作
    }
  }
}
</script>
