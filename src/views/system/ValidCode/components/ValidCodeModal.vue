<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="720px"
    :close-on-click-modal="false"
    @close="onCancel"
  >
    <el-form
      ref="modalForm"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-width="150px"
    >
      <el-form-item label="模式key" prop="modelKey">
        <el-input
          v-model="form.modelKey"
          placeholder="请输入模式key,为空则全局生效"
          :disabled="disabled"
        />
      </el-form-item>
      <el-form-item label="拦截地址" prop="url">
        <el-input
          v-model="form.url"
          placeholder="请输入拦截地址"
          :disabled="disabled"
        />
      </el-form-item>
      <el-form-item label="请求方式" prop="method">
        <el-select
          v-model="form.method"
          placeholder="请选择请求方式"
          :disabled="disabled"
          style="width: 100%"
        >
          <el-option value="GET" label="GET" />
          <el-option value="POST" label="POST" />
          <el-option value="PUT" label="PUT" />
          <el-option value="DELETE" label="DELETE" />
        </el-select>
      </el-form-item>
      <el-form-item label="验证码类型" prop="type">
        <el-select
          v-model="form.type"
          placeholder="请选择验证码类型"
          multiple
          :disabled="disabled"
          style="width: 100%"
        >
          <el-option value="image" label="图片验证码" />
          <el-option value="sms" label="短信验证码" />
        </el-select>
      </el-form-item>
      <el-form-item label="验证模式" prop="codeModel">
        <el-select
          v-model="form.codeModel"
          placeholder="请选择验证模式"
          :disabled="disabled"
          style="width: 100%"
          @change="onCodeModelChange"
        >
          <el-option value="NONE" label="不验证" />
          <el-option value="ALL" label="全部验证" />
          <el-option value="ANY" label="任意数量" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="anyModel" label="验证数量" prop="needPassNumber">
        <el-input-number
          v-model="form.needPassNumber"
          placeholder="请输入验证数量"
          :disabled="disabled"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="延迟验证" prop="delay">
        <el-radio-group
          v-model="form.delay"
          :disabled="disabled"
          @change="onDelayChange"
        >
          <el-radio
            :value="true"
            style="display: inline-block; margin-right: 20px"
            >开启</el-radio
          >
          <el-radio :value="false" style="display: inline-block">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="delay" label="延迟验证次数" prop="count">
        <el-input-number
          v-model="form.count"
          placeholder="请输入延迟验证次数"
          :disabled="disabled"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item
        v-if="delay"
        label="延迟验证次数最大存储时间"
        prop="reCountTime"
      >
        <el-input-number
          v-model="form.reCountTime"
          placeholder="请输入延迟验证次数最大存储时间"
          :disabled="disabled"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button
          v-if="!disabled"
          type="primary"
          :loading="loading"
          @click="onSubmit"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { post, put } from '@/utils/http/request'

const rules = {
  modelKey: [{ required: false, message: '请输入模式key', trigger: 'blur' }],
  url: [{ required: true, message: '请输入拦截地址', trigger: 'blur' }],
  method: [{ required: true, message: '请输入请求方式', trigger: 'change' }],
  type: [{ required: true, message: '请输入验证码类型', trigger: 'change' }],
  codeModel: [{ required: true, message: '请输入验证模式', trigger: 'change' }],
  needPassNumber: [
    { required: false, message: '请输入验证数量', trigger: 'blur' }
  ],
  delay: [{ required: true, message: '请输入延迟验证', trigger: 'change' }],
  count: [{ required: false, message: '请输入延迟验证次数', trigger: 'blur' }],
  reCountTime: [
    {
      required: false,
      message: '请输入延迟验证次数最大存储时间',
      trigger: 'blur'
    }
  ]
}

export default {
  name: 'ValidCodeModal',
  components: {},
  data() {
    return {
      rules,
      visible: false,
      loading: false,
      title: '',
      disabled: false,
      form: {
        modelKey: null,
        url: null,
        method: null,
        type: [],
        codeModel: null,
        needPassNumber: null,
        delay: false,
        count: null,
        reCountTime: null
      },
      urls: {
        save: '/voucher/s/validate',
        update: id => `/voucher/s/validate/${id}`
      },
      delay: false,
      anyModel: false
    }
  },
  watch: {
    'form.delay': function (v) {
      this.delay = v
    },
    'form.codeModel': function (v) {
      if (v === 'ANY') {
        this.anyModel = true
      } else {
        this.anyModel = false
      }
    }
  },
  methods: {
    // 新增
    add() {
      this.visible = true
      this.disabled = false
      this.title = '新增'
      this.form = {
        modelKey: null,
        url: null,
        method: null,
        type: [],
        codeModel: null,
        needPassNumber: null,
        delay: false,
        count: null,
        reCountTime: null
      }
      this.delay = false
      this.anyModel = false
    },

    // 编辑
    edit(record, title) {
      this.visible = true
      this.disabled = false
      this.title = title || '编辑'
      this.form = { ...record }
      this.form.id = record.modelId
      this.form.type = record.type ? record.type.split(',') : []
    },

    // 提交
    async onSubmit() {
      try {
        await this.$refs.modalForm.validate()
        this.loading = true

        // 处理 type 数组
        const formData = { ...this.form }
        formData.type = formData.type.join(',')

        let result
        if (formData.id) {
          // 编辑
          result = await put(this.urls.update(formData.id), formData)
        } else {
          // 新增
          result = await post(this.urls.save, formData)
        }

        if (result.success) {
          this.$message.success(result.message || '操作成功')
          this.onCancel()
          this.$emit('ok')
        } else {
          this.$message.error(result.message || '操作失败')
        }
      } catch (error) {
        console.error('提交失败:', error)
        if (error.fields) {
          // 表单验证失败
          return
        }
        this.$message.error('操作失败')
      } finally {
        this.loading = false
      }
    },

    // 取消
    onCancel() {
      this.visible = false
      this.loading = false
      this.disabled = false
      this.delay = false
      this.anyModel = false
      this.$refs.modalForm?.resetFields()
      this.$emit('cancel')
    },

    // 延迟验证改变
    onDelayChange(value) {
      this.delay = value
    },

    // 验证模式改变
    onCodeModelChange(value) {
      if (value === 'ANY') {
        this.anyModel = true
      } else {
        this.anyModel = false
      }
    }
  }
}
</script>
