<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="720px"
    :close-on-click-modal="false"
    @close="onCancel"
  >
    <el-form
      ref="modalForm"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="form.projectName"
          placeholder="请输入项目名称"
          :disabled="disabled"
        />
      </el-form-item>
      <el-form-item label="项目代码" prop="projectCode">
        <el-input
          v-model="form.projectCode"
          placeholder="请输入项目代码"
          :disabled="disabled"
        />
      </el-form-item>
      <el-form-item label="项目类型" prop="projectType">
        <el-select
          v-model="form.projectType"
          placeholder="请选择项目类型"
          :disabled="disabled"
          style="width: 100%"
        >
          <el-option value="00021" label="公共项目" />
          <el-option value="00022" label="分配项目" />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button
          v-if="!disabled"
          type="primary"
          :loading="loading"
          @click="onSubmit"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { post, put } from '@/utils/http/request'

const rules = {
  projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  projectCode: [{ required: true, message: '请输入项目代码', trigger: 'blur' }],
  projectType: [
    { required: true, message: '请输入项目类型', trigger: 'change' }
  ]
}

export default {
  name: 'ProjectModal',
  components: {},
  data() {
    return {
      rules,
      visible: false,
      loading: false,
      title: '',
      disabled: false,
      form: {
        projectName: null,
        projectCode: null,
        projectType: null
      },
      urls: {
        save: '/voucher/admin/project',
        update: id => `/voucher/admin/project/${id}`
      }
    }
  },
  methods: {
    // 新增
    add() {
      this.visible = true
      this.disabled = false
      this.title = '新增'
      this.form = {
        projectName: null,
        projectCode: null,
        projectType: null
      }
    },

    // 编辑
    edit(record, title) {
      this.visible = true
      this.disabled = false
      this.title = title || '编辑'
      this.form = { ...record }
      this.form.id = record.projectId
    },

    // 提交
    async onSubmit() {
      try {
        await this.$refs.modalForm.validate()
        this.loading = true

        let result
        if (this.form.id) {
          // 编辑
          result = await put(this.urls.update(this.form.id), this.form)
        } else {
          // 新增
          result = await post(this.urls.save, this.form)
        }

        if (result.success) {
          this.$message.success(result.message || '操作成功')
          this.onCancel()
          this.$emit('ok')
        } else {
          this.$message.error(result.message || '操作失败')
        }
      } catch (error) {
        console.error('提交失败:', error)
        if (error.fields) {
          // 表单验证失败
          return
        }
        this.$message.error('操作失败')
      } finally {
        this.loading = false
      }
    },

    // 取消
    onCancel() {
      this.visible = false
      this.loading = false
      this.disabled = false
      this.$refs.modalForm?.resetFields()
      this.$emit('cancel')
    }
  }
}
</script>
