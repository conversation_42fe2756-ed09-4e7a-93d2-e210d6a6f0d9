<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="720px"
    :close-on-click-modal="false"
    @close="onCancel"
  >
    <el-form
      ref="modalForm"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="所属项目" prop="projectId">
        <dict-selector
          v-model="form.projectId"
          url="/voucher/admin/project/info"
          :fields="{
            label: 'projectName',
            value: 'projectId'
          }"
        />
      </el-form-item>
      <el-form-item label="客户端ID" prop="clientId">
        <el-input
          v-model="form.clientId"
          placeholder="请输入客户端ID"
          :disabled="disabled"
        />
      </el-form-item>
      <el-form-item label="客户端秘钥" prop="clientSecret">
        <el-input
          v-model="form.clientSecret"
          placeholder="请输入客户端秘钥"
          :disabled="disabled"
        />
      </el-form-item>
      <el-form-item label="token有效期" prop="tokenValidity">
        <el-input
          v-model="form.tokenValidity"
          placeholder="请输入token有效期"
          :disabled="disabled"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button
          v-if="!disabled"
          type="primary"
          :loading="loading"
          @click="onSubmit"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { post, put } from '@/utils/http/request'
import DictSelector from '@c/Dict/dict.js'

const rules = {
  projectId: [{ required: true, message: '请输入所属项目', trigger: 'change' }],
  clientId: [{ required: true, message: '请输入客户端ID', trigger: 'blur' }],
  clientSecret: [
    { required: true, message: '请输入客户端秘钥', trigger: 'blur' }
  ],
  tokenValidity: [
    { required: true, message: '请输入token有效期', trigger: 'blur' }
  ]
}

export default {
  name: 'ClientModal',
  components: { DictSelector },
  data() {
    return {
      rules,
      visible: false,
      loading: false,
      title: '',
      disabled: false,
      form: {
        projectId: null,
        clientId: null,
        clientSecret: null,
        tokenValidity: null
      },
      urls: {
        save: '/voucher/s/client',
        update: id => `/voucher/s/client/${id}`
      }
    }
  },
  methods: {
    // 新增
    add() {
      this.visible = true
      this.disabled = false
      this.title = '新增'
      this.form = {
        projectId: null,
        clientId: null,
        clientSecret: null,
        tokenValidity: null
      }
    },

    // 编辑
    edit(record, title) {
      this.visible = true
      this.disabled = false
      this.title = title || '编辑'
      this.form = { ...record }
      this.form.id = record.clientId
    },

    // 提交
    async onSubmit() {
      try {
        await this.$refs.modalForm.validate()
        this.loading = true

        let result
        if (this.form.id) {
          // 编辑
          result = await put(this.urls.update(this.form.id), this.form)
        } else {
          // 新增
          result = await post(this.urls.save, this.form)
        }

        if (result.success) {
          this.$message.success(result.message || '操作成功')
          this.onCancel()
          this.$emit('ok')
        } else {
          this.$message.error(result.message || '操作失败')
        }
      } catch (error) {
        console.error('提交失败:', error)
        if (error.fields) {
          // 表单验证失败
          return
        }
        this.$message.error('操作失败')
      } finally {
        this.loading = false
      }
    },

    // 取消
    onCancel() {
      this.visible = false
      this.loading = false
      this.disabled = false
      this.$refs.modalForm?.resetFields()
      this.$emit('cancel')
    }
  }
}
</script>
