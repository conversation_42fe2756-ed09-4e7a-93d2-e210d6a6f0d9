<template>
  <div class="tag-manager">
    <!-- 顶部类型Tab -->
    <el-tabs v-model="activeType" @tab-click="onTypeChange">
      <el-tab-pane
        v-for="type in tagTypes"
        :key="type.value"
        :label="type.label"
        :name="type.value"
      />
    </el-tabs>

    <div class="tag-content">
      <!-- 左侧Tag树 -->
      <div class="tag-tree">
        <el-tree
          :data="tagTreeData"
          :props="treeProps"
          node-key="id"
          @node-click="onTreeNodeClick"
        />
      </div>
      <!-- 右侧Tag分页表格 -->
      <div class="tag-table">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column label="标签名" prop="name" />
          <el-table-column label="描述" prop="description" />
        </el-table>
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          background
          layout="prev, pager, next"
          @current-change="onPageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 示例数据
const tagTypes = [
  { label: '类型A', value: 'A' },
  { label: '类型B', value: 'B' },
  { label: '类型C', value: 'C' }
]
const activeType = ref(tagTypes[0].value)

const tagTreeData = ref([
  { id: 1, label: '标签组1', children: [{ id: 2, label: '标签1-1' }] },
  { id: 3, label: '标签组2' }
])
const treeProps = { children: 'children', label: 'label' }

const tableData = ref([
  { name: '标签1', description: '描述1' },
  { name: '标签2', description: '描述2' }
])
const total = ref(2)
const pageSize = ref(10)
const currentPage = ref(1)

function onTypeChange(tab) {
  // 切换类型时刷新树和表格数据
}

function onTreeNodeClick(node) {
  // 选中树节点时刷新表格数据
}

function onPageChange(page) {
  currentPage.value = page
  // 分页切换时刷新表格数据
}
</script>

<style scoped>
.tag-manager {
  padding: 16px;
}

.tag-content {
  display: flex;
  height: 500px;
  margin-top: 16px;
}

.tag-tree {
  width: 240px;
  padding-right: 16px;
  margin-right: 24px;
  overflow-y: auto;
  border-right: 1px solid #eee;
}

.tag-table {
  display: flex;
  flex: 1;
  flex-direction: column;
  padding-left: 16px;
}

.el-table {
  flex: 1;
}
</style>
