<template>
  <div class="mb-3">
    <el-form :inline="true" label-width="auto">
      <el-form-item label="名称">
        <el-input v-model="queryParams.value" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="编码">
        <el-input v-model="queryParams.code" placeholder="请输入编码" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()"> 查询 </el-button>
        <el-button type="primary" @click="resetTable"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </div>
  <div class="mb-2">
    <el-button type="primary" @click="handleAdd"> 新增 </el-button>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
  >
    <el-table-column
      type="index"
      label="序号"
      width="60"
      align="center"
      :index="(index: number) => index + 1"
    />
    <el-table-column label="字典名称" prop="value" />
    <el-table-column label="字典编号" prop="code" />
    <el-table-column label="备注" prop="remark" />
    <el-table-column label="操作" width="200">
      <template #default="scope">
        <el-button
          size="small"
          type="success"
          link
          @click="showChild(scope.row)"
        >
          查看子表
        </el-button>
        <el-button
          size="small"
          type="primary"
          link
          @click="handleEdit(scope.row)"
        >
          编辑
        </el-button>
        <el-popconfirm
          class="box-item"
          title="确认删除该资源吗"
          placement="top-start"
          @confirm="handleDelete(scope.row)"
        >
          <template #reference>
            <el-button size="small" type="danger" link> 删除 </el-button>
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>
  <dialogForm ref="formRef" @ok="loadTableData(1)" />
  <!-- 二维码弹窗 -->
  <el-dialog
    v-model="dialogVisible"
    title="查看子表"
    width="1000px"
    :close-on-click-modal="false"
  >
    <div class="text-center">
      <dialogChildren :code="childCode" />
    </div>
  </el-dialog>
</template>
<script setup lang="ts">
import { ElButton, ElMessage, ElTable, ElTableColumn } from 'element-plus'
import { onMounted, ref } from 'vue'
import TableComposition from '@/composition/TableComposition'
import { useRouter } from 'vue-router'
import dialogForm from '@v/system/dicManager/components/dialogForm.vue'
import URLS from '@/api/URLS'
import dialogChildren from '@v/system/dicManager/components/dialogChildren.vue'
const router = useRouter()

const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit
} = TableComposition({
  urls: {
    list: '/build/dict/page',
    normal: '/build/dict',
    rowKey: 'id'
  }
})
// 搜索
const search = () => {
  console.log('search', queryParams.value)
  loadTableData(1)
}

onMounted(() => {
  loadTableData(1)
})
const dialogVisible = ref(false)
const childCode = ref('')
const showChild = data => {
  dialogVisible.value = true
  childCode.value = data.code
}
</script>
