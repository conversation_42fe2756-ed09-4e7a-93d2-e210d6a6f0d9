<script setup lang="ts">
import { reactive, watch, ref } from 'vue'
import FormComposition from '@/composition/FormCompostion'
import { get } from '@/utils/http/request'
const emits = defineEmits(['ok'])
const dealRecord = (record: any) => {
  console.log('dealRecord', record)
  formData.value.itemResultType = String(record.itemResultType)
}

const rules = reactive({
  value: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入编码', trigger: 'blur' }]
})

const dealSubmitParams = (data: any) => {
  console.log('dealSubmitParams', data)
}
const {
  visible,
  title,
  formData,
  formRef,
  loading,
  handleAdd,
  handleEdit,
  handleSubmit,
  handleClose
} = FormComposition({
  urls: {
    normal: '/build/dict',
    detail: '/build/dict',
    rowKey: 'id'
  },
  dealRecord,
  dealSubmitParams,
  emits,
  addInit: async () => {
    formData.value = {}
  }
})

defineExpose({ handleAdd, handleEdit })
</script>

<template>
  <el-dialog v-model="visible" :title="title" width="500">
    <el-form
      ref="formRef"
      :model="formData"
      label-width="120px"
      :rules="rules"
      class="check-form"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="名称" prop="value">
            <el-input v-model="formData.value" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="编码" prop="code">
            <el-input v-model="formData.code" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.option-list {
  margin-top: 8px;
  margin-left: 100px;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.option-label {
  width: 60px;
  padding: 2px 4px;
  margin-right: 6px;
  font-weight: bold;
  text-align: right;
  background: #eee;
  border-radius: 2px;
}
</style>
