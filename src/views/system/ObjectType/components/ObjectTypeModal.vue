<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="720px"
    :close-on-click-modal="false"
    @close="onCancel"
  >
    <el-form
      ref="modalForm"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="类型key" prop="code">
        <el-input
          v-model="form.code"
          placeholder="请输入类型key"
          :disabled="disabled"
        />
      </el-form-item>
      <el-form-item label="类型名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入类型名称"
          :disabled="disabled"
        />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-radio-group v-model="form.type" :disabled="disabled">
          <el-radio value="00101" style="display: block; margin-bottom: 10px"
            >公共对象</el-radio
          >
          <el-radio value="00102" style="display: block">分组对象</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number
          v-model="form.sort"
          placeholder="请输入排序"
          :disabled="disabled"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button
          v-if="!disabled"
          type="primary"
          :loading="loading"
          @click="onSubmit"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { post, put } from '@/utils/http/request'

const rules = {
  code: [{ required: true, message: '请输入类型key', trigger: 'blur' }],
  name: [{ required: true, message: '请输入类型名称', trigger: 'blur' }],
  type: [{ required: true, message: '请输入类型', trigger: 'change' }],
  sort: [{ required: true, message: '请输入排序', trigger: 'blur' }]
}

export default {
  name: 'ObjectTypeModal',
  components: {},
  data() {
    return {
      rules: rules,
      visible: false,
      loading: false,
      title: '',
      disabled: false,
      primaryKey: 'code',
      form: {
        code: null,
        name: null,
        type: null,
        sort: null
      },
      urls: {
        save: '/voucher/s/obj/type',
        update: id => `/voucher/s/obj/type/${id}`
      }
    }
  },
  methods: {
    // 新增
    add() {
      this.visible = true
      this.disabled = false
      this.title = '新增'
      this.form = {
        code: null,
        name: null,
        type: null,
        sort: null
      }
    },

    // 编辑
    edit(record, title) {
      this.visible = true
      this.disabled = false
      this.title = title || '编辑'
      this.form = { ...record }
    },

    // 提交
    async onSubmit() {
      try {
        await this.$refs.modalForm.validate()
        this.loading = true

        let result
        if (this.form[this.primaryKey] && this.title === '编辑') {
          // 编辑
          result = await put(
            this.urls.update(this.form[this.primaryKey]),
            this.form
          )
        } else {
          // 新增
          result = await post(this.urls.save, this.form)
        }

        if (result.success) {
          this.$message.success(result.message || '操作成功')
          this.onCancel()
          this.$emit('ok')
        } else {
          this.$message.error(result.message || '操作失败')
        }
      } catch (error) {
        console.error('提交失败:', error)
        if (error.fields) {
          // 表单验证失败
          return
        }
        this.$message.error('操作失败')
      } finally {
        this.loading = false
      }
    },

    // 取消
    onCancel() {
      this.visible = false
      this.loading = false
      this.disabled = false
      this.$refs.modalForm?.resetFields()
      this.$emit('cancel')
    }
  }
}
</script>
