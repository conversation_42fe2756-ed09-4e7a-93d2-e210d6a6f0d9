<template>
  <div class="resource-management h-full">
    <!-- 查询条件 -->
    <div class="search-bar">
      <el-input
        v-model="searchParams.name"
        clearable
        placeholder="资源名称"
        style="width: 200px"
      />
      <el-select
        v-model="searchParams.type"
        clearable
        placeholder="全部类型"
        style="width: 150px"
      >
        <el-option
          v-for="type in resourceTypes"
          :key="type.value"
          :label="type.label"
          :value="type.value"
        />
      </el-select>
      <el-button type="primary" @click="handleSearch">查询</el-button>
      <el-button @click="handleResetSearch">重置</el-button>
    </div>

    <!-- 操作按钮 -->
    <div class="actions-bar">
      <el-button type="success" @click="openAddModal">新增资源</el-button>
    </div>

    <!-- 资源表格 -->
    <div class="table-container">
      <el-table :data="tableData" border row-key="id" style="width: 100%">
        <el-table-column label="资源名称" prop="name" width="400" />
        <el-table-column
          label="资源类型"
          prop="translation.typeStr"
          width="100"
        />
        <el-table-column label="路径/标识" prop="meta.path" />
        <el-table-column label="资源标识" prop="key" />
        <el-table-column label="图标" prop="meta.icon" />
        <el-table-column label="排序" prop="sort" width="70" />
        <el-table-column label="操作" width="250">
          <template #default="scope">
            <el-space>
              <el-link
                type="primary"
                size="small"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-link>
              <el-link
                type="primary"
                size="small"
                @click="handleCopy(scope.row)"
              >
                复制
              </el-link>
              <el-link
                type="primary"
                size="small"
                @click="handleConfigure(scope.row)"
              >
                关联API
              </el-link>
              <el-popconfirm
                class="box-item"
                title="确认删除该资源吗"
                placement="top-start"
                @confirm="handleDelete(scope.row)"
              >
                <template #reference>
                  <el-link type="danger"> 删除 </el-link>
                </template>
              </el-popconfirm>
            </el-space>
          </template>
        </el-table-column>
      </el-table>
      <el-loading v-if="loading" text="加载中..." />
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEditMode ? '编辑资源' : '新增资源'"
      width="900px"
    >
      <el-form ref="resourceFormRef" :model="resourceForm" label-width="100px">
        <!-- 两列布局 -->
        <div class="form-grid">
          <!-- 资源名称 -->
          <el-form-item
            label="资源名称"
            prop="name"
            required
            error="请输入资源名称"
          >
            <el-input
              v-model="resourceForm.name"
              placeholder="请输入资源名称 (JSON 格式)"
            />
          </el-form-item>

          <!-- 资源类型-->
          <el-form-item
            label="资源类型"
            prop="type"
            required
            error="请选择资源类型"
          >
            <el-select v-model="resourceForm.type" placeholder="Select">
              <el-option label="菜单" value="00015" />
              <el-option label="按钮" value="00019" />
            </el-select>
          </el-form-item>

          <!-- 资源标识 -->
          <el-form-item
            label="资源标识"
            prop="key"
            required
            error="请输入资源标识"
          >
            <el-input v-model="resourceForm.key" placeholder="请输入资源标识" />
          </el-form-item>

          <!-- 父级资源 -->
          <el-form-item label="父级资源" prop="parentId">
            <el-cascader
              v-model="resourceForm.parentId"
              :options="tableData"
              :props="cascaderProps"
              class="w-full"
              clearable
            />
          </el-form-item>

          <!-- 菜单图标 -->
          <el-form-item label="资源图标" prop="meta.icon">
            <el-input
              v-model="resourceForm.meta.icon"
              placeholder="请输入图标名称"
            />
          </el-form-item>

          <!-- 访问路径 -->
          <el-form-item
            label="访问路径"
            prop="meta.path"
            required
            error="请输入访问路径"
          >
            <el-input
              v-model="resourceForm.meta.path"
              placeholder="请输入访问路径"
              @change="checkPath"
            />
          </el-form-item>

          <!-- 资源组件 -->
          <el-form-item label="资源组件" prop="meta.component">
            <el-input
              v-model="resourceForm.meta.component"
              placeholder="请输入资源组件路径"
            />
          </el-form-item>

          <!-- 重定向路径 -->
          <el-form-item label="重定向路径" prop="meta.redirect">
            <el-input
              v-model="resourceForm.meta.redirect"
              placeholder="请输入重定向路径"
            />
          </el-form-item>

          <!-- 资源排序 -->
          <el-form-item label="资源排序" prop="sort">
            <el-input-number
              v-model="resourceForm.sort"
              :max="10000"
              :min="1"
              placeholder="请输入资源排序"
            />
          </el-form-item>

          <!-- 是否为游客资源 -->
          <el-form-item label="游客资源" prop="visit">
            <el-switch v-model="resourceForm.visit" />
          </el-form-item>

          <!-- 是否启用 -->
          <el-form-item label="是否启用" prop="enable">
            <el-switch v-model="resourceForm.enable" />
          </el-form-item>

          <!-- 是否可见 -->
          <el-form-item label="是否隐藏" prop="hidden">
            <el-switch v-model="resourceForm.hidden" />
          </el-form-item>
          <el-form-item label="铺满内容页" prop="meta.fullPage">
            <el-switch
              v-model="resourceForm.meta.fullPage"
              active-value="1"
              inactive-value="0"
            />
          </el-form-item>
          <el-form-item label="新标签页" prop="meta.blank">
            <el-switch
              v-model="resourceForm.meta.blank"
              active-value="1"
              inactive-value="0"
            />
          </el-form-item>
          <el-form-item label="外链" prop="meta.externalLink">
            <el-switch
              v-model="resourceForm.meta.externalLink"
              active-value="1"
              inactive-value="0"
            />
          </el-form-item>
        </div>

        <!-- 自定义字段 -->
        <el-divider>自定义字段</el-divider>
        <div
          v-for="(field, index) in resourceForm.customFields"
          :key="index"
          style="display: flex"
        >
          <el-row :gutter="20">
            <el-col :span="7">
              <el-form-item label="字段名称">
                <el-input v-model="field.name" placeholder="字段名称" />
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="字段键">
                <el-input v-model="field.key" placeholder="字段键" />
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="字段值">
                <el-input v-model="field.value" placeholder="字段值" />
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-button
                size="small"
                style="margin-bottom: 10px"
                type="danger"
                @click="removeCustomField(index)"
              >
                删除字段
              </el-button>
            </el-col>
          </el-row>
        </div>
        <el-button size="small" type="primary" @click="addCustomField">
          添加自定义字段
        </el-button>
      </el-form>

      <template #footer>
        <el-button @click="closeModal">取消</el-button>
        <el-button type="primary" @click="checkFormAndSubmit">
          {{ isEditMode ? '保存' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <field-config-modal
      :show="configureShow"
      :model="configure"
      @ok="() => (configureShow = false)"
      @cancel="() => (configureShow = false)"
    />
  </div>
</template>

<script setup>
import { onMounted, reactive, ref, toRaw } from 'vue'
import {
  ElButton,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElLoading,
  ElMessage,
  ElOption,
  ElSelect,
  ElTable,
  ElTableColumn
} from 'element-plus'
import { del, get, post, put } from '@/utils/http/request'
import FieldConfigModal from '@v/companyAuth/modules/FieldConfigModal.vue'
import { isUrl } from '@pureadmin/utils'

// --- 响应式状态 ---
const searchParams = reactive({
  name: '',
  type: '' // '' 表示全部类型
})

const tableData = ref([])
const loading = ref(false)
const configureShow = ref(false)
const configure = ref({
  id: ''
})
const resourceFormRef = ref()

const resourceTypes = ref([
  { value: 'MENU', label: '菜单' },
  { value: 'BUTTON', label: '按钮' },
  { value: 'API', label: '接口' }
])

const dialogVisible = ref(false)
const isEditMode = ref(false)
const parentPaths = {}

// 自定义字段逻辑
const addCustomField = () => {
  resourceForm.customFields.push({ name: '', key: '', value: '' })
}

const removeCustomField = index => {
  resourceForm.customFields.splice(index, 1)
}

const cascaderProps = {
  label: 'name',
  value: 'id',
  checkStrictly: true
}

// 初始化表单数据
const defaultResourceForm = {
  id: null,
  name: '{"zh":"示例","en":"example"}', // JSON 格式的多语言资源名称
  key: '',
  type: '', // 资源类型
  parentId: null,
  sort: 0,
  visit: false,
  enable: true,
  hidden: false,
  remark: null,
  meta: {
    path: '',
    component: '',
    icon: '',
    redirect: '',
    fullPage: false,
    blank: false,
    externalLink: false
  },
  customFields: [] // 自定义字段
}
const resourceForm = reactive({ ...defaultResourceForm })

// --- 方法 ---
const loadTableData = async () => {
  const params = {
    ...searchParams
  }
  get('voucher/s/res/list', params).then(response => {
    if (response && response.success) {
      const data = response.data
      initTableData(data)
      tableData.value = data
      console.log('获取到资源:', tableData.value)
    } else {
      console.error('获取资源错误:', response.message)
      tableData.value = []
    }
  })
}

const initTableData = (data, path = []) => {
  data.forEach(item => {
    const meta = {}
    item.attributes.forEach(attr => (meta[attr.key] = attr.value))
    item.meta = meta
    if (item.children && item.children.length > 0) {
      initTableData(item.children, [...path, item.id])
    }

    parentPaths[item.id] = path
  })
}

const handleSearch = () => {
  loadTableData()
}

const handleResetSearch = () => {
  searchParams.name = ''
  searchParams.type = ''
  loadTableData()
}

const resetForm = () => {
  Object.assign(resourceForm, defaultResourceForm)
  resourceForm.id = null // 确保 id 也被重置
}

const openAddModal = () => {
  isEditMode.value = false
  resetForm()
  dialogVisible.value = true
}

const closeModal = () => {
  dialogVisible.value = false
  resetForm()
}

const checkPath = newVal => {
  if (newVal) {
    if (isUrl(newVal)) {
      resourceForm.meta.externalLink = '1'
    } else {
      resourceForm.meta.externalLink = '0'
      if (!newVal.startsWith('/')) {
        resourceForm.meta.path = '/' + newVal
      }
    }
  }
}

const checkFormAndSubmit = () => {
  resourceFormRef.value.validate(valid => {
    if (valid) {
      handleSubmit()
    }
  })
}

const handleSubmit = () => {
  const parentId =
    typeof resourceForm.parentId === 'string'
      ? resourceForm.parentId
      : resourceForm.parentId?.[resourceForm.parentId.length - 1]

  const formData = {
    ...resourceForm,
    parentId,
    attributes: Object.entries(resourceForm.meta).map(([key, value]) => ({
      key,
      value
    }))
  }

  delete formData.meta

  console.log('Submitting resource:', formData, resourceForm)

  if (isEditMode.value) {
    // 编辑逻辑
    put(`voucher/s/res/${formData.id}`, formData).then(response => {
      if (response && response.success) {
        ElMessage({
          message: '修改成功',
          type: 'success'
        })

        closeModal()
        loadTableData() // 重新加载数据以显示更改
      } else {
        ElMessage({
          message: response?.msg || '修改失败',
          type: 'error'
        })
      }
    })
  } else {
    // 新增逻辑
    post('voucher/s/res', formData).then(response => {
      if (response && response.success) {
        ElMessage('创建成功')

        closeModal()
        loadTableData() // 重新加载数据以显示更改
      } else {
        ElMessage({
          message: response?.msg || '创建失败',
          type: 'error'
        })
      }
    })
  }
}

const handleCopy = row => {
  isEditMode.value = false
  Object.assign(resourceForm, toRaw(row))
  resourceForm.parentId = parentPaths[row.id] || []
  resourceForm.id = null
  dialogVisible.value = true
}

const handleEdit = row => {
  isEditMode.value = true
  Object.assign(resourceForm, toRaw(row))
  resourceForm.parentId = parentPaths[row.id] || []
  dialogVisible.value = true
}

const handleConfigure = row => {
  configure.value.id = row.id
  configureShow.value = true
}

const handleDelete = row => {
  console.log('handleDelete', row)
  del(`voucher/s/res/${row.id}`).then(res => {
    if (res && res.success) {
      ElMessage({
        message: '删除成功',
        type: 'success'
      })
      loadTableData()
    } else {
      ElMessage({
        message: res?.msg || '删除失败',
        type: 'error'
      })
    }
  })
  /*if (confirm(`确定要删除资源 "${row.name}" 吗?`)) {
    // 模拟删除
    allMockResources.value = allMockResources.value.filter(r => r.id !== row.id)
    console.log('Resource deleted, ID:', row.id)
    loadTableData() // 重新加载数据
  }*/
}

// --- 生命周期钩子 ---
onMounted(() => {
  loadTableData()
})
</script>

<style scoped>
.resource-management {
  margin: 0 auto;
  font-family: sans-serif;
}

.search-bar,
.actions-bar {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 20px;
}

.search-bar input[type='text'],
.search-bar select {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

button {
  padding: 8px 15px;
  color: white;
  cursor: pointer;
  background-color: #007bff;
  border: none;
  border-radius: 4px;
  transition: background-color 0.2s;
}

button:hover {
  background-color: #0056b3;
}

button:disabled {
  cursor: not-allowed;
  background-color: #ccc;
}

.search-bar button:nth-of-type(2) {
  /* 重置按钮 */
  background-color: #6c757d;
}

.search-bar button:nth-of-type(2):hover {
  background-color: #545b62;
}

.table-container {
  margin-bottom: 20px;
  overflow-x: auto; /* For smaller screens */
}

table {
  width: 100%;
  border-collapse: collapse;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

th,
td {
  padding: 10px;
  text-align: left;
  border: 1px solid #ddd;
}

th {
  font-weight: bold;
  background-color: #f8f9fa;
}

tr:nth-child(even) {
  background-color: #f2f2f2;
}

td button {
  padding: 5px 8px;
  margin-right: 5px;
  font-size: 0.9em;
}

td button:last-child {
  margin-right: 0;
}

td button:nth-of-type(2) {
  /* 删除按钮 */
  background-color: #dc3545;
}

td button:nth-of-type(2):hover {
  background-color: #c82333;
}

.loading-placeholder,
.empty-placeholder {
  padding: 20px;
  color: #666;
  text-align: center;
  border: 1px dashed #ccc;
  border-radius: 4px;
}

.pagination {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: flex-end; /* 右对齐 */
  margin-top: 20px;
}

.pagination select {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: rgb(0 0 0 / 50%);
}

.modal-content {
  width: 90%;
  padding: 25px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgb(0 0 0 / 20%);
}

.modal-content h3 {
  padding-bottom: 10px;
  margin-top: 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input[type='text'],
.form-group input[type='number'],
.form-group select {
  box-sizing: border-box;
  width: calc(100% - 16px); /* Account for padding */
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.form-actions {
  margin-top: 20px;
  text-align: right;
}

.form-actions button {
  margin-left: 10px;
}

.form-actions button[type='button'] {
  /* 取消按钮 */
  background-color: #6c757d;
}

.form-actions button[type='button']:hover {
  background-color: #545b62;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}
</style>
