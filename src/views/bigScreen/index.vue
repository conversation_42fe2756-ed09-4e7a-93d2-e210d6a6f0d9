<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import Company3dPage from '@v/bigScreen/components/Company3dPage.vue'
import FloorPlanPage from '@v/bigScreen/components/FloorPlanPage.vue'
import FloorFireFacilities from '@v/bigScreen/components/FloorFireFacilities.vue'
import FloorFireDevice from '@v/bigScreen/components/FloorFireDevice.vue'
import VideoPage from '@v/bigScreen/components/VideoPage.vue'
import CompanySelect from '@c/select/CompanySelect.vue'
import BuildSelect from '@c/select/BuildSelect.vue'
import FloorSelect from '@c/select/FloorSelect.vue'
import { isAllEmpty, useGlobal } from '@pureadmin/utils'
import userStore from '@s/modules/UserStore'
const currentCompany: any = ref('')
const currentBuild = ref('')
const floorId = ref('')

const router = useRouter()
const toHomePage = () => {
  router.push({
    path: '/'
  })
}

const currentPage = ref('1')
const navList = [
  { label: '建筑3D平面图', value: '1' },
  { label: '楼层平面图', value: '2' },
  { label: '建筑内部消防设施分布', value: '3' },
  { label: '建筑内部物联网设备分布', value: '4' },
  { label: '控制室人员在岗监控', value: '5' }
]
const handlePageBtnClick = (item: string) => {
  currentPage.value = item
  // currentBuild.value = ''
  // floorId.value = ''
}

const handleBuildClick = (item: any) => {
  console.log('handleBuildClick', item)
  currentBuild.value = item.data.buildId
  currentPage.value = '2'
}
const user = userStore()
/** 昵称（如果昵称为空则显示用户名） */
const username = computed(() => {
  return isAllEmpty(user.extend?.nickname)
    ? user.userInfo?.fullName
    : user.extend?.nickname
})
const companySelect = ref()

watch(
  () => currentBuild.value,
  newVal => {
    console.log('currentBuild changed', newVal)
  }
)
const handleCompanyChange = e => {
  console.log('handleCompanyChange', e)
  // currentCompany.value = e
  currentBuild.value = ''
  floorId.value = ''
}
const buildCompent = ref()
const handleBuildChange = e => {
  console.log('handleBuildChange', e)
  buildCompent.value && buildCompent.value?.choseNode({ buildId: e })
  // currentBuild.value = e
  floorId.value = ''
}
</script>

<template>
  <div class="flex flex-col h-full">
    <!-- 顶部头部导航 -->
    <div
      class="header-nav w-full h-[70px] flex items-center px-10 bg-white border-b border-blue-300 relative z-10"
    >
      <div
        class="header-title text-3xl font-bold text-blue-900 tracking-widest select-none pointer-events-none"
      >
        建筑消防可视化
      </div>
      <div
        class="flex items-center space-x-6 text-base text-gray-700 ml-auto header-right"
      >
        <span
          >当前公司：<span class="text-blue-600 cursor-pointer">{{
            companySelect?.companyName || '请选择公司'
          }}</span></span
        >
        <span
          >当前用户：<span class="text-blue-600 cursor-pointer">{{
            username
          }}</span></span
        >
        <!-- <span class="cursor-pointer hover:underline text-gray-600"
          >退出登录</span
        > -->
      </div>
    </div>
    <!-- 导航栏 -->
    <div class="nav-bar w-full h-[60px] bg-[#0a1e3a] flex items-center px-10">
      <div class="flex space-x-12 justify-center nav-content">
        <div
          v-for="item in navList"
          :key="item.value"
          class="relative flex items-center h-full cursor-pointer text-lg"
          :class="
            currentPage === item.value
              ? 'text-cyan-300 font-bold'
              : 'text-white'
          "
          @click="handlePageBtnClick(item.value)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>

    <!-- 内容区 -->
    <div class="w-full flex-1 relative content">
      <div class="selectList">
        <div>
          <company-select
            ref="companySelect"
            v-model="currentCompany"
            @change="handleCompanyChange"
          />
        </div>
        <div v-if="currentCompany">
          <build-select
            v-model="currentBuild"
            :company-id="currentCompany"
            @change="handleBuildChange"
          />
        </div>
        <div v-if="currentCompany && currentBuild">
          <floor-select
            v-model="floorId"
            :build-id="currentBuild"
            style="width: 200px"
          />
        </div>
      </div>
      <el-empty v-if="!currentCompany" description="选择建筑管理单位" />
      <company3d-page
        v-else-if="currentPage === '1'"
        ref="buildCompent"
        v-model:build-id="currentBuild"
        :company-id="currentCompany"
        @on-build-click="handleBuildClick"
      />
      <floor-plan-page
        v-else-if="
          currentPage === '2' && currentCompany && currentBuild && floorId
        "
        :build-id="currentBuild"
        :floor-id="floorId"
      />
      <floor-fire-facilities
        v-else-if="
          currentPage === '3' && currentCompany && currentBuild && floorId
        "
        :floor-id="floorId"
        :build-id="currentBuild"
      />
      <floor-fire-device v-else-if="currentPage === '4'" :floor-id="floorId" />
      <video-page
        v-else-if="
          currentPage === '5' && currentCompany && currentBuild && floorId
        "
        :company-id="currentCompany"
        :build-id="currentBuild"
        :floor-id="floorId"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.header-nav {
  position: relative;
  min-width: 1200px;
  padding: 10px;
  text-align: center;
  background-image: url('@/assets/bigScreen/header-bg.png');
  background-size: cover;
  box-shadow: 0 2px 8px 0 rgb(0 0 0 / 3%);
}

.header-title {
  margin: auto;
  font-family: 'Alimama ShuHeiTi', Arial, sans-serif;
  font-size: 32px;
  font-style: normal;
  font-weight: 700;
  color: #002e73;
  text-align: left;
  text-transform: none;
  letter-spacing: 4px;
  pointer-events: none;
  user-select: none;
}

.header-right {
  position: absolute;
  top: 50%;
  right: 10px;
  font-family: 'Source Han Sans', Arial, sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  color: #1a1a1a;
  text-align: left;
  text-transform: none;
  transform: translateY(-50%);
}

.nav-bar {
  min-width: 1200px;
  padding: 15px;
  border-bottom: 2px solid #1e3a5c;
}

.nav-bar .font-bold {
  position: relative;
}

.nav-bar .font-bold::after {
  position: absolute;
  bottom: -15px;
  left: 50%;
  display: block;
  width: 64px;
  height: 4px;
  content: '';
  background: #22d3ee;
  border-radius: 2px;
  transform: translateX(-50%);
}

.nav-content {
  margin: auto;
}

.base-div {
  width: 100%;
}

.selectList {
  position: absolute;
  top: 43px;
  left: 43px;
  z-index: 10;
  display: flex;

  div {
    margin-right: 10px;
  }
}

.content {
  padding: 33px;
  background-color: #f5f7fa;
}

.el-empty {
  background-color: #fff !important;
}
</style>
