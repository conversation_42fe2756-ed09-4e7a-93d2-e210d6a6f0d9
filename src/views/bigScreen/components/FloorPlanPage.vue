<script setup lang="ts">
import FloorSelect from '@c/select/FloorSelect.vue'
import { onMounted, ref, watch } from 'vue'
import { get } from '@/utils/http/request'
import URLS from '@api/URLS'
import { ElMessage } from 'element-plus'

const props = defineProps({
  buildId: {
    type: String,
    required: true
  },
  floorId: {
    type: String,
    default: ''
  }
})
const handleFloorChange = floorId => {
  console.log(floorId)
  getBuildImage()
}
const imageType = ref('1')
const imageUrl = ref('')
const getBuildImage = async () => {
  let businessType
  let businessId
  if (imageType.value === '1' && !props.floorId) {
    businessType = 'standardFloorFirePlanImage'
    businessId = props.buildId
  } else if (imageType.value === '2' && !props.floorId) {
    businessType = 'emergencyEscapePlanImage'
    businessId = props.buildId
  } else if (imageType.value === '1' && props.floorId) {
    businessType = 'floorLayoutImage'
    businessId = props.floorId
  } else if (imageType.value === '2' && props.floorId) {
    businessType = 'floorEmergencyEscapeMap'
    businessId = props.floorId
  }
  get(URLS.MANAGER_FILE + '/fileResource/record/list', {
    businessType: businessType,
    businessId: businessId
  }).then(res => {
    if (res && res.success) {
      if (res.data && res.data.length > 0) {
        imageUrl.value = URLS.imgFileUrl(res.data[0].id)
      } else {
        imageUrl.value = ''
      }
    } else {
      ElMessage({
        message: res.message,
        type: 'error'
      })
    }
  })
}

onMounted(() => getBuildImage())
watch(
  () => props.floorId,
  () => getBuildImage()
)
watch(
  () => props.buildId,
  () => {
    getBuildImage()
  }
)
watch(
  () => imageType.value,
  () => {
    getBuildImage()
  }
)
</script>

<template>
  <div class="flex flex-col w-full h-full">
    <div
      class="flex-1 w-full"
      style="padding-top: 50px; background-color: #fff"
    >
      <el-empty v-if="!imageUrl" description="请先上传相关图片" />
      <img v-else :src="imageUrl" />
      <div class="btn-img">
        <img
          v-if="imageType === '2'"
          src="@/assets/bigScreen/escape.png"
          alt=""
          width="120px"
          @click="imageType = '1'"
        />
        <img
          v-if="imageType === '1'"
          src="@/assets/bigScreen/closeescape.png"
          alt=""
          width="120px"
          @click="imageType = '2'"
        />
      </div>
    </div>
    <!-- <el-empty v-else description="请选择建筑" /> -->
  </div>
</template>
<style lang="scss" scoped>
.btn-img {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    margin-bottom: 10px;
    cursor: pointer;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.1);
    }
  }
}

.el-empty {
  background-color: #fff !important;
}
</style>
