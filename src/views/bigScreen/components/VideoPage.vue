<script setup lang="ts">
import <PERSON><PERSON>uca from '@c/player/jessibuca.vue'
import { onMounted, ref, watch } from 'vue'
import { get } from '@/utils/http/request'
const props = defineProps({
  companyId: {
    type: String,
    required: true
  },
  buildId: {
    type: String,
    default: ''
  },
  floorId: {
    type: String,
    default: ''
  }
})
// const playUrl = ref(
//   'ws://***************:7999/rtp/99154970001110000000_99154970001310000001.live.flv'
// )
const videoList = ref([])
const playData: any = ref({})
const videoDetail: any = ref({})
const getvodioList = async () => {
  if (!props.companyId || !props.buildId || !props.floorId) {
    return
  }
  const res = await get('/build/build/floor/device/list', {
    companyId: props.companyId,
    buildId: props.buildId,
    buildFloorId: props.floorId,
    deviceTypeCodes: 'video'
  })
  if (res && res.success) {
    playData.value = res.data[0]
    videoList.value = res.data
    getVidioDetial()
  }
}
const getVidioDetial = async () => {
  const outNo = playData.value.attrs.find(
    item => item.attrCode === 'outNo'
  )?.attrValue
  const channelId = playData.value.attrs.find(
    item => item.attrCode === 'channelId'
  )?.attrValue
  const res = await get('/build/device/video/play', {
    outNo,
    channelId
  })
  if (res && res.success) {
    videoDetail.value = res.data
  }
}

watch(
  () => props.buildId,
  () => {
    getvodioList()
  }
)
watch(
  () => props.companyId,
  () => {
    getvodioList()
  }
)
watch(
  () => props.floorId,
  () => {
    getvodioList()
  }
)
const handleVideoChange = item => {
  playData.value = item
  getVidioDetial()
}

onMounted(() => {
  getvodioList()
})
</script>

<template>
  <div class="flex h-full cont">
    <div v-show="videoList.length" class="w-[200px]">
      <div class="menu">
        <div
          v-for="(item, index) in videoList"
          :key="index"
          class="menu-item"
          :class="{ checked: playData.id === item.id }"
          @click="handleVideoChange(item)"
        >
          {{ item.deviceName }}
        </div>
      </div>
    </div>
    <div v-show="videoDetail.src && videoList.length" class="flex-1">
      <jessibuca
        :video-url="videoDetail.src || ''"
        :hasAudio="true"
        autoplay
        live
        fluent
      />
    </div>
    <el-empty v-show="!videoList.length" description="暂无数据" />
  </div>
</template>
<style lang="scss" scoped>
.cont {
  padding-top: 60px;
}

.menu {
  width: 100%;
  height: 100%;
  padding: 10px;
  background-color: #fff;

  .menu-item {
    padding: 12px;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 400;
    color: #0256ff;
    cursor: pointer;
    background: #e0eaff;
    border-bottom: 1px solid #eee;
    border-radius: 6px;
  }

  .menu-item:hover {
    color: #fff;
    background: #0256ff;
  }

  .checked {
    font-size: 14px;
    font-weight: 400;
    color: #fff;
    background: #0256ff;
  }
}

.el-empty {
  width: 100%;
}
</style>
