<script setup lang="ts">
import AntvX6Composition from '@/composition/AntvX6Composition'
import { onMounted, ref, Ref, watch, defineModel } from 'vue'
import { NodeType } from '@/types/editor/EditorTypes'
import EditorDataComposition from '@/composition/EditorDataComposition'

const props = defineProps({
  companyId: {
    type: String,
    required: true
  }
})
const buildId = defineModel('buildId', {
  type: String,
  default: ''
})
const emits = defineEmits(['onBuildClick'])

const handleNodeClick = nodeData => {
  console.log('1111111', currentNode.value)
  if (nodeData.id === 'buildNum') {
    emits('onBuildClick', currentNode.value)
    choseNode(nodeData)
  }
}
const choseNode = nodeData => {
  const allNodes = getAllNodes()
  console.log(nodeData, 'handleBuildChange', allNodes)
  allNodes.map(item => {
    if (item.data.id === 'buildNum') {
      console.log(item.data.buildId, nodeData.buildId)
      item.attr({
        image: {
          'xlink:href':
            item.data.buildId === nodeData.buildId
              ? item.data.checkImage
              : item.data.image
        }
      })
    }
  })
}

const { init, fromJson, initBackgroundNode, currentNode, getAllNodes } =
  AntvX6Composition(handleNodeClick, true)
const { loadConfig } = EditorDataComposition(initBackgroundNode, fromJson)
onMounted(() => {
  init(() => {
    loadConfig(props.companyId, '1', 'companyLayoutImage', () => {
      console.log(buildId.value, 'currentBuild')
      buildId.value && choseNode({ buildId: buildId.value })
    })
  })
})
watch(
  () => props.companyId,
  () => {
    loadConfig(props.companyId, '1', 'companyLayoutImage', () => {
      console.log(buildId.value, 'currentBuild')
      buildId.value && choseNode({ buildId: buildId.value })
    })
  }
)
defineExpose({
  choseNode
})
</script>

<template>
  <div id="graph-container" class="w-full h-full" />
</template>
<style lang="scss" scoped>
.el-empty {
  background-color: #fff !important;
}
</style>
