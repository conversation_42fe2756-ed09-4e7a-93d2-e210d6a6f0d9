<script setup lang="ts">
import FloorSelect from '@c/select/FloorSelect.vue'
import { onMounted, Ref, ref, watch } from 'vue'
import AntvX6Composition from '@/composition/AntvX6Composition'
import EditorDataComposition from '@/composition/EditorDataComposition'
import { NodeType } from '@/types/editor/EditorTypes'
import { get, post, put } from '@/utils/http/request'
import URLS from '@/api/URLS'

const props = defineProps({
  floorId: {
    type: String,
    required: true
  }
})
const handleFloorChange = floorId => {
  console.log(floorId)
}
const handleNodeClick = nodeData => {
  console.log('1111111', nodeData)
}
const { init, fromJson, initBackgroundNode } = AntvX6Composition(
  handleNodeClick,
  true
)
const { loadConfig } = EditorDataComposition(initBackgroundNode, fromJson)
const currentCell = ref(null)
const showtips = ref(false)
const styleTips = ref({})
const loadding = ref(false)

onMounted(() => {
  initDeviceTypes()
  init(graph => {
    if (props.floorId) {
      loadConfig(props.floorId, '4', 'floorLayoutImage')
    }
    graph.on('cell:mouseenter', async ({ e, cell, view }) => {
      if (showtips.value == false && cell.getData().type !== 'background') {
        styleTips.value = {
          left: `${e.clientX + 10}px`,
          top: `${e.clientY - 130 + 10}px`
        }
        console.log('getData', cell.getData())
        const cellData = cell.getData()
        showtips.value = true
        const deviceinfo = await getDeviceData(cell.getData())
        console.log('deviceinfo', deviceinfo)
        if (deviceinfo?.data) {
          currentOption.value = tipconfig[cellData.deviceType].map(item => {
            return {
              ...item,
              value: deviceinfo?.data[item.key] || ''
            }
          })
        }
        currentCell.value = cell
      }
    })
    graph.on('cell:mouseleave', ({ e, cell, view }) => {
      showtips.value = false
      currentCell.value = null
    })
  })
})
const getDeviceData = async data => {
  console.log('getDeviceData', data)
  if (loadding.value) {
    return
  }
  loadding.value = true
  const res = await get(
    URLS.BUILD + `/device/getByDeviceNo` + `?deviceNo=${data.deviceCode}`
  )
  loadding.value = false
  return res
}
watch(
  () => props.floorId,
  () => {
    loadConfig(props.floorId, '4', 'floorLayoutImage')
  }
)

const deviceTypeListArr: Ref<any[]> = ref([])
const tipconfig = {
  combustibleGas: [
    {
      label: '名称',
      value: '',
      key: 'name'
    },
    {
      label: '地址',
      value: '',
      key: 'position'
    },
    {
      label: '状态',
      value: '',
      key: 'lastStatusName'
    },
    {
      label: '告警',
      value: '',
      key: 'alarmContent'
    },
    {
      label: '气体浓度',
      value: '',
      key: 'concentration'
    }
  ],
  video: [
    {
      label: '名称',
      value: '',
      key: 'name'
    },
    {
      label: '地址',
      value: '',
      key: 'position'
    },
    {
      label: '告警',
      value: '',
      key: 'alarmContent'
    }
  ],
  smokeDetector: [
    {
      label: '名称',
      value: '',
      key: 'name'
    },
    {
      label: '地址',
      value: '',
      key: 'position'
    },
    {
      label: '信号强度',
      value: '',
      key: 'signal'
    },
    {
      label: '电池电量',
      value: '',
      key: 'batMountPercent'
    },
    {
      label: '烟雾浓度',
      value: '',
      key: 'concentration'
    },
    {
      label: '告警',
      value: '',
      key: 'alarmContent'
    }
  ],
  transmissionDevice: [
    {
      label: '名称',
      value: '',
      key: 'name'
    },
    {
      label: '地址',
      value: '',
      key: 'position'
    },
    {
      label: '水位',
      value: '',
      key: 'waterLevel'
    },
    {
      label: '电池电量',
      value: '',
      key: 'batMountPercent'
    },
    {
      label: '信号强度',
      value: '',
      key: 'signal'
    }
  ],
  waterPipe: [
    {
      label: '名称',
      value: '',
      key: 'name'
    },
    {
      label: '地址',
      value: '',
      key: 'position'
    },
    {
      label: '水位',
      value: '',
      key: 'waterLevel'
    },
    {
      label: '电池电量',
      value: '',
      key: 'batMountPercent'
    },
    {
      label: '信号强度',
      value: '',
      key: 'signal'
    },
    {
      label: '告警',
      value: '',
      key: 'alarmContent'
    }
  ],
  electricalFire: [
    {
      label: '名称',
      value: '',
      key: 'name'
    },
    {
      label: '地址',
      value: '',
      key: 'position'
    },
    {
      label: '告警',
      value: '',
      key: 'alarmContent'
    },
    {
      label: '温度',
      value: '',
      key: 'tempMountB'
    },
    {
      label: '漏电',
      value: '',
      key: 'leftMountA'
    },
    {
      label: '电压',
      value: '',
      key: 'batMountA'
    },
    {
      label: '电流',
      value: '',
      key: 'currentMountC'
    }
  ]
}
const currentOption = ref([])
/**
 * 获取设备类型列表
 */
const initDeviceTypes = () => {
  get(URLS.BUILD + '/build/device-type/all').then(res => {
    if (res && res.success) {
      deviceTypeListArr.value = res.data
    }
  })
}
</script>

<template>
  <div class="flex flex-col w-full h-full">
    <div id="graph-container" class="flex-1 w-full relative" />
    <div v-if="showtips" class="tips" :style="styleTips">
      <div v-if="!loadding">
        <div v-for="(item, index) in currentOption" :key="index">
          <span class="tips-label">{{ item.label }}：</span>
          <span class="tips-content">{{ item.value }}</span>
        </div>
      </div>
      <div v-else style="color: #fff">加载中...</div>
    </div>
    <div class="tipsList">
      <div
        v-for="(item, index) in deviceTypeListArr"
        :key="index"
        class="tipsList-item flex justify-center items-center flex-col"
      >
        <div class="img">
          <img
            :src="URLS.imgFileUrl(item?.deviceTypeLogo[0].id)"
            alt=""
            srcset=""
          />
        </div>
        <span class="text">{{ item.deviceTypeName }}</span>
      </div>
    </div>
    <!-- <el-empty v-show="!floorId" description="请选择建筑管理楼层" /> -->
  </div>
</template>
<style lang="scss" scoped>
.tips {
  position: absolute;
  width: 278px;
  padding: 13px 20px;
  background: rgb(0 17 46 / 78%);
  border-radius: 6px;

  .tips-label {
    font-family: OPPOSans, sans-serif;
    font-size: 14px;
    font-weight: 400;
    color: #fff;
  }

  .tips-content {
    font-family: OPPOSans, sans-serif;
    font-size: 14px;
    font-weight: 400;
    color: #9effff;
  }
}

.el-empty {
  background-color: #fff !important;
}

.tipsList {
  position: absolute;
  top: 43px;
  right: 43px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 40px;
  height: calc(100% - 90px);
  padding: 12px;
  overflow-y: auto;
  font-family: OPPOSans, sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #fff;
  background-color: rgb(0 0 0 / 35%);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);

  .img {
    img {
      width: 40px;
    }
  }
}
</style>
