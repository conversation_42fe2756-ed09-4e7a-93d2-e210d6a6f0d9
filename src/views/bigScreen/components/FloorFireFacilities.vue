<script setup lang="ts">
import FloorSelect from '@c/select/FloorSelect.vue'
import { onMounted, Ref, ref, watch } from 'vue'
import AntvX6Composition from '@/composition/AntvX6Composition'
import EditorDataComposition from '@/composition/EditorDataComposition'
import { NodeType } from '@/types/editor/EditorTypes'
import { ElMessage } from 'element-plus'
import { get, post, put } from '@/utils/http/request'
import URLS from '@/api/URLS'
const props = defineProps({
  floorId: {
    type: String,
    required: true
  }
})
const handleFloorChange = floorId => {
  console.log(floorId)
}
const handleNodeClick = nodeData => {
  console.log('1111111', nodeData)
}
const { init, fromJson, initBackgroundNode } = AntvX6Composition(
  handleNodeClick,
  true
)
const { loadConfig } = EditorDataComposition(initBackgroundNode, fromJson)
const currentCell = ref(null)
onMounted(() => {
  initDeviceTypes()
  init(graph => {
    graph.on('cell:mouseenter', ({ e, cell, view }) => {
      console.log('cell:mouseenter', cell, e)
      if (showtips.value == false && cell.getData().type !== 'background') {
        styleTips.value = {
          left: `${e.clientX + 10}px`,
          top: `${e.clientY - 130 + 10}px`
        }
        showtips.value = true
        currentCell.value = cell
      }
    })
    graph.on('cell:mouseleave', ({ e, cell, view }) => {
      showtips.value = false
      currentCell.value = null
    })
    if (props.floorId) {
      loadConfig(props.floorId, '5', 'floorLayoutImage')
    }
  })
})

watch(
  () => props.floorId,
  () => {
    if (!props.floorId) {
      return
    }
    loadConfig(props.floorId, '5', 'floorLayoutImage')
  }
)
const showtips = ref(false)
const styleTips = ref({})
const deviceTypeListArr = ref([])
/**
 * 获取设备类型列表
 */
const initDeviceTypes = () => {
  get(URLS.BUILD + '/dict/p/005000').then(res => {
    if (res && res.success) {
      deviceTypeListArr.value = res.data.map(item => {
        const config = JSON.parse(item.config || '{}')
        return {
          ...item,
          config: {
            ...config,
            image: URLS.imgFileUrl(config.fileId)
          }
        }
      })
      console.log('设备类型列表', deviceTypeListArr.value)
    }
  })
}
</script>

<template>
  <div class="flex flex-col w-full h-full">
    <div id="graph-container" class="flex-1 w-full relative" />
    <!-- <el-empty v-show="!floorId" description="请选择建筑管理楼层" /> -->
    <div v-if="showtips" class="tips" :style="styleTips">
      <span class="tips-label">设备名称：</span>
      <span class="tips-content">{{ currentCell.data.label }}</span>
    </div>
    <div class="tipsList">
      <div
        v-for="(item, index) in deviceTypeListArr"
        :key="index"
        class="tipsList-item flex justify-center items-center flex-col"
      >
        <div class="img">
          <img :src="item.config.image" alt="" srcset="" />
        </div>
        <span class="text">{{ item.value }}</span>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.tips {
  position: absolute;
  width: 278px;
  padding: 13px 20px;
  background: rgb(0 17 46 / 78%);
  border-radius: 6px;

  .tips-label {
    font-family: OPPOSans, sans-serif;
    font-size: 14px;
    font-weight: 400;
    color: #fff;
  }

  .tips-content {
    font-family: OPPOSans, sans-serif;
    font-size: 14px;
    font-weight: 400;
    color: #9effff;
  }
}

.el-empty {
  background-color: #fff !important;
}

.tipsList {
  position: absolute;
  top: 43px;
  right: 23px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 40px;
  height: calc(100% - 90px);
  padding: 12px;
  overflow: auto;
  font-family: OPPOSans, sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #fff;
  background-color: rgb(0 0 0 / 35%);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);

  .img {
    img {
      width: 40px;
    }
  }
}
</style>
