<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import FormComposition from '@/composition/FormCompostion'
import Editor from '@/components/Editor/index.vue'
import DictSelector from '@c/Dict/dict'
const emits = defineEmits(['ok'])
const rules = reactive({
  articleType: [{ required: true, message: '请输入文章类型', trigger: 'blur' }],
  articleName: [{ required: true, message: '请输入文章名称', trigger: 'blur' }],
  articleContent: [
    { required: true, message: '请输入文章内容', trigger: 'blur' }
  ]
})
const dealRecord = (record: any) => {
  // formData.value = {
  //   articleType: record.articleType,
  //   articleName: record.articleName,
  //   articleContent: record.articleContent
  // }
}
const dealSubmitParams = (data: any) => {
  console.log(data, 'data')
  // return {
  //   articleType: data.articleType,
  //   articleName: data.articleName,
  //   articleContent: data.articleContent
  // }
}
const {
  visible,
  title,
  formData,
  formRef,
  loading,
  handleAdd,
  handleEdit,
  handleSubmit,
  handleClose
} = FormComposition({
  urls: {
    normal: '/build/article',
    detail: '/build/article',
    rowKey: 'id'
  },
  dealRecord,
  dealSubmitParams,
  emits
})
onMounted(() => {})
defineExpose({ handleAdd, handleEdit })
</script>

<template>
  <el-dialog v-model="visible" :title="title" width="1000">
    <el-form
      ref="formRef"
      :model="formData"
      label-width="120px"
      :rules="rules"
      class="check-form"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="文章名称" prop="articleName">
            <el-input
              v-model="formData.articleName"
              placeholder="请输入文章名称"
            />
          </el-form-item>
          <el-form-item label="文章类型" prop="articleType">
            <dict-selector
              v-model="formData.articleType"
              parentKey="010000"
              placeholder="请选择文章类型"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="文章内容" prop="articleContent">
            <Editor v-model="formData.articleContent" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
/* 文章表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

.el-textarea {
  width: 100%;
}
</style>
