<template>
  <div class="mb-3" style="margin-top: 18px">
    <el-form :inline="true" label-width="auto">
      <el-form-item label="任务编号">
        <el-input v-model="queryParams.taskNo" placeholder="请输入任务编号" />
      </el-form-item>
      <el-form-item label="检查结果">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择检查结果"
          style="width: 200px"
        >
          <el-option label="已完成" value="1" />
          <el-option label="未完成" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()"> 查询 </el-button>
        <el-button type="primary" @click="resetTable"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </div>
  <el-table
    ref="tableRef"
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
    class="check-table"
  >
    <el-table-column label="建筑名称" prop="buildName" />
    <el-table-column label="任务编号" prop="taskNo" />
    <el-table-column label="检查人" prop="checkUser" />
    <el-table-column label="检查结果" prop="status">
      <template #default="scope">
        <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
          {{ scope.row.status === 1 ? '已完成' : '未完成' }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="80" align="center">
      <template #default="scope">
        <el-button size="small" @click="handleTaskDetial(scope.row)"
          >详情</el-button
        >
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>

  <el-dialog
    v-model="dialogVisible"
    title="任务详情"
    width="50%"
    :before-close="handleClose"
  >
    <div class="task-detail-container">
      <el-form :model="detail" label-width="120px" class="task-info-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排查建筑">
              <el-input v-model="detail.buildName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务名称">
              <el-input v-model="detail.taskName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="本次排查编号">
              <el-input v-model="detail.taskNo" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务类型">
              <dict-selector
                v-model="detail.checkType"
                parentKey="008000"
                placeholder="请选择检查类别"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查日期">
              <el-input
                disabled
                :value="detail.startCheckDate + ' 到 ' + detail.endCheckDate"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-tag>{{ getStatusText(detail.status) }}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="安全负责人">
              <el-input v-model="detail.safetyManagerUser" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式">
              <el-input v-model="detail.safetyManagerContact" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查人">
              <el-input v-model="detail.checkUser" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div class="checklist-container mt-4">
        <h3 class="checklist-title">检查表</h3>
        <div class="checklist-items border p-3">
          <div
            v-for="(item, index) in detail.checkContent"
            :key="index"
            class="check-item mb-4"
          >
            <div class="item-title font-bold mb-2">
              {{ index + 1 }}. {{ item.itemName }}
              <el-badge
                v-if="item.urgentCount > 0"
                type="danger"
                class="ml-2"
                >{{ item.urgentCount }}</el-badge
              >
            </div>
            <div class="item-options pl-4">
              <div>
                <div
                  v-for="(option, optIndex) in item.options"
                  :key="optIndex"
                  class="checkbox-item mb-1"
                >
                  <el-checkbox
                    v-model="option.checked"
                    :label="option.optionNo"
                    disabled
                  >
                    {{ option.optionDesc }}
                  </el-checkbox>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ElButton, ElTable, ElTableColumn } from 'element-plus'
import { ref, onMounted, watch, nextTick, inject } from 'vue'
import TableComposition from '@/composition/TableComposition'
import { useRouter } from 'vue-router'
import { del, get } from '@/utils/http/request.js'
import DictSelector from '@c/Dict/dict'
const router = useRouter()
const props = defineProps<{
  defaultParams: any
}>()
const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit,
  defaultQueryParams
} = TableComposition({
  urls: {
    list: '/build/self-check/task-record/page',
    normal: '/build/self-check/task-record/page',
    rowKey: 'id'
  }
})

// 搜索
const search = () => {
  console.log('search', queryParams.value)
  loadTableData(1)
}
onMounted(() => {
  Object.assign(defaultQueryParams, {
    buildIds: props.defaultParams?.id
  })
  console.log(props.defaultParams)
  loadTableData(1)
})
const detail = ref<any>()
const handleTaskDetial = async (row: any) => {
  console.log('handleTaskDetial', row)
  const response = await get('/build/self-check/task-record/' + row.id)
  detail.value = response.data
  // 处理选中状态
  detail.value.checkContent.forEach((item: any) => {
    if (item.itemName.includes('单选')) {
      item.selectedOption =
        item.options.find((opt: any) => opt.checked)?.optionNo || ''
    } else if (item.itemName.includes('多选')) {
      item.selectedOptions = item.options
        .filter((opt: any) => opt.checked)
        .map((opt: any) => opt.optionNo)
    }
  })
  dialogVisible.value = true
}
// 状态文本映射
const getStatusText = (status: number): string => {
  const statusMap = {
    0: '未开始',
    1: '已完成',
    2: '进行中',
    3: '已逾期'
  }
  return statusMap[status] || '未知状态'
}
const dialogVisible = ref(false)
const handleClose = () => {
  dialogVisible.value = false
}

const tableRef = ref()
</script>

<style scoped>
.task-detail-container {
  padding: 10px;
}

.task-info-form {
  margin-bottom: 20px;
}

.checklist-title {
  margin-bottom: 10px;
  font-size: 16px;
}

.checklist-items {
  max-height: 400px;
  overflow-y: auto;
  background: #f9f9f9;
}

.check-item {
  padding-bottom: 15px;
  border-bottom: 1px dashed #e5e5e5;
}

.check-item:last-child {
  border-bottom: none;
}

.item-title {
  color: #333;
}

.radio-item,
.checkbox-item {
  margin-left: 10px;
}
</style>
