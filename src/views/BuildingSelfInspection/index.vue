<template>
  <div class="mb-3" style="margin-top: 18px">
    <el-form :inline="true" label-width="auto">
      <el-form-item label="建筑名称/地址">
        <el-input
          v-model="queryParams.nameOrAddress"
          placeholder="请输入建筑名称/地址"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()"> 查询 </el-button>
        <el-button type="primary" @click="resetTable"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </div>
  <el-table
    ref="tableRef"
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
    class="check-table"
  >
    <el-table-column label="建筑名称" prop="name" />
    <el-table-column label="地址" prop="address" />
    <el-table-column label="操作" width="200" align="center">
      <template #default="scope">
        <el-button
          size="small"
          type="primary"
          @click="handleTaskDetial(scope.row)"
          >查看任务</el-button
        >
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>
  <el-dialog
    v-model="dialogVisible"
    title="任务详情"
    width="50%"
    :before-close="handleClose"
  >
    <div>
      <detail-com v-if="dialogVisible" :defaultParams="defaultParams" />
    </div>
  </el-dialog>
</template>
<script setup lang="ts">
import { ElButton, ElTable, ElTableColumn } from 'element-plus'
import { ref, onMounted, watch, nextTick, inject } from 'vue'
import TableComposition from '@/composition/TableComposition'
import { useRouter } from 'vue-router'
import { del, get } from '@/utils/http/request.js'
import DictSelector from '@c/Dict/dict'
import detailCom from './components/detail.vue'
const router = useRouter()
const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit,
  defaultQueryParams
} = TableComposition({
  urls: {
    list: '/build/build',
    normal: '/build/build',
    rowKey: 'id'
  }
})

// 搜索
const search = () => {
  console.log('search', queryParams.value)
  loadTableData(1)
}
onMounted(() => {
  loadTableData(1)
})

const defaultParams = ref<any>({})
const handleTaskDetial = async (row: any) => {
  console.log('handleTaskDetial', row)
  defaultParams.value = row
  dialogVisible.value = true
}

const dialogVisible = ref(false)
const handleClose = () => {
  dialogVisible.value = false
}

const tableRef = ref()
</script>

<style scoped>
.task-detail-container {
  padding: 10px;
}

.task-info-form {
  margin-bottom: 20px;
}

.checklist-title {
  margin-bottom: 10px;
  font-size: 16px;
}

.checklist-items {
  background: #f9f9f9;
}

.check-item {
  padding-bottom: 15px;
  border-bottom: 1px dashed #e5e5e5;
}

.check-item:last-child {
  border-bottom: none;
}

.item-title {
  color: #333;
}

.radio-item,
.checkbox-item {
  margin-left: 10px;
}
</style>
