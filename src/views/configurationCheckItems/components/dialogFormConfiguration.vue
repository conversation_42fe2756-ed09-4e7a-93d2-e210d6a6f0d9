<template>
  <el-dialog v-model="visible" :title="title" width="1000">
    <el-form
      ref="formRef"
      :model="formData"
      label-width="120px"
      class="check-form"
      :rules="rules"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="检查表单名称" prop="checkListName">
            <el-input v-model="formData.checkListName" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="行业" prop="industry">
            <dict-selector
              v-model="formData.industry"
              parentKey="006000"
              placeholder="请选择行业"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检查类别" prop="checkType">
            <dict-selector
              v-model="formData.checkType"
              parentKey="007000"
              placeholder="请选择检查类别"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="mb-2 flex align-center justify-between">
      <div class="title-text">检查项目</div>
      <el-button type="primary" @click="addTableVisible"> 选择 </el-button>
    </div>
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="tableData"
      border
      row-key="id"
      style="width: 100%"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center' }"
      height="400"
      class="check-table"
    >
      <el-table-column
        label="序号"
        width="100"
        align="center"
        :sortable="false"
      >
        <template #default="{ row, $index }">
          <el-input
            v-model="row.sort"
            size="small"
            style="width: 60px; text-align: center"
            @change="onSortChange(row, $index)"
          />
        </template>
      </el-table-column>
      <el-table-column label="自查项目名称" prop="itemName" />
      <el-table-column label="检查要求" prop="checkRequest" />
      <el-table-column label="描述" prop="itemDesc" />
      <el-table-column label="检查指引" prop="checkGuide" />
      <el-table-column label="操作" width="80" align="center">
        <template #default="{ $index }">
          <el-button type="danger" size="small" @click="removeRow($index)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog v-model="tablevisible" title="选择检查项" width="1000">
    <checkItemsTable
      v-if="tablevisible"
      ref="tableRef"
      :tableDataChose="tableData"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="choseSubmit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, watch, ref, provide } from 'vue'
import FormComposition from '@/composition/FormCompostion'
import checkItemsTable from '@/views/configurationCheckItems/components/checkItemsTable.vue'
import DictSelector from '@c/Dict/dict'
const emits = defineEmits(['ok'])
const dealRecord = (record: any) => {
  console.log('dealRecord', record)
  tableData.value = record.checkItems.map((item: any, index: number) => ({
    ...item,
    ...item.dailyCheckItemDetail,
    sort: index + 1 // 确保每个项都有唯一的 sort 值
  }))
}

const rules = reactive({
  checkListName: [
    { required: true, message: '请输入检查表单名称', trigger: 'blur' }
  ],
  industry: [{ required: true, message: '请输入行业', trigger: 'blur' }]
})

const tableRef = ref()
const dealSubmitParams = (data: any) => {
  // 防御性判断，避免 tableRef 未挂载时报错
  const selectedItems = tableData.value
  data.itemList = selectedItems.map((item: any, index) => ({
    checkItemConfigId: item.id,
    sort: index + 1
  }))
  console.log('dealSubmitParams', data)
}
const {
  visible,
  title,
  formData,
  formRef,
  loading,
  handleAdd,
  handleEdit,
  handleSubmit,
  handleClose
} = FormComposition({
  urls: {
    normal: '/build/dailycheck/list-config',
    detail: '/build/dailycheck/list-config',
    rowKey: 'id'
  },
  dealRecord,
  dealSubmitParams,
  emits,
  addInit: () => {
    formData.value.checkItems = []
    tableData.value = []
  }
})

const tablevisible = ref(false)
const tableData = ref([])
const choseSubmit = () => {
  // 获取选中的检查项
  const selectedItems = tableRef.value?.getSelection() || []
  if (selectedItems.length === 0) {
    return
  }
  tableData.value = selectedItems.map((item: any, index: number) => ({
    ...item,
    sort: index + 1 // 确保每个项都有唯一的 sort 值
  }))
  tablevisible.value = false
}

// 修改序号后排序
const onSortChange = (row: any, index: number) => {
  // 先按 sort 升序排序
  tableData.value.sort((a: any, b: any) => a.sort - b.sort)
  // 重新赋值 sort，保证唯一且连续
  tableData.value.forEach((item: any, idx: number) => {
    item.sort = idx + 1
  })
}

// 删除行
const removeRow = (index: number) => {
  tableData.value.splice(index, 1)
  // 删除后重排序号
  tableData.value.forEach((item: any, idx: number) => {
    item.sort = idx + 1
  })
}
const addTableVisible = () => {
  tablevisible.value = true
}
defineExpose({ handleAdd, handleEdit })
</script>

<style lang="scss" scoped>
.option-list {
  margin-top: 8px;
  margin-left: 100px;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.option-label {
  width: 60px;
  padding: 2px 4px;
  margin-right: 6px;
  font-weight: bold;
  text-align: right;
  background: #eee;
  border-radius: 2px;
}

.align-center {
  align-items: center;
}

.title-text {
  margin-right: 20px;
  font-weight: bolder;
}

.check-table {
  .el-table__body-wrapper {
    max-height: 400px; /* 设置最大高度 */
    overflow-y: auto; /* 超出部分滚动 */
  }

  ::v-deep .el-input__inner {
    text-align: center !important;
  }
}
</style>
