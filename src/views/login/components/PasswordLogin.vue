<script lang="ts" setup>
import { reactive, ref } from 'vue'
import ImgCaptcha from '@c/captcha/img/ImgCaptcha.vue'
import { v4 as uuid } from 'uuid'
import useUserStore, { type LoginResult } from '@s/modules/UserStore'
import { type IDataResponse } from '@/utils/http/request'
import md5 from 'md5'
import { useRouter } from 'vue-router'
import { type FormInstance } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { debounce } from '@pureadmin/utils'
import { useEventListener } from '@vueuse/core'

const form = reactive({
  username: null,
  password: null,
  imageCode: null
})

const { t } = useI18n()
const imgDeviceId = ref(uuid())
const userStore = useUserStore()
const router = useRouter()
const loading = ref(false)
const disabled = ref(false)
const loginRef = ref<FormInstance | undefined>(null)
const errorMessage = ref<string | null>(null)

const rules = {}

const emit = defineEmits<{
  loginHandler: [res: Promise<IDataResponse<LoginResult>>]
}>()

const resetDeviceId = () => {
  imgDeviceId.value = uuid()
}

const login = () => {
  errorMessage.value = null

  loginRef.value.validate(value => {
    if (value) {
      const data = {
        ...form,
        password: md5(form.password)
      }

      emit(
        'loginHandler',
        userStore
          .loginPassword(data, imgDeviceId.value)
          .finally(() => resetDeviceId())
      )
    }
  })
}

const immediateDebounce: any = debounce(formRef => login(), 1000, true)

useEventListener(document, 'keydown', ({ code }) => {
  if (
    ['Enter', 'NumpadEnter'].includes(code) &&
    !disabled.value &&
    !loading.value
  )
    immediateDebounce()
})
</script>

<template>
  <el-form
    ref="loginRef"
    :model="form"
    :rules="rules"
    class="mt-2"
    label-width="auto"
  >
    <el-form-item
      :rules="[{ type: 'string', required: true, message: '请填写用户名' }]"
      prop="username"
    >
      <el-input
        v-model="form.username"
        class="h-[46px] inputs-radius"
        size="large"
        placeholder="用户名"
      />
    </el-form-item>
    <el-form-item
      :rules="[{ type: 'string', required: true, message: '请填写密码' }]"
      prop="password"
      class="mt-[30px]"
    >
      <el-input
        v-model="form.password"
        class="h-[46px] inputs-radius"
        size="large"
        placeholder="密码"
        type="password"
      />
    </el-form-item>
    <el-form-item
      :rules="[{ required: true, message: '请填写验证码' }]"
      prop="imageCode"
      class="mt-[30px]"
    >
      <div class="flex justify-between w-full h-full">
        <el-input
          v-model="form.imageCode"
          class="flex-1 captcha-inputs-radius"
          size="large"
          placeholder="请输入验证码"
        />
        <img-captcha
          v-model:deviceId="imgDeviceId"
          class="captcha-img-radius"
          :height="46"
        />
      </div>
    </el-form-item>
    <el-form-item class="mt-[30px]">
      <el-button
        :disabled="disabled"
        :loading="loading"
        class="w-full h-[50px]"
        type="primary"
        size="large"
        round
        @click="login"
      >
        登录
      </el-button>
    </el-form-item>
  </el-form>
</template>

<style lang="scss" scoped>
.inputs-radius {
  *:not(input) {
    border-radius: 154px !important;
  }
}

.captcha-inputs-radius {
  *:not(input) {
    border-radius: 154px 0 0 154px;
  }
}

.captcha-img-radius {
  border-radius: 0 154px 154px 0;

  * {
    border-radius: 0 154px 154px 0;
  }
}
</style>
