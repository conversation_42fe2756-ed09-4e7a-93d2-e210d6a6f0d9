<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import Motion from './utils/motion'
import { useRouter } from 'vue-router'
import { message } from '@/utils/message'
import { reactive, ref, toRaw } from 'vue'
import { debounce } from '@pureadmin/utils'
import { useNav } from '@/layout/hooks/useNav'
import { useEventListener } from '@vueuse/core'
import { ElNotification, FormInstance, TabsPaneContext } from 'element-plus'
import { useLayout } from '@/layout/hooks/useLayout'
import { useUserStoreHook } from '@/store/modules/user'
import { getTopMenu, initRouter } from '@/router/utils'
import { loginTitle, bg, illustration } from './utils/static'
import { useTranslationLang } from '@/layout/hooks/useTranslationLang'
import { useDataThemeChange } from '@/layout/hooks/useDataThemeChange'

import dayIcon from '@/assets/svg/day.svg?component'
import darkIcon from '@/assets/svg/dark.svg?component'
import globalization from '@/assets/svg/globalization.svg?component'
import Check from '~icons/ep/check'
import PasswordLogin from './components/PasswordLogin.vue'
import usePermissionStore from '@s/modules/PermissionStore'
import type { IDataResponse } from '@/utils/http/request'
import type { LoginResult } from '@s/modules/UserStore'

defineOptions({
  name: 'Login'
})

const router = useRouter()
const permissionStore = usePermissionStore()
const loading = ref(false)
const disabled = ref(false)
const ruleFormRef = ref<FormInstance>()
const errorMessage = ref<string | null>(null)

const { initStorage } = useLayout()
initStorage()

const { t } = useI18n()
const { dataTheme, overallStyle, dataThemeChange } = useDataThemeChange()
dataThemeChange(overallStyle.value)
const { title, getDropdownItemStyle, getDropdownItemClass } = useNav()
const { locale, translationCh, translationEn } = useTranslationLang()

const ruleForm = reactive({
  username: 'admin',
  password: 'admin123'
})

const onLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(valid => {
    if (valid) {
      loading.value = true
      useUserStoreHook()
        .loginByUsername({
          username: ruleForm.username,
          password: ruleForm.password
        })
        .then(res => {
          if (res.success) {
            // 获取后端路由
            return initRouter().then(() => {
              disabled.value = true
              router
                .push(getTopMenu(true).path)
                .then(() => {
                  message(t('login.pureLoginSuccess'), { type: 'success' })
                })
                .finally(() => (disabled.value = false))
            })
          } else {
            message(t('login.pureLoginFail'), { type: 'error' })
          }
        })
        .finally(() => (loading.value = false))
    }
  })
}

const immediateDebounce: any = debounce(formRef => onLogin(formRef), 1000, true)

useEventListener(document, 'keydown', ({ code }) => {
  if (
    ['Enter', 'NumpadEnter'].includes(code) &&
    !disabled.value &&
    !loading.value
  )
    immediateDebounce(ruleFormRef.value)
})

const loginType = ref('password')
const messageClass = ref('')

const typeChange = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}

const loginHandler = (loginPromise: Promise<IDataResponse<LoginResult>>) => {
  loginPromise
    .then((res: IDataResponse<LoginResult>) => {
      if (res && res.success) {
        return permissionStore
          .getPermission()
          .then(() => {
            if (permissionStore.firstMenu) {
              return router.push({
                name: permissionStore.firstMenu.name
              })
            } else if (permissionStore.showPermissions[0]) {
              return router.push({
                name: permissionStore.showPermissions[0].name
              })
            }
            return router.push({ name: '403' })
          })
          .then(() => {
            ElNotification({
              title: '登录成功',
              message: `欢迎回来 ${res.data.user.fullName}`,
              type: 'success'
            })
          })
      }
    })
    .catch(err => {
      if (err instanceof Error) {
        console.error('登录出现错误', err)
        errorMessage.value = '登录出现错误'
      } else if (err.msg) {
        errorMessage.value = err.msg
      } else {
        errorMessage.value = err
      }
      messageClass.value = 'shake'
      setTimeout(() => {
        messageClass.value = ''
      }, 800)
    })
}
</script>

<template>
  <div class="select-none">
    <img :src="bg" class="wave" alt="background" />
    <div class="flex-c absolute right-5 top-3">
      <!-- 主题 -->
      <!--      <el-switch
        v-model="dataTheme"
        inline-prompt
        :active-icon="dayIcon"
        :inactive-icon="darkIcon"
        @change="dataThemeChange"
      />
      &lt;!&ndash; 国际化 &ndash;&gt;
      <el-dropdown trigger="click">
        <globalization
          class="hover:text-primary hover:bg-[transparent]! w-[20px] h-[20px] ml-1.5 cursor-pointer outline-hidden duration-300"
        />
        <template #dropdown>
          <el-dropdown-menu class="translation">
            <el-dropdown-item
              :style="getDropdownItemStyle(locale, 'zh')"
              :class="['dark:text-white!', getDropdownItemClass(locale, 'zh')]"
              @click="translationCh"
            >
              <IconifyIconOffline
                v-show="locale === 'zh'"
                class="check-zh"
                :icon="Check"
              />
              简体中文
            </el-dropdown-item>
            <el-dropdown-item
              :style="getDropdownItemStyle(locale, 'en')"
              :class="['dark:text-white!', getDropdownItemClass(locale, 'en')]"
              @click="translationEn"
            >
              <span v-show="locale === 'en'" class="check-en">
                <IconifyIconOffline :icon="Check" />
              </span>
              English
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>-->
    </div>
    <div class="login-container">
      <div class="img">
        <!--        <component :is="toRaw(illustration)" />-->
      </div>
      <div class="login-box">
        <div class="login-form">
          <!--          <avatar class="avatar" />-->
          <img
            :src="loginTitle"
            :class="['login-title', errorMessage ? '' : 'login-title-bottom']"
            alt="login-title"
          />
          <el-alert
            v-if="errorMessage"
            :class="messageClass"
            :closable="false"
            :title="errorMessage"
            effect="dark"
            type="error"
          />
          <Motion :delay="100">
            <password-login @loginHandler="loginHandler" />
          </Motion>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import url('@/style/login.css');
</style>

<style lang="scss" scoped>
:deep(.el-form-item__error) {
  margin-top: 6px;
  margin-left: 10px;
  font-size: 14px;
}

:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}

:deep(.el-tabs) {
  .el-tabs__header {
    margin-bottom: 20px;
  }

  .el-tabs__nav {
    display: flex;
    width: 100%;

    .el-tabs__item {
      flex: 1;
      padding: 0 10px;
      font-size: 14px;
      text-align: center;

      &.is-active {
        font-weight: 500;
      }
    }
  }

  .el-tabs__nav-wrap::after {
    height: 1px;
    opacity: 0.1;
  }
}

.translation {
  ::v-deep(.el-dropdown-menu__item) {
    padding: 5px 40px;
  }

  .check-zh {
    position: absolute;
    left: 20px;
  }

  .check-en {
    position: absolute;
    left: 20px;
  }
}

.shake {
  font-size: 1rem;
  animation: shake 800ms ease-in-out;
}

@keyframes shake {
  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(+2px, 0, 0);
  }

  30%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(+4px, 0, 0);
  }

  50% {
    transform: translate3d(-4px, 0, 0);
  }
}
</style>
