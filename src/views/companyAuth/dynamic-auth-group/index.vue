<template>
  <div class="dynamic-auth">
    <el-radio-group :model-value="authTypeSel" @change="handleAuthTypeChange">
      <el-radio-button
        v-for="auth in authTypes"
        :key="auth.code"
        :value="auth.code"
      >
        {{ auth.name }}
      </el-radio-button>
    </el-radio-group>
    <!--<el-button type="primary" link @click="handleAddAuthType" v-if="isAdd">
      新建权限类别
    </el-button>-->
    <add-auth-type-modal
      v-if="isAdd"
      ref="authTypeModal"
      @ok="handleOk"
      @cancel="handleCancel('authTypeModal')"
    />
  </div>
</template>
<script>
import AddAuthTypeModal from './add-auth-type-modal.vue'
import { get, post } from '@/utils/http/request.js'

export default {
  name: 'DynamicAuthGroup',
  components: { AddAuthTypeModal },
  props: {
    isAdd: {
      type: Boolean,
      required: false
    },
    exclude: {
      type: Array,
      default: () => []
    },
    show: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      authTypes: [],
      radioStyle: {
        display: 'block',
        height: '30px',
        lineHeight: '30px'
      },
      authTypeSel: null,
      visible: false,
      modalLoading: false,
      url: {
        allType: '/voucher/s/obj/type/all',
        type: '/voucher/s/obj/type/types',
        add: '/voucher/s/obj/type'
      }
    }
  },
  watch: {
    exclude: {
      handler: function (n, v) {
        if (n.length > 0) {
          this.getAuthTypes()
        }
      },
      deep: true
    }
  },
  mounted() {
    this.getAuthTypes()
  },
  methods: {
    handleAuthTypeChange(value) {
      this.authTypeSel = value
      this.$emit('curSel', this.authTypeSel)
    },
    handleAddAuthType() {
      this.$refs.authTypeModal.visible = true
    },
    handleOk() {
      const modalRef = this.$refs.authTypeModal
      const form = modalRef.$refs.authTypeForm
      // 获得form表单的值
      form.validate(valid => {
        if (valid) {
          modalRef.loading = true
          post(this.url.add, modalRef.form)
            .then(res => {
              if (res.success) {
                this.authTypes.push(modalRef.form)
                modalRef.visible = false
                // 重置表单数据
                modalRef.resetForm()
              } else {
                this.$message.error(res.msg)
              }
            })
            .finally(() => {
              modalRef.loading = false
            })
        }
      })
    },
    getAuthTypes() {
      get(this.show ? this.url.allType : this.url.type).then(res => {
        if (res.success) {
          this.authTypes = res.data || []
          if (this.authTypes.length > 0) {
            if (this.exclude.length > 0) {
              this.exclude.map(v => {
                const idx = this.authTypes.findIndex(e => e.code === v)
                if (idx >= 0) {
                  this.authTypes.splice(idx, 1)
                }
              })
            }
            this.authTypeSel = this.authTypes[0].code
            this.$emit('curSel', this.authTypeSel)
          }
        }
      })
    }
  }
}
</script>
<style lang="less"></style>
