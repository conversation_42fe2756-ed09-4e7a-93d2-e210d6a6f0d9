<template>
  <el-dialog
    v-model="visible"
    title="新建权限资源类别"
    width="440px"
    :close-on-click-modal="false"
    @close="$emit('cancel')"
  >
    <el-form
      ref="authTypeForm"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="资源名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="内码" prop="code">
        <el-input v-model="form.code" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input v-model="form.sort" type="number" />
      </el-form-item>
      <el-form-item label="类别" prop="type">
        <el-radio-group v-model="form.type" @change="onChange">
          <el-radio value="00102"> 分组对象 </el-radio>
          <el-radio value="00101"> 非分组对象 </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('cancel')">取消</el-button>
        <el-button type="primary" :loading="loading" @click="$emit('ok')"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'
// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入资源名称', trigger: 'blur' }],
  code: [
    {
      required: true,
      message: 'Please select Activity zone',
      trigger: 'change'
    }
  ],
  type: [{ required: true, message: 'Please pick a date', trigger: 'change' }]
}

export default {
  name: 'AddAuthTypeModal',
  props: {
    model: {
      type: Object,
      default: () => null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      }
    }
    return {
      rules: rules,
      form: {
        name: '',
        code: '',
        sort: 0,
        type: '00101'
      },
      visible: false,
      loading: false
    }
  },
  watch: {
    visible: function (n, o) {
      if (n) {
        this.form.code = uuidv4()
      }
    }
  },
  methods: {
    onChange(value) {
      this.form.type = value
    },
    resetForm() {
      this.form = {
        name: '',
        code: '',
        sort: 0,
        type: 0
      }
      this.visible = false
    }
  }
}
</script>
