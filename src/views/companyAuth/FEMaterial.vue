<template>
  <el-card :shadow="never">
    <div class="table-page-search-wrapper">
      <el-form :inline="true" class="ant-advanced-search-form">
        <el-row :gutter="48">
          <el-col :md="8" :sm="24">
            <el-form-item label="资源名称">
              <el-input v-model="queryParam.name" />
            </el-form-item>
          </el-col>
          <el-col :md="8" :sm="24">
            <el-form-item label="资源类型">
              <el-select v-model="queryParam.type">
                <el-option value="00015" label="菜单" />
                <el-option value="00019" label="按钮" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="
                (advanced && { float: 'right', overflow: 'hidden' }) || {}
              "
            >
              <el-button type="primary" @click="$refs.table.refresh(true)"
                >查询</el-button
              >
              <el-button style="margin-left: 8px" @click="handleSearchReset"
                >重置</el-button
              >
            </span>
          </el-col>
        </el-row>
      </el-form>
      <div class="table-operator">
        <el-button type="primary" @click="handleAddMaterialModal">
          <el-icon class="el-icon--right"><PlusIcon /></el-icon>新建资源
        </el-button>
        <el-dropdown v-if="selectedRowKeys.length > 0">
          <el-button style="margin-left: 8px">
            批量操作
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>
                <el-icon><Delete /></el-icon>
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <el-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        @selection-change="onSelectChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" width="80">
          <template #default="{ $index }">
            {{ (pagination.current - 1) * pagination.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="资源名称" prop="name" />
        <el-table-column label="资源类型" prop="type">
          <template #default="{ row }">
            <el-tag v-if="row.type === '00015'" type="success"> 菜单 </el-tag>
            <el-tag v-else type="info">按钮</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="资源路径" prop="attributes">
          <template #default="{ row }">
            <span v-for="attr in row.attributes" :key="attr.key">
              <span v-if="attr.key === 'path' || attr.key === 'redirect'">{{
                attr.value
              }}</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="sort" />
        <el-table-column label="是否启用" prop="enable">
          <template #default="{ row }">
            <el-tag v-if="row.enable" type="success">已启用</el-tag>
            <el-tag v-else type="danger">已停用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="是否可见" prop="hidden">
          <template #default="{ row }">
            <span>{{ row.hidden ? '隐藏' : '可见' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="游客菜单" prop="visit">
          <template #default="{ row }">
            <span>{{ row.visit ? '是' : '否' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <a @click="handleEdit(row, 'copy')">复制</a>
            <el-divider direction="vertical" />
            <a @click="handleEdit(row)">编辑</a>
            <el-divider direction="vertical" />
            <el-dropdown>
              <a class="ant-dropdown-link">
                更多
                <el-icon><ArrowDown /></el-icon>
              </a>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>
                    <a href="javascript:;" @click="handleDetail(row)">详情</a>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-popconfirm
                      title="您确定要删除此项资源?"
                      confirm-button-text="确定"
                      cancel-button-text="取消"
                      @confirm="deleteItem(row.id)"
                    >
                      <template #reference>
                        <a href="javascript:;">删除</a>
                      </template>
                    </el-popconfirm>
                  </el-dropdown-item>
                  <el-dropdown-item v-if="row.type === '00015'">
                    <a href="javascript:;" @click="handleAdd(row.id)"
                      >添加子级</a
                    >
                  </el-dropdown-item>
                  <el-dropdown-item v-if="row.type === '00015'">
                    <a href="javascript:;" @click="handleFieldConfig(row.id)"
                      >字段配置</a
                    >
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        style="margin-top: 20px; text-align: right"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <add-f-e-material-modal
      ref="feMaterialModal"
      :model="mdl"
      @ok="handleOk"
      @cancel="handleCancel('feMaterialModal')"
    />
    <field-config-modal
      ref="fieldConfigModal"
      :model="fieldConfigData"
      @ok="handleFieldConfigOk"
      @cancel="handleCancel('fieldConfigModal')"
    />
  </el-card>
</template>
<script>
import { Plus as PlusIcon, Delete, ArrowDown } from '@element-plus/icons-vue'
import AddFEMaterialModal from '@/views/companyAuth/modules/AddFEMaterialModal.vue'
import FieldConfigModal from '@/views/companyAuth/modules/FieldConfigModal.vue'
import { get } from '@/utils/http/request.js'

const columns = [
  {
    title: '#',
    scopedSlots: { customRender: 'serial' }
  },
  {
    title: '资源名称',
    dataIndex: 'name'
  },
  {
    title: '资源类型',
    dataIndex: 'type',
    scopedSlots: { customRender: 'type' }
  },
  {
    title: '资源路径',
    dataIndex: 'attributes',
    customRender: attributes =>
      attributes.map(v => {
        if (v.key === 'path' || v.key === 'redirect') {
          return v.value
        }
      })
  },
  {
    title: '排序',
    dataIndex: 'sort'
  },
  {
    title: '是否启用',
    dataIndex: 'enable',
    scopedSlots: { customRender: 'enable' }
  },
  {
    title: '是否可见',
    dataIndex: 'hidden',
    customRender: text => (text ? '隐藏' : '可见')
  },
  {
    title: '游客菜单',
    dataIndex: 'visit',
    customRender: text => (text ? '是' : '否')
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '150px',
    scopedSlots: { customRender: 'action' }
  }
]

const statusMap = {
  true: {
    status: 'success',
    text: '已启用'
  },
  false: {
    status: 'default',
    text: '已停用'
  }
}

const resourceMap = {
  '00015': {
    color: 'green',
    text: '菜单'
  },
  '00019': {
    color: 'cyan',
    text: '按钮'
  }
}

export default {
  name: 'FEMaterial',
  components: {
    AddFEMaterialModal,
    FieldConfigModal,
    PlusIcon,
    Delete,
    ArrowDown
  },
  filters: {
    statusFilter(type) {
      return statusMap[type].text
    },
    statusTypeFilter(type) {
      return statusMap[type].status
    },
    resourceFilter(type) {
      return resourceMap[type].text
    },
    resourceTypeFilter(type) {
      return resourceMap[type].color
    }
  },
  mixins: [TableListMixin, ModalMixin],
  data() {
    this.columns = columns
    return {
      mdl: {
        data: null,
        type: 'add'
      },
      fieldConfigData: {
        id: null
      },
      url: {
        list: '/voucher/s/res/list',
        delete: '/voucher/s/res'
      },
      // 表格数据
      tableData: [],
      // 分页配置
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  mounted() {
    this.loadTableData()
  },
  methods: {
    handleCancel(refName) {
      this.$refs[refName].visible = false
    },
    // 加载表格数据
    async loadTableData() {
      this.loading = true
      try {
        const parameter = {
          pageNum: this.pagination.current,
          pageSize: this.pagination.pageSize,
          ...this.queryParam
        }
        const res = await this.loadData(parameter)
        if (res && res.success) {
          this.tableData = res.result || res.data || []
          this.pagination.total = this.tableData.length
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.current = 1
      this.loadTableData()
    },
    // 当前页改变
    handleCurrentChange(current) {
      this.pagination.current = current
      this.loadTableData()
    },
    // 选择改变
    onSelectChange(selection) {
      this.selectedRowKeys = selection.map(item => item.id)
      this.selectedRows = selection
    },
    // 重写 refresh 方法
    refresh() {
      this.loadTableData()
    },
    handleAddMaterialModal() {
      this.handleAdd(null)
    },
    handleAdd(parentId) {
      this.mdl = {
        data: null,
        parentId: parentId,
        type: 'add'
      }
      this.$refs.feMaterialModal.visible = true
    },
    handleEdit(record, type) {
      this.mdl = {
        data: record,
        parentId: null,
        type: type || 'edit'
      }
      this.$refs.feMaterialModal.visible = true
    },
    handleDetail(record) {
      this.mdl = {
        data: record,
        parentId: null,
        type: 'detail'
      }
      this.$refs.feMaterialModal.visible = true
    },
    // 复写loadData方法用新的业务逻辑覆盖
    loadData(parameter) {
      if (!this.url.list) {
        this.$message.warning('未设置url->list')
        return
      }
      const requestParameters = Object.assign({}, parameter, this.queryParam)
      return get(this.url.list, { data: requestParameters }).then(res => {
        if (res.data && res.data.length > 0) {
          this.recuriveProcess(res.data)
        }
        return res
      })
    },
    recuriveProcess(list = []) {
      list.map(v => {
        if (v.children && !v.children.length) {
          v.children = null
        } else {
          this.recuriveProcess(v.children || [])
        }
      })
    },
    handleOk(needRefresh) {
      if (needRefresh) {
        this.refresh()
      }
    },
    handleFieldConfigOk() {
      this.$refs.fieldConfigModal.resetForm()
    },
    handleFieldConfig(id) {
      this.fieldConfigData.id = id
      this.$refs.fieldConfigModal.visible = true
    }
  }
}
</script>
