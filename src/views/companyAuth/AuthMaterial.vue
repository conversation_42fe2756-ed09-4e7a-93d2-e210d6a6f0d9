<template>
  <el-card shadow="never" class="h-full" body-class="h-full">
    <div class="table-page-search-wrapper h-full flex flex-col">
      <dynamic-auth-group
        :is-add="true"
        class="my-auth-group"
        @curSel="getTreeList"
      />
      <div class="auth-material-content flex-1">
        <div class="auth-tree-box">
          <el-input
            v-model="searchValue"
            style="margin-bottom: 20px"
            placeholder="输入内容以搜索"
            clearable
            @input="onChange"
          >
            <template #suffix>
              <el-icon><SearchIcon /></el-icon>
            </template>
          </el-input>
          <el-button
            type="primary"
            :plain="true"
            class="mb-1.5 w-full"
            @click="handleAddMember({ key: '1' }, '')"
          >
            新建权限资源
          </el-button>
          <el-tree
            ref="tree"
            class="mt-2"
            :expanded-keys="expandedKeys"
            :auto-expand-parent="autoExpandParent"
            :data="gData"
            :props="replaceFields"
            :current-node-key="currentSelectedKey"
            :show-checkbox="true"
            :check-strictly="true"
            node-key="id"
            @node-expand="onExpand"
            @node-click="onTreeNodeSelect"
            @check="onTreeNodeCheck"
          >
            <template #default="{ data }">
              <div class="flex-1 flex items-center justify-between pr-2">
                <div class="flex-1 whitespace-nowrap ellipsis overflow-hidden">
                  <span
                    v-if="data.name.indexOf(searchValue) > -1"
                    :title="data.name"
                  >
                    {{ data.name.substr(0, data.name.indexOf(searchValue)) }}
                    <span style="color: #f50">{{ searchValue }}</span>
                    {{
                      data.name.substr(
                        data.name.indexOf(searchValue) + searchValue.length
                      )
                    }}
                  </span>
                  <span v-else :title="data.name">{{ data.name }}</span>
                  <el-tag v-if="defaultIds.has(data.id)" type="success">
                    基础角色
                  </el-tag>
                </div>
                <div @click="e => e.stopPropagation()">
                  <el-space>
                    <el-dropdown
                      @command="
                        command => handleAddMember({ key: command }, data.id)
                      "
                    >
                      <el-icon><PlusIcon /></el-icon>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="1"
                            >添加同级</el-dropdown-item
                          >
                          <el-dropdown-item command="2"
                            >添加下级</el-dropdown-item
                          >
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                    <el-icon @click="handleEditMember(data.id)">
                      <EditIcon />
                    </el-icon>
                    <span class="inline-block" @click="stopPropagation">
                      <el-popconfirm
                        title="确定要删除吗?"
                        @confirm="() => handleDeleteMember(data.id)"
                      >
                        <template #reference>
                          <el-icon><Delete /></el-icon>
                        </template>
                      </el-popconfirm>
                    </span>
                    <el-dropdown
                      @command="
                        command =>
                          command === '1'
                            ? handleDelBaseRole(data.id)
                            : handleSetBaseRole(data.id)
                      "
                    >
                      <el-icon><MoreIcon /></el-icon>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item
                            v-if="defaultIds.has(data.id)"
                            command="1"
                          >
                            取消基础角色
                          </el-dropdown-item>
                          <el-dropdown-item v-else command="2"
                            >设置为基础角色</el-dropdown-item
                          >
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </el-space>
                </div>
              </div>
            </template>
          </el-tree>
        </div>

        <div class="right-content flex flex-col">
          <div class="table-operator">
            <el-button
              type="primary"
              :disabled="!treeNodeSelectedId"
              @click="handleAddSubItem"
            >
              <el-icon class="el-icon--right"><PlusIcon /></el-icon>添加子项
            </el-button>
            <el-dropdown
              v-if="selectedRowKeys.length > 0"
              @command="handleBatchDelete"
            >
              <el-button style="margin-left: 8px">
                批量操作
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="1">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>

          <el-table
            ref="table"
            v-loading="loading"
            class="flex-1"
            :data="tableData"
            row-key="id"
            @selection-change="onSelectChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" width="80">
              <template #default="{ $index }">
                {{
                  (pagination.current - 1) * pagination.pageSize + $index + 1
                }}
              </template>
            </el-table-column>
            <el-table-column label="资源名称" prop="name" />
            <el-table-column label="资源编码" prop="code" />
            <el-table-column label="备注" prop="remark">
              <template #default="{ row }">
                <el-text class="w-150px mb-2" truncated>{{
                  row.remark
                }}</el-text>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-popconfirm
                  title="确定要删除吗?"
                  @confirm="deleteData(row.id)"
                >
                  <template #reference>
                    <a href="#"
                      >删除<span v-if="queryParam.parentId">关系</span></a
                    >
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            style="margin-top: 20px; text-align: right"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <!-- 添加权限类别资源模态框 -->
    <add-auth-role-modal
      ref="authRoleModal"
      :model="roleData"
      @ok="handleAuthRoleOk"
      @cancel="handleCancel('authRoleModal')"
    />
    <!-- 添加权限子资源模态框 -->
    <auth-type-tree-modal
      ref="authTypeTreeModal"
      :exclude="[curAuthType]"
      :vdata="curRestData"
      :parentId="treeNodeSelectedId"
      @ok="handleAuthTypeOk"
      @cancel="handleCancel('authTypeTreeModal')"
    />
  </el-card>
</template>
<script>
import {
  Search as SearchIcon,
  Plus as PlusIcon,
  Edit as EditIcon,
  Delete,
  ArrowDown,
  MoreFilled as MoreIcon
} from '@element-plus/icons-vue'
import DynamicAuthGroup from './dynamic-auth-group/index.vue'
import AddAuthRoleModal from './modules/AddAuthRoleModal.vue'
import AuthTypeTreeModal from './modules/AuthTypeTreeModal.vue'
import { del, get, post } from '@/utils/http/request.js'
import URLS from '@api/URLS'

const columns = [
  {
    title: '#',
    scopedSlots: { customRender: 'serial' }
  },
  {
    title: '资源名称',
    dataIndex: 'name'
  },
  {
    title: '权限类型',
    dataIndex: 'translation.typeStr',
    customRender: text => text || '-'
  },
  {
    title: '默认对象',
    dataIndex: 'defaultObj',
    customRender: text => (text ? '是' : '否')
  },
  {
    title: '备注',
    dataIndex: 'remark',
    scopedSlots: { customRender: 'remark' }
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '150px',
    scopedSlots: { customRender: 'action' }
  }
]

const getParentKey = (key, tree) => {
  let parentKey
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.children) {
      if (node.children.some(item => item.id === key)) {
        parentKey = node.id
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children)
      }
    }
  }
  return parentKey
}

const getKeyData = (key, tree) => {
  let data = null
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.id === key) {
      data = node
    } else if (node.children) {
      if (node.children.some(item => item.id === key)) {
        data = node.children.filter(item => item.id === key)[0]
      } else if (getKeyData(key, node.children)) {
        data = getKeyData(key, node.children)
      }
    }
  }
  return data
}

export default {
  name: 'AuthMaterial',
  components: {
    DynamicAuthGroup,
    AddAuthRoleModal,
    AuthTypeTreeModal,
    SearchIcon,
    PlusIcon,
    EditIcon,
    Delete,
    ArrowDown,
    MoreIcon
  },
  data() {
    this.columns = columns
    return {
      loading: false,
      selectedRowKeys: [],
      currentSelectedKey: null,
      expandedKeys: [],
      searchValue: '',
      autoExpandParent: true,
      curAuthType: null,
      curRestData: {},
      gData: [],
      allData: null,
      showTab: ['position', 'department'],
      url: {
        list: '/voucher/s/obj/list',
        tree: '/voucher/s/obj/companyTree',
        delete: '/voucher/s/obj',
        deleteBatch: '/voucher/s/obj/relationDelete'
      },
      // selected tree node Id
      treeNodeSelectedId: '',
      replaceFields: {
        children: 'children',
        title: 'name',
        key: 'id'
      },
      selectedKeys: [],
      roleData: null,
      defaultIds: new Set(),
      // 表格数据
      tableData: [],
      // 分页配置
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  watch: {
    curAuthType: function (n, o) {
      if (n) {
        this.getAllTreeData(n)
        this.getAuthTableData(n)
      }
    },
    gData: {
      deep: true,
      handler: function () {
        this.defaultIds = new Set()
        this.gData.forEach(item => {
          if (item.extend && item.extend.isDefault === '1') {
            this.defaultIds.add(item.id)
          }
        })
      }
    }
  },
  methods: {
    handleCancel(refName) {
      this.$refs[refName].visible = false
    },
    loadData(parameter) {
      return get(this.url.list, parameter)
    },
    // 加载表格数据
    async loadTableData() {
      this.loading = true
      try {
        const parameter = {
          pageNum: this.pagination.current,
          pageSize: this.pagination.pageSize,
          ...this.queryParam
        }
        const res = await this.loadData(parameter)
        if (res && res.success) {
          this.tableData = res.data || res.result || []
          this.pagination.total = res.totalRecords || 0
        } else {
          this.$message.error(res.msg || '加载数据失败')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.current = 1
      this.loadTableData()
    },
    // 当前页改变
    handleCurrentChange(current) {
      this.pagination.current = current
      this.loadTableData()
    },
    // 选择改变
    onSelectChange(selection) {
      this.selectedRowKeys = selection.map(item => item.id)
      this.selectedRows = selection
    },
    // 重写 refresh 方法
    refresh() {
      this.loadTableData()
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    onChange(e) {
      const value = e
      const expandedKeys = this.gData
        .map(item => {
          if (item.name.indexOf(value) > -1) {
            return getParentKey(item.id, this.gData)
          }
          return null
        })
        .filter((item, i, self) => item && self.indexOf(item) === i)
      Object.assign(this, {
        expandedKeys,
        searchValue: value,
        autoExpandParent: true
      })
    },
    getAllTreeData(n, refresh = false) {
      if (this.allData && Object.keys(this.allData).length > 0 && !refresh) {
        this.gData = this.allData[n]
        return
      }
      get(this.url.tree).then(res => {
        if (res.success) {
          this.allData = res.data
          this.gData = this.allData[n]
        }
      })
    },
    getAuthTableData(n) {
      this.queryParam = {}
      if (this.treeNodeSelectedId) {
        this.queryParam.currentType = n
      } else {
        this.queryParam.type = n
      }
      this.refresh()
    },
    getTreeList($event) {
      // 切换radio button时 置为null
      this.selectedKeys = []
      this.currentSelectedKey = null
      if (this.queryParam?.parentId) {
        delete this.queryParam.parentId
      }
      this.treeNodeSelectedId = null
      this.curAuthType = $event
    },
    handleAddMember(e, id) {
      let parentId = ''
      if (e.key === '1') {
        // 新增同级需要父级的id传给parentId
        parentId = getParentKey(id, this.gData || [])
      } else {
        // 新增子级将id传给parentId
        parentId = id
      }
      this.roleData = {
        data: null,
        type: 'add'
      }
      this.$refs.authRoleModal.parentId = parentId || ''
      this.$refs.authRoleModal.roleType = this.curAuthType
      this.$refs.authRoleModal.visible = true
    },
    handleAddSubItem() {
      // 从allData中获得除this.curAuthType外的treeData
      Object.keys(this.allData).map(v => {
        if (v !== this.curAuthType) {
          this.curRestData[v] = this.allData[v]
        }
      })
      this.$refs.authTypeTreeModal.visible = true
    },
    handleEditMember(id) {
      const t = getKeyData(id, this.gData)
      this.roleData = {
        data: t,
        type: 'edit'
      }
      this.$refs.authRoleModal.visible = true
    },
    handleDeleteMember(id) {
      del(`${this.url.delete}/${id}`).then(res => {
        if (res.success) {
          this.$message.success('删除成功')
          if (this.treeNodeSelectedId === id) {
            // 当删除的node是selected node时，由于node deleted ，table needs refresh
            this.onTreeNodeSelect([null], { selected: true })
          }
          // 更新树
          this.getAllTreeData(this.curAuthType, true)
          this.refresh()
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    // fix bug：嵌套到tree中的popconfirm窗的冒泡事件，导致的定位错误
    stopPropagation(e) {
      e.stopPropagation()
    },
    // tree node select handler
    onTreeNodeSelect(keys, e) {
      // if (e.selected) {
      //   this.queryParam = {}
      //   this.selectedKeys = keys
      //   this.treeNodeSelectedId = keys[0]
      //   this.queryParam.parentId = keys[0]
      //   this.queryParam.currentType = this.curAuthType
      //   this.refresh()
      // }
    },
    onTreeNodeCheck(data, checked) {
      // 获取当前选中的节点
      const checkedKeys = checked.checkedKeys

      if (checkedKeys.length === 0) {
        // 如果没有选中任何节点，清空选择
        this.currentSelectedKey = null
        this.selectedKeys = []
        this.treeNodeSelectedId = null
        if (this.queryParam?.parentId) {
          delete this.queryParam.parentId
        }
      } else {
        // 只保留最后一个选中的节点（实现单选）
        const lastCheckedKey = data.id
        this.currentSelectedKey = lastCheckedKey
        this.treeNodeSelectedId = lastCheckedKey
        this.selectedKeys = [lastCheckedKey]
        this.queryParam.parentId = lastCheckedKey
        // 确保只有一个节点被选中
        this.$refs.tree.setCheckedKeys([lastCheckedKey])
      }
      this.refresh()
    },
    handleAuthRoleOk(success) {
      if (success) {
        this.getAllTreeData(this.curAuthType, true)
      }
    },
    handleAuthTypeOk(success) {
      if (success) {
        this.queryParam = {}
        this.queryParam.parentId = this.treeNodeSelectedId
        if (this.treeNodeSelectedId) {
          this.queryParam.currentType = this.curAuthType
        } else {
          this.queryParam.type = this.curAuthType
        }
        this.refresh()
      }
    },
    // 表格中的删除操作
    deleteData(id) {
      if (this.treeNodeSelectedId) {
        const params = {
          parentId: this.treeNodeSelectedId,
          children: [id]
        }
        this.deleteBatchItems(params).then(() => {
          this.getAllTreeData(this.curAuthType, true)
        })
      } else {
        this.deleteItem(id).then(() => {
          this.getAllTreeData(this.curAuthType, true)
        })
      }
    },
    // 批量删除
    handleBatchDelete() {
      if (this.treeNodeSelectedId) {
        const params = {
          parentId: this.treeNodeSelectedId,
          children: this.selectedRowKeys
        }
        this.deleteBatchItems(params)
      } else {
        // 未选择tree node时的批量删除
      }
    },
    tableChage(pagination, filters, sorter, { currentDataSource }) {
      console.log(
        'pagination, filters, sorter, { currentDataSource }',
        pagination,
        filters,
        sorter,
        {
          currentDataSource
        }
      )
      this.loadData(pagination)
    },
    handleSetBaseRole(id) {
      console.log('handleSetBaseRole(id)', id)
      post(URLS.API_VOUCHER + '/s/objExtend', {
        objectId: id,
        extendKey: 'isDefault',
        extendVal: '1'
      }).then(res => {
        if (res && res.success) {
          this.$message.success('设置成功')
          this.defaultIds.add(id)
          this.getAllTreeData(this.curAuthType, true)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handleDelBaseRole(id) {
      del(URLS.API_VOUCHER + `/s/objExtend/${id}/isDefault`).then(res => {
        if (res && res.success) {
          this.$message.success('删除成功')
          this.defaultIds.delete(id)
          this.getAllTreeData(this.curAuthType, true)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    deleteBatchItems(params) {
      return new Promise((resolve, reject) => {
        if (!this.url.deleteBatch) {
          this.$message.warning('未设置url->deleteBatch')
          reject(new Error('未设置url->deleteBatch！'))
          return
        }
        if (params == null) {
          params = {
            ids: this.selectedRowKeys
          }
        }
        post(this.url.deleteBatch, params).then(res => {
          if (res.success) {
            this.$message.success('删除成功')
            // 刷新表格
            this.refresh()
            resolve()
          } else {
            this.$message.warning(res.msg)
            reject(new Error('删除失败！'))
          }
        })
      })
    }
  }
}
</script>
<style lang="less">
.auth-tree-box {
  border-right: 1px solid #e8e8e8;
  padding-right: 20px;
  overflow: hidden;
  .ant-tree-node-content-wrapper {
    width: 100%;
    // padding: 0 !important;
  }

  // .my-tree-title {
  // padding: 0 5px;
  //   cursor: pointer;
  // }
  .tree-tools {
    display: none;
  }
  // .ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected {
  // background-color: transparent;
  // .my-tree-title {
  //   background-color: fade(@primary-color, 60%);
  // }
  // }
  .ant-tree li .ant-tree-node-content-wrapper {
    position: relative;
  }
  .ant-tree li .ant-tree-node-content-wrapper:hover {
    // background-color: transparent;
    .tree-tools {
      display: block;
    }
    // .my-tree-title {
    //   background-color: fade(@primary-color, 20%);
    // }
  }
}
.my-auth-group {
  margin-bottom: 30px;
}
.auth-material-content {
  display: flex;
  flex-wrap: nowrap;
  .auth-tree-box {
    width: 360px;
    margin-right: 10px;
  }
  .right-content {
    width: calc(100% - 370px);
  }
}
.tree-name-cont {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 220px;
}
</style>
