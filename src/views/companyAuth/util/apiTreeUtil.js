export default class ApiTreeUtil {
  static modifyAPIDataKey(list = []) {
    return list.map(v => {
      v.children = []
      // this.setCheckable(v)
      this.setLeaf(v)
      if (v.interfaces && v.interfaces.length > 0) {
        this.setTreeNode(v.interfaces)
        v.children = v.children.concat(v.interfaces)
      }
      if (v.interfaceGroups && v.interfaceGroups.length > 0) {
        this.setTreeNode(v.interfaceGroups)
        v.children = v.children.concat(v.interfaceGroups)
        this.modifyAPIDataKey(v.interfaceGroups)
      }
      return v
    })
  }
  static setTreeNode(list = []) {
    list.map(v => {
      // this.setCheckable(v)
      this.setLeaf(v)
    })
  }
  static setLeaf(item) {
    if (item.url) {
      item.isLeaf = true
    } else {
      item.isLeaf = false
    }
  }
  static setCheckable(item) {
    if (item.url) {
      item.checkable = true
    } else {
      item.checkable = false
    }
  }
}
