<template>
  <div>
    <el-input
      class="search-tree-input"
      placeholder="输入内容以搜索"
      clearable
      @input="onChange"
    >
      <template #suffix>
        <el-icon><SearchIcon /></el-icon>
      </template>
    </el-input>
    <div v-if="needCheckAll">
      <el-checkbox
        v-model="checkAll"
        :indeterminate="indeterminate"
        @change="onCheckAllChange"
      >
        全选/全不选
      </el-checkbox>
    </div>
    <el-tree
      ref="tree"
      :show-checkbox="checkable"
      :auto-expand-parent="autoExpandParent"
      :default-expanded-keys="expandedKeys"
      :default-checked-keys="checkedKeysVal"
      :data="treeData"
      :props="replaceFields"
      node-key="id"
      @check="onCheck"
      @node-click="onSelect"
      @node-expand="onExpand"
    >
      <template #default="{ data }">
        <el-icon v-if="!data.url || data.url == ''"><Folder /></el-icon>
        <el-icon v-else><Document /></el-icon>
        <span v-html="searchStr(spliceStr(data))" />
      </template>
    </el-tree>
  </div>
</template>
<script>
import { Search as SearchIcon, Folder, Document } from '@element-plus/icons-vue'

export default {
  name: 'GjDirectoryTree',
  components: {
    SearchIcon,
    Folder,
    Document
  },
  props: {
    treeData: {
      type: Array,
      default: () => []
    },
    replaceFields: {
      type: Object,
      default: () => {
        return {
          url: 'url',
          label: 'title',
          children: 'children',
          value: 'key'
        }
      }
    },
    checkable: {
      type: Boolean,
      default: false
    },
    selectable: {
      type: Boolean,
      default: true
    },
    needCheckAll: {
      type: Boolean,
      default: false
    },
    checkedKeys: {
      type: Array,
      default: () => []
    },
    wholeChildKeys: {
      type: Array,
      default: () => []
    },
    selectedKeys: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      expandedKeys: [],
      checkedKeysVal: [],
      selectedKeysVal: [],
      searchValue: '',
      autoExpandParent: true,
      indeterminate: false,
      checkAll: false
    }
  },
  watch: {
    checkedKeys: {
      handler: function (n, o) {
        if (n) {
          this.$refs.tree.setCheckedKeys([])
          this.checkedKeysVal = n
          if (this.needCheckAll) {
            this.isCheckAll(this.checkedKeysVal)
          }
        }
      },
      deep: true
    },
    selectedKeys: {
      handler: function (n, o) {
        this.selectedKeysVal = n
      },
      deep: true
    }
  },
  methods: {
    spliceStr(item) {
      let title = ''
      for (const key in this.replaceFields) {
        if (key === 'value' || key === 'children') {
          continue
        }
        if (item[this.replaceFields[key]]) {
          title += item[this.replaceFields[key]] + ' '
        }
      }
      return title
    },
    searchStr(title) {
      if (this.searchValue && title.indexOf(this.searchValue) > -1) {
        title =
          title.substr(0, title.indexOf(this.searchValue)) +
          ' <span style="color: #f50">' +
          this.searchValue +
          '</span> ' +
          title.substr(
            title.indexOf(this.searchValue) + this.searchValue.length
          )
      }
      return title
    },
    getExpandedKeys(tree, searchVal) {
      if (!searchVal) {
        return
      }
      tree.map(v => {
        if (v.children) {
          // 如果node 有children 且children 的名称包含搜索的字符，则该node展开
          let expand = false
          v.children.map(s => {
            if (this.spliceStr(s).indexOf(searchVal) > -1) {
              expand = true
            }
          })
          if (expand) {
            this.expandedKeys.push(v[this.replaceFields.value])
          }
          this.getExpandedKeys(v.children, searchVal)
        }
      })
    },
    onExpand(data, node) {
      if (node.expanded) {
        this.expandedKeys.push(node.key)
      } else {
        this.expandedKeys = this.expandedKeys.filter(key => key !== node.key)
      }
      this.autoExpandParent = false
    },
    onChange(value) {
      this.expandedKeys = []
      this.getExpandedKeys(this.treeData, value)
      Object.assign(this, {
        searchValue: value,
        autoExpandParent: true
      })
    },
    onCheck(current, info) {
      const checkedKeys = info.checkedKeys
      this.isCheckAll(checkedKeys)
      this.$emit('update:checkedKeys', checkedKeys)
      this.$emit('check', checkedKeys, info)
    },
    onSelect(data, node) {
      // const keys = [node.key]
      // this.$emit('update:selectedKeys', keys)
      // this.$emit('select', keys, { node, data })
    },
    onCheckAllChange(checked) {
      if (checked === true) {
        this.checkAll = true
        this.checkedKeysVal = this.wholeChildKeys
        this.isCheckAll(this.checkedKeysVal)
      } else {
        this.checkAll = false
        this.checkedKeysVal = []
      }
      this.$emit('update:checkedKeys', this.checkedKeysVal)
      this.$emit('check', this.checkedKeysVal)
    },
    isCheckAll(checkedKeys) {
      const t = this.wholeChildKeys.length
      const checkedLen = checkedKeys.length
      if (checkedLen === t) {
        this.checkAll = true
        this.indeterminate = false
      } else {
        this.checkAll = false
        if (checkedLen > 0) {
          this.indeterminate = true
        } else {
          this.indeterminate = false
        }
      }
    }
  }
}
</script>
<style lang="less">
.search-tree-input {
  margin-bottom: 20px;
  max-width: 220px;
}
.auth-tree-box {
  border-right: 1px solid #e8e8e8;
  padding-right: 20px;
  overflow: hidden;
  .el-tree-node__content {
    width: 100%;
  }

  .tree-tools {
    display: none;
  }

  .el-tree-node__content {
    position: relative;
  }
  .el-tree-node__content:hover {
    .tree-tools {
      display: block;
    }
  }
}
.my-auth-group {
  margin-bottom: 30px;
}
</style>
