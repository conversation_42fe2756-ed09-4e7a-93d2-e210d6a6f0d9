import { Plus, MoreFilled, Search } from '@element-plus/icons-vue'

export default {
  name: 'Tree',
  components: {
    Plus,
    MoreFilled,
    Search
  },
  props: {
    dataSource: {
      type: Array,
      required: true
    },
    openKeys: {
      type: Array,
      default: () => []
    },
    search: {
      type: Boolean,
      default: false
    }
  },
  created() {
    this.localOpenKeys = this.openKeys.slice(0)
  },
  data() {
    return {
      localOpenKeys: []
    }
  },
  methods: {
    handlePlus(item) {
      this.$emit('add', item)
    },
    handleTitleClick(...args) {
      this.$emit('titleClick', { args })
    },

    renderSearch() {
      return (
        <el-input
          placeholder="input search text"
          style={{ width: '100%', marginBottom: '1rem' }}
          v-slots={{
            suffix: () => (
              <el-icon>
                <Search />
              </el-icon>
            )
          }}
        />
      )
    },
    renderIcon(icon) {
      return (
        (icon && (
          <el-icon>
            <i class={`el-icon-${icon}`} />
          </el-icon>
        )) ||
        null
      )
    },
    renderMenuItem(item) {
      return (
        <el-menu-item key={item.key} index={item.key}>
          {this.renderIcon(item.icon)}
          {item.title}
          <el-button
            class="btn"
            style={{ width: '20px', zIndex: 1300 }}
            size="small"
            text
            onClick={() => this.handlePlus(item)}
          >
            <el-icon>
              <Plus />
            </el-icon>
          </el-button>
        </el-menu-item>
      )
    },
    renderItem(item) {
      return item.children
        ? this.renderSubItem(item, item.key)
        : this.renderMenuItem(item, item.key)
    },
    renderItemGroup(item) {
      const childrenItems = item.children.map(o => {
        return this.renderItem(o, o.key)
      })

      return (
        <el-menu-item-group key={item.key}>
          <template v-slot:title>
            <span>{item.title}</span>
            <el-dropdown>
              <el-button class="btn" text>
                <el-icon>
                  <MoreFilled />
                </el-icon>
              </el-button>
              <template v-slot:dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>新增</el-dropdown-item>
                  <el-dropdown-item>合并</el-dropdown-item>
                  <el-dropdown-item>移除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          {childrenItems}
        </el-menu-item-group>
      )
    },
    renderSubItem(item, key) {
      const childrenItems =
        item.children &&
        item.children.map(o => {
          return this.renderItem(o, o.key)
        })

      const title = (
        <template v-slot:title>
          {this.renderIcon(item.icon)}
          <span>{item.title}</span>
        </template>
      )

      if (item.group) {
        return this.renderItemGroup(item)
      }
      // titleClick={this.handleTitleClick(item)}
      return (
        <el-sub-menu key={key} index={key}>
          {title}
          {childrenItems}
        </el-sub-menu>
      )
    }
  },
  render() {
    const { dataSource, search } = this.$props

    // this.localOpenKeys = openKeys.slice(0)
    const list = dataSource.map(item => {
      return this.renderItem(item)
    })

    return (
      <div class="tree-wrapper">
        {search ? this.renderSearch() : null}
        <el-menu
          mode="vertical"
          class="custom-tree"
          default-openeds={this.localOpenKeys}
          onSelect={item => this.$emit('click', item)}
          onOpen={val => {
            this.localOpenKeys = [...this.localOpenKeys, val]
          }}
          onClose={val => {
            this.localOpenKeys = this.localOpenKeys.filter(key => key !== val)
          }}
        >
          {list}
        </el-menu>
      </div>
    )
  }
}
