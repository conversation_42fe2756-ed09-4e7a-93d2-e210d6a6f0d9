<template>
  <div>
    <el-input
      v-model="searchValue"
      class="search-tree-input"
      placeholder="输入内容以搜索"
      clearable
      @input="onChange"
    >
      <template #suffix>
        <el-icon><SearchIcon /></el-icon>
      </template>
    </el-input>
    <div v-if="needCheckAll">
      <el-checkbox
        v-model="checkAll"
        :indeterminate="indeterminate"
        @change="onCheckAllChange"
      >
        全选/全不选
      </el-checkbox>
    </div>
    <el-tree
      ref="tree"
      :show-checkbox="checkable"
      :default-checked-keys="checkedKeysVal"
      :default-expanded-keys="expandedKeys"
      :auto-expand-parent="autoExpandParent"
      :data="treeData"
      :props="replaceFields"
      :check-strictly="checkStrictly"
      node-key="id"
      @check="onCheck"
      @node-click="onSelect"
      @node-expand="onExpand"
    >
      <template #default="{ data }">
        <span
          v-if="
            transformI18n(data[replaceFields.label])?.indexOf(searchValue) > -1
          "
        >
          {{
            transformI18n(data[replaceFields.label]).substr(
              0,
              transformI18n(data[replaceFields.label]).indexOf(searchValue)
            )
          }}
          <span style="color: #f50">{{ searchValue }}</span>
          {{
            transformI18n(data[replaceFields.label]).substr(
              transformI18n(data[replaceFields.label]).indexOf(searchValue) +
                searchValue.length
            )
          }}
        </span>
        <span v-else>{{ transformI18n(data[replaceFields.label]) }}</span>
      </template>
    </el-tree>
  </div>
</template>
<script>
import { Search as SearchIcon } from '@element-plus/icons-vue'
import { transformI18n } from '@/plugins/i18n.js'

export default {
  name: 'GjTree',
  components: {
    SearchIcon
  },
  props: {
    treeData: {
      type: Array,
      default: () => []
    },
    replaceFields: {
      type: Object,
      default: () => {
        return {
          label: 'title',
          children: 'children',
          value: 'key'
        }
      }
    },
    checkable: {
      type: Boolean,
      default: false
    },
    checkStrictly: {
      type: Boolean,
      default: true
    },
    checkOne: {
      type: Boolean,
      default: false
    },
    selectable: {
      type: Boolean,
      default: true
    },
    needCheckAll: {
      type: Boolean,
      default: false
    },
    checkedKeys: {
      type: Array,
      default: () => []
    },
    wholeChildKeys: {
      type: Array,
      default: () => []
    },
    selectedKeys: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      expandedKeys: [],
      checkedKeysVal: [],
      selectedKeysVal: [],
      searchValue: '',
      autoExpandParent: true,
      indeterminate: false,
      checkAll: false,
      childrenMap: new Map(),
      allKeys: new Set(),
      currentKeys: []
    }
  },
  watch: {
    checkedKeys: {
      handler: function (n, o) {
        if (n && n.length > 0) {
          this.checkedKeysVal = Array.from(new Set(n))
          this.expandedKeys = this.checkedKeysVal
        } else {
          this.checkedKeysVal = []
          this.expandedKeys = []
          this.$refs.tree.setCheckedKeys([])
        }
        if (this.needCheckAll) {
          // this.checkAll = this.checkedKeysVal.length === this.allKeys.size
          this.isCheckAll(this.checkedKeysVal)
        }
      },
      deep: true
    },
    selectedKeys: {
      handler: function (n, o) {
        this.selectedKeysVal = n
      },
      deep: true
    }
  },
  mounted() {
    this.getAllChildren(this.treeData, this.allKeys)
  },
  methods: {
    transformI18n,
    getExpandedKeys(tree, searchVal) {
      if (!searchVal) {
        return
      }
      tree.map(v => {
        if (v.children) {
          // 如果node 有children 且children 的名称包含搜索的字符，则该node展开
          let expand = false
          v.children.map(s => {
            if (s[this.replaceFields.label].indexOf(searchVal) > -1) {
              expand = true
            }
          })
          if (expand) {
            this.expandedKeys.push(v[this.replaceFields.value])
          }
          this.getExpandedKeys(v.children, searchVal)
        }
      })
    },
    getAllChildren(tree, set) {
      tree.forEach(v => {
        const childrenKeys = new Set()
        this.childrenMap.set(v.id, childrenKeys)
        if (v.children) {
          this.getAllChildren(v.children, childrenKeys)
        }
        if (set) {
          set.add(v.id)
          if (childrenKeys.size > 0) {
            childrenKeys.forEach(key => set.add(key))
          }
        }
      })
    },
    onExpand(data, node) {
      if (node.expanded) {
        this.expandedKeys.push(node.key)
      } else {
        this.expandedKeys = this.expandedKeys.filter(key => key !== node.key)
      }
      this.autoExpandParent = false
    },
    onChange(value) {
      this.expandedKeys = []
      this.getExpandedKeys(this.treeData, value)
      Object.assign(this, {
        searchValue: value,
        autoExpandParent: true
      })
    },
    onCheck(current, selectInfo) {
      let keys = selectInfo.checkedKeys

      if (this.checkOne) {
        keys = [current[this.replaceFields.value]]
      }

      if (
        this.checkedKeysVal.find(
          item => current[this.replaceFields.value] === item
        )
      ) {
        keys = this.checkedKeysVal.filter(
          item => current[this.replaceFields.value] !== item
        )
      }

      this.checkedKeysVal = keys
      this.$refs.tree.setCheckedKeys(keys)
      this.isCheckAll(keys)
      this.$emit('update:checkedKeys', keys)
      this.$emit('check', keys, current)
      if (this.checkOne) {
        this.$emit('select', keys, current)
      }
    },
    onSelect(data, node) {
      // const keys = [node.key]
      // this.$emit('update:selectedKeys', keys)
      // this.$emit('select', keys, { node, data })
    },
    onCheckAllChange(e) {
      if (this.checkAll) {
        this.checkedKeysVal = Array.from(this.allKeys)
      } else {
        this.checkedKeysVal = []
      }
      this.$emit('update:checkedKeys', this.checkedKeysVal)
      this.$emit('check', this.checkedKeysVal)
    },
    isCheckAll(checkedKeys) {
      const t = this.allKeys.size
      const checkedLen = checkedKeys.length
      if (checkedLen === t) {
        this.checkAll = true
        this.indeterminate = false
      } else {
        this.checkAll = false
        if (checkedLen > 0) {
          this.indeterminate = true
        } else {
          this.indeterminate = false
        }
      }
    }
  }
}
</script>
<style lang="less">
.search-tree-input {
  margin-bottom: 20px;
  max-width: 220px;
}
.auth-tree-box {
  border-right: 1px solid #e8e8e8;
  padding-right: 20px;
  overflow: hidden;
  .el-tree-node__content {
    width: 100%;
  }

  .tree-tools {
    display: none;
  }

  .el-tree-node__content {
    position: relative;
  }
  .el-tree-node__content:hover {
    .tree-tools {
      display: block;
    }
  }
}
.my-auth-group {
  margin-bottom: 30px;
}
</style>
