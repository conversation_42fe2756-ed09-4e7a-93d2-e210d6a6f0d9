<template>
  <el-card :shadow="never">
    <div
      style="
        padding-top: 20px;
        padding-left: 10px;
        margin-top: 20px;
        margin-bottom: 10px;
        margin-left: 10px;
        border: 1px solid black;
      "
    >
      <p>1、请选择需要排序的人员，若未选择，则按照入职时间排序</p>
      <p>
        2、此排序未企业全局排序，在同一部门下，均以此排序顺序排列，未排序人员同第一条规则
      </p>
    </div>
    <div>
      <el-button
        style="width: 100px; margin-right: 10px; margin-bottom: 10px"
        @click="showDrawer"
      >
        <el-icon><PlusIcon /></el-icon>添加
      </el-button>
      <el-button @click="confirm('delete')">
        批量移除
        <el-icon><Check /></el-icon>
      </el-button>
      <el-button
        type="primary"
        style="float: right; width: 100px; margin-bottom: 10px"
        @click="confirm('save')"
      >
        保存排序
      </el-button>
    </div>
    <div>
      <el-drawer
        v-model="visible"
        title="选择人员"
        direction="rtl"
        size="500px"
        @close="onClose"
      >
        <el-radio-group v-model="radioValue" size="default">
          <el-radio-button value="0"> 人员 </el-radio-button>
        </el-radio-group>
        <el-input
          v-model="searchValue"
          style="margin-top: 20px"
          placeholder="输入姓名"
          clearable
          @input="inputOnchange"
          @keyup.enter="onSearch"
        >
          <template #append>
            <el-button @click="onSearch">查询</el-button>
          </template>
        </el-input>
        <br /><br />
        <div>
          <el-table
            v-show="drawerAnimationShow"
            :row-selection="selectionConfig"
            :row-key="item => item.userId"
            :columns="personColumns"
            :data="personData"
            @current-change="pageChange"
          />
        </div>
        <div
          v-show="!drawerAnimationShow"
          v-loading="true"
          style="
            width: 100%;
            height: 98%;
            line-height: 620px;
            background-color: white;
          "
          element-loading-text="Loading..."
        >
          <div class="spin-content">
            <center style="margin-top: 20px">加载中</center>
          </div>
        </div>
        <div />
        <el-button
          :type="personNum > 0 ? 'primary' : ''"
          @click="addToTempTable"
        >
          添加{{ ' ' + personNum }}
        </el-button>
      </el-drawer>
    </div>
    <div>
      <div v-show="animationShow">
        <el-table
          :row-selection="tampTableSelectionConfig"
          :row-key="item => item.userId"
          :data="data"
          max-height="400"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="index" label="序号" />
          <el-table-column prop="userName" label="姓名" />
          <el-table-column prop="position" label="职务" />
          <el-table-column prop="department" label="部门" />
          <el-table-column label="顺序">
            <template #default="{ row }">
              <div style="width: 70px">
                <EditCell
                  ref="editCell"
                  :tableLength="data.length"
                  :index="row.index"
                  :sortNum="row.sort"
                  @change="onCellChange(row.sort, $event, row)"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="{ row }">
              <a @click="onDelete(row.index)">移除</a>
              <a
                style="margin-left: 10px"
                @click="moveInTempTable(2, row.index)"
                >移至顶部</a
              >
              <a
                style="margin-left: 10px"
                @click="moveInTempTable(3, row.index)"
                >移至底部</a
              >
            </template>
          </el-table-column>
          <el-table-column label="移动">
            <template #default="{ row }">
              <el-icon
                title="上移一行"
                style="font-size: 15px"
                class="element"
                @click="moveInTempTable(0, row.index)"
                ><ArrowUp
              /></el-icon>
              <br />
              <el-icon
                title="下移一行"
                style="margin-right: 20px"
                class="element"
                @click="moveInTempTable(1, row.index)"
                ><ArrowDown
              /></el-icon>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div
        v-show="!animationShow"
        v-loading="true"
        style="
          width: 100%;
          height: 100%;
          line-height: 620px;
          background-color: white;
        "
        element-loading-text="Loading..."
      >
        <div class="spin-content">
          <center style="margin-top: 20px">加载中</center>
        </div>
      </div>
    </div>
  </el-card>
</template>
<script>
import {
  Plus as PlusIcon,
  Check,
  ArrowUp,
  ArrowDown
} from '@element-plus/icons-vue'
import EditCell from '@/views/companyAuth/AuthUserComponents/EditCell.vue'
import { post, get } from '@/utils/http/request'

const columns = [
  {
    title: '序号',
    dataIndex: 'index'
  },
  {
    title: '姓名',
    dataIndex: 'userName'
  },
  {
    title: '职务',
    dataIndex: 'position'
  },
  {
    title: '部门',
    dataIndex: 'department'
  },
  {
    title: '顺序',
    dataIndex: 'sort',
    scopedSlots: { customRender: 'sort' },
    sorter: (a, b) => a.sort - b.sort
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' }
  },
  {
    title: '移动',
    dataIndex: 'move',
    scopedSlots: { customRender: 'move' }
  }
]
const personColumns = [
  {
    title: '序号',
    width: 25,
    dataIndex: 'index'
  },
  {
    title: '姓名',
    width: 100,
    dataIndex: 'userName'
  }
]
const personData = []

export default {
  name: 'AuthUser',
  components: {
    EditCell,
    PlusIcon,
    Check,
    ArrowUp,
    ArrowDown
  },
  data() {
    return {
      columns,
      data: [],
      personColumns,
      personData,
      personNum: 0,
      tampData: [],
      pageAddress: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      url: {
        all: '/voucher/s/userCompanyExtend/query?exKey=sort',
        saveList: '/voucher/s/userCompanyExtend/list/sort',
        userList: 'voucher/s/user/companyInfo'
        // userList: '/voucher/s/company/user' // 'http://*************:21201
      },
      visible: false,
      radioValue: '0',
      selectionConfig: {
        selectedRowKeys: [],
        selectedRows: [],
        onChange: this.handleSelectOnchange
      },
      tampTableSelectionConfig: {
        selectedRowKeys: [],
        selectedRows: [],
        onChange: this.handleTampTableSelectOnchange
      },
      modalType: '',
      modalText: '',
      addRows: [],
      searchValue: '',
      removeList: [],
      edit: true,
      drawerAnimationShow: true,
      animationShow: true,
      modalVisible: false,
      tableLength: 0,
      timer: null // 定时器
    }
  },
  watch: {},
  created() {
    this.getList()
  },
  methods: {
    // 重置
    reSet() {
      this.personNum = 0
      this.selectionConfig.selectedRows = []
      this.selectionConfig.selectedRowKeys = []
      this.tampTableSelectionConfig.selectedRows = []
      this.tampTableSelectionConfig.selectedRowKeys = []
      this.searchValue = ''
      this.addRows = []
      this.removeList = []
    },
    // 信息
    info(type, msg, selType) {
      this.$notification[type]({
        message: msg,
        description: selType
      })
    },
    getList() {
      this.animationShow = false
      get(this.url.all).then(res => {
        this.data = res.data
        if (this.data && this.data.length > 0) {
          this.data.forEach((item, index) => {
            // console.log(index, '-------------------p')
            item.key = item.id
            item.index = index + 1
            item.sort = index
            item.userName = item.translation.userIdStr.fullName
            if (
              item.translation.userIdStr.groupedData.department === undefined
            ) {
              item.department = '----'
            } else {
              item.department =
                item.translation.userIdStr.groupedData.department
                  .map(temp => temp.objectName)
                  .join('、')
            }
            if (item.translation.userIdStr.groupedData.position === undefined) {
              item.position = '----'
            } else {
              item.position = item.translation.userIdStr.groupedData.position
                .map(temp => temp.objectName)
                .join('、')
            }
          })
        }
        this.animationShow = true
      })
    },
    sort() {
      console.log('sort')
      const temp = this.data.sort((a, b) => a.sort - b.sort)
      console.log('sort.temp', temp)
    },
    getUserList() {
      this.drawerAnimationShow = false
      get(this.url.userList, {
        data: {
          pageNum: this.pageAddress.current,
          fullName: this.searchValue
        }
      }).then(res => {
        this.tampData = res.data
        this.pageAddress.pageSize = res.pageSize
        this.pageAddress.total = res.totalRecords
        this.pageAddress.current = res.pageNum
        if (this.tampData && this.personData) {
          this.personData = []
          this.tampData.forEach((item, index) => {
            const data = {
              userName: item.user.fullName,
              userId: item.user.userId,
              index: index + 1
            }
            if (item.auth?.privateAuths[0]?.auths?.department !== undefined) {
              data.department = item.auth.privateAuths[0].auths.department
                .map(temp => temp.objectName)
                .join('、')
            } else {
              data.department = '----'
            }
            if (item.auth?.privateAuths[0]?.auths?.position !== undefined) {
              data.position = item.auth.privateAuths[0].auths.position
                .map(temp => temp.objectName)
                .join('、')
            } else {
              data.position = '----'
            }
            this.personData.push(data)
          })
        }
        this.drawerAnimationShow = true
      })
    },
    saveList() {
      const saveDataList = []
      // if (this.data.length < 0) {
      //   this.info('error', '提示', '无数据保存')
      //   return
      // }
      this.data.forEach((item, index) => {
        saveDataList.push({
          userId: item.userId,
          exKey: 'sort',
          exValue: index
        })
      })
      post(this.url.saveList, saveDataList).then(res => {
        if (res.success) {
          this.info('success', '提示', '保存成功')
          // this.modalVisible = false
          this.getList()
          this.reSet()
        } else {
          this.info('error', '提示', '保存失败')
        }
      })
    },
    afterVisibleChange(val) {},
    pageChange(page) {
      this.pageAddress.current = page.current
      this.getUserList()
    },
    // 开启抽屉
    showDrawer() {
      this.visible = true
      this.getUserList()
    },
    // 关闭抽屉
    onClose() {
      this.visible = false
      this.reSet()
    },
    // 打开对话框
    // showModal(type) {
    //   if (type === 'delete' && this.removeList.length !== 0) {
    //     this.modalType = type
    //     this.modalText = '是否移除'
    //     this.modalVisible = true
    //     return
    //   }
    //   if (type === 'save') {
    //     this.modalType = type
    //     this.modalText = '是否保存排序'
    //     this.modalVisible = true
    //   }
    // },
    confirm(type) {
      const self = this
      if (type === 'delete') {
        if (this.removeList.length === 0) {
          this.info('error', '错误', '没有选择')
          return
        }
        this.modalType = type
        this.$confirm({
          title: '批量移除',
          content: '是否移除',
          okText: '确认',
          cancelText: '取消',
          onOk() {
            self.onDeleteList()
          },
          onCancel() {}
        })
      }
      if (type === 'save') {
        this.modalType = type
        this.$confirm({
          title: '保持排序',
          content: '是否保存',
          okText: '确认',
          cancelText: '取消',
          onOk() {
            self.saveList()
          },
          onCancel() {}
        })
      }
    },
    // 取消对话框
    handleCancel(e) {
      this.modalVisible = false
    },
    // 批量操作
    handleChange(value) {
      if (this.removeList.length === 0) {
        this.info('error', '错误', '没有选择')
      }
      // this.onDeleteList()
    },
    // // 保存排序
    // handleOk() {
    //   if (this.modalType === 'delete' ) {
    //     this.onDeleteList()
    //     this.modalVisible = false
    //     return
    //   }
    //   if (this.modalType === 'save') {
    //     this.saveList()
    //   }
    // },
    // 监听搜索按钮
    onSearch(value) {
      clearTimeout(this.timer)
      if (this.searchValue.length > 64) {
        this.info('error', '错误', '输入的字符过多')
        return
      }
      this.getUserList()
    },
    // 人员搜索防抖
    inputOnchange() {
      clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        // 执行搜索请求
        this.onSearch()
      }, 1000) // 设置时间
    },
    // 人员表批量选择
    handleSelectOnchange(selectedRowKeys, selectedRows) {
      this.selectionConfig.selectedRowKeys = selectedRowKeys
      this.selectionConfig.selectedRows = selectedRows
      if (selectedRows.length > 0 && selectedRows) {
        // 每次翻页selectedRows会被重新赋值 ，用addRows保存每一页的数据
        selectedRows.forEach(item => {
          this.addRows.push({
            key: item.userId,
            userId: item.userId,
            userName: item.userName,
            department: item.department,
            position: item.position
          })
        })
      }
      this.personNum = selectedRowKeys.length
    },
    // 临时表批量选择
    handleTampTableSelectOnchange(selectedRowKeys, selectedRows) {
      this.tampTableSelectionConfig.selectedRowKeys = selectedRowKeys
      this.tampTableSelectionConfig.selectedRows = selectedRows
      if (selectedRows.length > 0) {
        selectedRows.forEach(item => {
          this.removeList.push(item.index)
        })
      } else {
        this.removeList = []
      }
    },
    // 添加到临时表
    addToTempTable() {
      if (this.personNum > 0 && this.addRows.length > 0) {
        this.addRows = this.addRows.filter(
          (item, index, self) =>
            self.findIndex(obj => obj.userId === item.userId) === index
        )
        const userIdList = []
        var addCount = 0
        // console.log('this.addRows', this.addRows)
        if (this.data.length > 0) {
          this.data.forEach(item => {
            userIdList.push(item.userId)
          })
          this.addRows.forEach(item => {
            const num = this.data.length + 1
            if (!userIdList.includes(item.userId)) {
              this.data.push({
                index: num,
                sort: this.data[this.data.length - 1].sort + 1,
                userId: item.userId,
                userName: item.userName,
                key: item.userId,
                department: item.department,
                position: item.position
              })
              addCount = addCount + 1
            }
          })
        } else {
          this.addRows.forEach(item => {
            const num = this.data.length + 1
            this.data.push({
              index: num,
              sort: num,
              userId: item.userId,
              userName: item.userName,
              key: item.userId,
              department: item.department,
              position: item.position
            })
            addCount = addCount + 1
          })
        }
        this.info('success', '添加成功', '添加了' + addCount + '项')
      }
      this.visible = false
      this.reSet()
    },
    // 在内临时表上下移动
    moveInTempTable(moveType, sortNum, noInfo) {
      // console.log('sortNum', sortNum)
      // console.log('moveType', moveType)
      // console.log('openopen', this.data)
      // moveType 0 向上 1 向下  2 移至顶部 3 移至底部
      const tableLength = this.data.length
      if (moveType === 0) {
        if (sortNum === 1 && noInfo !== true) {
          this.info('open', '提示', '已经是首位了')
          return
        }
        this.moveTool(sortNum, sortNum - 1)
      }
      if (moveType === 1 && noInfo !== true) {
        if (sortNum === tableLength) {
          this.info('open', '提示', '已经是末尾了')
          return
        }
        this.moveTool(sortNum, sortNum + 1)
      }
      if (moveType === 2) {
        if (sortNum === 1 && noInfo !== true) {
          this.info('open', '提示', '已经是首位了')
          return
        }
        while (true) {
          if (sortNum === 1) {
            break
          }
          this.moveTool(sortNum, sortNum - 1)
          sortNum = sortNum - 1
        }
      }
      if (moveType === 3) {
        if (sortNum === this.data.length && noInfo !== true) {
          this.info('open', '提示', '已经是末尾了')
          return
        }
        while (true) {
          if (sortNum === tableLength) {
            break
          }
          this.moveTool(sortNum, sortNum + 1)
          sortNum = sortNum + 1
        }
      }
    },
    // 移动工具(交换)
    moveTool(fromSort, toSort) {
      if (this.data.length > 0) {
        fromSort = fromSort - 1
        toSort = toSort - 1
        const tamp = {
          userId: this.data[toSort].userId,
          userName: this.data[toSort].userName,
          department: this.data[toSort].department,
          position: this.data[toSort].position,
          sort: this.data[toSort].sort
        }
        this.data[toSort].userId = this.data[fromSort].userId
        this.data[toSort].userName = this.data[fromSort].userName
        this.data[toSort].department = this.data[fromSort].department
        this.data[toSort].position = this.data[fromSort].position
        this.data[toSort].sort = this.data[fromSort].sort
        this.data[fromSort].userId = tamp.userId
        this.data[fromSort].userName = tamp.userName
        this.data[fromSort].department = tamp.department
        this.data[fromSort].position = tamp.position
        this.data[fromSort].sort = tamp.sort
      }
    },
    // 编辑排序
    onCellChange(fromSort, toSort, record) {
      if (fromSort - toSort === 0) {
        return
      }
      record.sort = toSort
      this.sort()
      /* if (fromSort - toSort > 0) {
        while (true) {
          if (fromSort === toSort) {
            break
          }
          this.moveTool(fromSort, fromSort -1)
          fromSort = fromSort -1
        }
      }
      if (fromSort - toSort < 0) {
        while (true) {
          if (fromSort === toSort) {
            break
          }
          this.moveTool(fromSort, fromSort + 1)
          fromSort = fromSort + 1
        }
      } */
    },
    // 把临时表内的数据移除
    onDelete(sortNum) {
      // 如果只有一条数据 直接删除
      if (this.data.length === 1) {
        return this.data.pop()
      }
      // 将数据移动到最后 然后删除
      this.moveInTempTable(3, sortNum, true)
      return this.data.pop()
    },
    // 批量移除
    onDeleteList() {
      // 转set去重
      this.removeList = Array.from(new Set(this.removeList))
      let i = 0
      this.removeList.forEach(item => {
        this.onDelete(item - i)
        i++
      })
      this.reSet()
    }
  }
}
</script>
<style lang="less">
.active {
  background-color: #1677ff;
  color: white;
}

.element {
  transition: transform 0.3s ease;
}

.element:hover {
  transform: scale(1.5); /* 使用scale属性来实现元素的缩放 */
}

.auth-tree-box {
  border-right: 1px solid #e8e8e8;
  padding-right: 20px;
  overflow: hidden;

  .ant-tree-node-content-wrapper {
    width: 100%;
    // padding: 0 !important;
  }

  // .my-tree-title {
  // padding: 0 5px;
  //   cursor: pointer;
  // }
  .tree-tools {
    display: none;
  }

  // .ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected {
  // background-color: transparent;
  // .my-tree-title {
  //   background-color: fade(@primary-color, 60%);
  // }
  // }
  .ant-tree li .ant-tree-node-content-wrapper {
    position: relative;
  }

  .ant-tree li .ant-tree-node-content-wrapper:hover {
    // background-color: transparent;
    .tree-tools {
      display: block;
    }

    // .my-tree-title {
    //   background-color: fade(@primary-color, 20%);
    // }
  }
}

.my-auth-group {
  margin-bottom: 30px;
}
</style>
