<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="440px"
    :close-on-click-modal="false"
    @close="$emit('cancel')"
  >
    <el-form
      ref="roleForm"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="资源名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <!--        <el-form-item label="编码" prop="code">
        <el-input v-model="form.code" />
      </el-form-item>-->
      <el-form-item label="排序" prop="orderObj">
        <el-input v-model="form.orderObj" />
      </el-form-item>
      <!--        <el-form-item label="默认对象" prop="defaultObj">
        <el-radio-group v-model="form.defaultObj">
          <el-radio :value="true">
            是
          </el-radio>
          <el-radio :value="false">
            否
          </el-radio>
        </el-radio-group>
      </el-form-item>-->
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('cancel')">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleOk"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { post, put } from '@/utils/http/request.js'
import pick from 'lodash.pick'

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入资源名称' }]
  // code: [{ required: true, message: '请输入编码' }],
  // defaultObj: [{ required: true, message: '请选择是否为默认对象' }]
}

export default {
  name: 'AddAuthRoleModal',
  props: {
    model: {
      type: Object,
      default: () => null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      }
    }
    return {
      rules: rules,
      form: {
        id: '',
        name: '',
        code: '',
        orderObj: 0,
        defaultObj: false,
        remark: ''
      },
      // 控制authGroupModal显示隐藏
      visible: false,
      // 控制authGroupModal loading
      loading: false,
      roleType: null,
      parentId: '',
      title: '',
      url: {
        addAuthRole: '/voucher/s/obj',
        editAuthRole: '/voucher/s/obj'
      }
    }
  },
  watch: {
    visible: function (n, v) {
      if (n && this.model) {
        if (this.model.type === 'add') {
          this.title = '新建权限类别资源'
        } else {
          this.title = '编辑权限类别资源'
          this.form = pick(this.model.data, [
            'id',
            'name',
            'code',
            'orderObj',
            'defaultObj',
            'remark'
          ])
        }
      }
    }
  },
  methods: {
    resetForm() {
      this.roleType = null
      this.parentId = ''
      this.form = {
        name: '',
        code: '',
        orderObj: 0,
        defaultObj: false,
        remark: ''
      }
      this.$refs.roleForm.resetFields()
    },
    handleOk() {
      this.$refs.roleForm.validate(valid => {
        if (valid) {
          this.loading = true
          if (this.model.type === 'add') {
            post(this.url.addAuthRole, {
              ...this.form,
              type: this.roleType,
              parentId: this.parentId
            })
              .then(res => {
                if (res.success) {
                  this.$emit('ok', true)
                  this.visible = false
                  this.resetForm()
                } else {
                  this.$message.error(res.msg)
                }
              })
              .finally(() => {
                setTimeout(() => {
                  this.loading = false
                }, 1000)
              })
          } else {
            put(`${this.url.editAuthRole}/${this.form.id}`, {
              ...this.form,
              type: this.roleType,
              parentId: this.parentId
            })
              .then(res => {
                if (res.success) {
                  this.$emit('ok', true)
                  this.visible = false
                  this.resetForm()
                } else {
                  this.$message.error(res.msg)
                }
              })
              .finally(() => {
                setTimeout(() => {
                  this.loading = false
                }, 1000)
              })
          }
        }
      })
    }
  }
}
</script>
