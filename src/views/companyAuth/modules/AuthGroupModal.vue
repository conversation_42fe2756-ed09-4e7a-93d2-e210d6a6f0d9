<template>
  <el-dialog
    v-model="visible"
    title="设置权限类别"
    width="640px"
    :close-on-click-modal="false"
    @close="$emit('cancel')"
  >
    <el-form
      ref="ruleForm"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="组名称" prop="objectName">
            <span v-if="formType === 'detail' || modalType === 'common'">{{
              form.objectName
            }}</span>
            <el-input v-else v-model="form.objectName" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item>
            <el-radio-group
              :model-value="authTypeSel"
              @change="handleAuthTypeChange"
            >
              <el-radio-button
                v-for="auth in authTypes"
                :key="auth.code"
                :value="auth.code"
              >
                {{ auth.name }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col v-if="showNullErr" :span="20" :offset="4">
          <p style="color: #f5222d">请选择权限</p>
        </el-col>
        <el-col :span="24">
          <el-form-item>
            <el-tree
              v-model:checked-keys="checkedKeys"
              show-checkbox
              :data="treeData"
              :props="replaceFields"
              node-key="id"
              @check="onCheck"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('cancel')">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleOk"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { get } from '@/utils/http/request.js'
// import pick from 'lodash.pick'
// 表单验证规则
const rules = {
  objectName: [{ required: true, message: '请输入组名称', trigger: 'blur' }]
}

export default {
  name: 'AuthGroupModal',
  props: {
    model: {
      type: Object,
      default: () => null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 }
      }
    }
    this.formItemLayoutWithOutLabel = {
      wrapperCol: {
        xs: { span: 24, offset: 0 },
        sm: { span: 20, offset: 4 }
      }
    }
    return {
      treeData: [],
      wholeData: [],
      checkedKeys: [],
      wholeCheckedKeys: {},
      replaceFields: {
        children: 'children',
        title: 'name',
        key: 'id'
      },
      rules: rules,
      form: {
        objectName: '',
        objectId: ''
      },
      visible: false,
      loading: false,
      // 模态框处于何种业务类型 common 不分组类型 / auth 分组类型
      modalType: null,
      authTypes: [],
      authTypeSel: null,
      formType: 'add', // 默认add模式 具有add/edit/detail 三种模式
      url: {
        list: '/voucher/s/obj/type/types',
        tree: '/voucher/s/obj/companyTree'
      },
      showNullErr: false
    }
  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal) {
        this.authTypes = []
        this.treeData = []
        this.modalType = this.model.type
        this.formType = this.model.model
        const payload = this.model.payload
        if (payload) {
          // 编辑和查看详情时，没有objectName 就设置为基础权限
          if (this.modalType === 'common') {
            this.form.objectName = '基础权限'
          } else {
            this.form.objectName = payload.objectName
          }
          this.form.objectId = payload.objectId
          this.wholeCheckedKeys = payload.checkedKeys
          console.log('this.wholeCheckedKeys:', this.wholeCheckedKeys)
        } else {
          // 此时是新增权限类别 需要给基础权限组设置一个默认名
          if (this.modalType === 'common') {
            this.form.objectName = '基础权限'
          }
        }
        this.getAuthData()
      }
    },
    authTypeSel: function (newVal, oldVal) {
      if (newVal) {
        this.checkedKeys = this.wholeCheckedKeys[this.authTypeSel] || []
        console.log('this.checkedKeys:', this.checkedKeys)
      }
    }
  },
  methods: {
    handleAuthTypeChange(e) {
      this.authTypeSel = e.target.value
      this.showNullErr = false
      this.treeData = this.wholeData[this.authTypeSel] || []
      if (this.formType === 'detail') {
        this.setTreeUnchecked(this.treeData)
      }
      // this.checkedKeys = this.wholeCheckedKeys[this.authTypeSel]
    },

    onCheck(checkedKeys, info) {
      console.log('onCheck', checkedKeys, info)
      this.wholeCheckedKeys[this.authTypeSel] = checkedKeys
      this.checkedKeys = checkedKeys
      console.log(' this.wholeCheckedKeys:', this.wholeCheckedKeys)
    },
    handleOk() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          // 生成objectId
          if (this.wholeCheckedKeys) {
            if (!this.form.objectId) {
              this.form.objectId = Date.now()
            }
            const data = { ...this.form, checkedKeys: this.wholeCheckedKeys }
            this.$emit('ok', data)
          } else {
            this.showNullErr = true
          }
        }
      })
    },
    getAuthData() {
      // 00101  公共对象 00102  分组对象
      const queryType = this.modalType === 'common' ? '00101' : '00102'
      get(this.url.tree, {
        all: true,
        type: queryType
      }).then(res => {
        if (res.success) {
          this.wholeData = res.data || []
          get(`${this.url.list}?type=${queryType}`).then(res => {
            if (res.success) {
              this.authTypes = res.data || []
              this.authTypes = this.authTypes.filter(
                typeInfo => this.wholeData[typeInfo.code]
              )
              if (this.authTypes.length) {
                this.authTypeSel = this.authTypes[0].code
                this.treeData = this.wholeData[this.authTypeSel] || []
              }
              if (this.formType === 'detail') {
                this.setTreeUnchecked(this.treeData)
              }
            }
          })
        }
      })
    },
    setTreeUnchecked(data) {
      if (!data) {
        return
      }
      data.map(v => {
        v.disabled = true
        if (v.children && v.children.length > 0) {
          this.setTreeUnchecked(v.children)
        }
      })
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
      this.form.objectId = ''
      this.form.objectName = ''
      this.treeData = []
      this.wholeCheckedKeys = {}
      this.checkedKeys = []
      this.authTypeSel = null
      this.visible = false
      this.showNullErr = false
    }
  }
}
</script>
