<template>
  <el-dialog
    v-model="visible"
    title="访问控制配置"
    width="840px"
    :close-on-click-modal="false"
    @close="$emit('cancel')"
  >
    <div v-loading="loading">
      <el-row style="margin-bottom: 20px">
        所属： {{ model.interfaceName ? model.interfaceName : '全局配置' }}
      </el-row>
      <el-row>
        <el-col>
          <div class="table-operator">
            <el-button type="primary" @click="addSecurity()">
              <el-icon class="el-icon--left">
                <PlusIcon />
              </el-icon>
              添加新配置
            </el-button>
          </div>
          <el-table
            ref="table"
            v-loading="loading"
            :data="tableData"
            row-key="id"
            @selection-change="onSelectChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="#" width="80">
              <template #default="{ $index }">
                {{
                  (pagination.current - 1) * pagination.pageSize + $index + 1
                }}
              </template>
            </el-table-column>
            <el-table-column label="验证器类型" prop="handler" />
            <el-table-column label="参数" prop="param">
              <template #default="{ row }">
                <el-text class="w-150px mb-2" truncated
                  >{{ row.param }}
                </el-text>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="150">
              <template #default="{ row }">
                <a @click="editSecurity(row)">编辑</a>
                <el-divider direction="vertical" />
                <el-popconfirm
                  title="确定要删除吗?"
                  @confirm="deleteItem(row.id)"
                >
                  <template #reference>
                    <a href="#">删除</a>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            style="margin-top: 20px; text-align: right"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-col>
      </el-row>
    </div>
    <APISecurityModal
      ref="apiSecurityModal"
      :model="securityModal"
      @ok="handlerParamOk"
      @cancel="handlerParamCancel"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('cancel')">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleOk"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { Plus as PlusIcon } from '@element-plus/icons-vue'
import APISecurityModal from '@/views/companyAuth/modules/APISecurityModal.vue'
import pick from 'lodash.pick'
import { del, get } from '@/utils/http/request.js'

export default {
  name: 'APISecurityListModal',
  components: {
    APISecurityModal,
    PlusIcon
  },
  props: {
    model: {
      type: Object,
      default: () => {
        return {
          interfaceId: null,
          interfaceName: null
        }
      }
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 }
      }
    }
    this.formItemLayoutWithOutLabel = {
      wrapperCol: {
        xs: { span: 24, offset: 0 },
        sm: { span: 20, offset: 4 }
      }
    }
    return {
      visible: false,
      loading: false,
      securityModal: {},
      url: {
        list: '/voucher/s/ins/param/list',
        delete: '/voucher/s/ins/param'
      },
      // 表格数据
      tableData: [],
      // 分页配置
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      queryParam: {}
    }
  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal) {
        if (this.model.interfaceId) {
          this.queryParam.interfaceId = this.model.interfaceId
        } else {
          delete this.queryParam.interfaceId
        }
        this.refresh()
        this.securityModal = pick(this.model, ['interfaceId', 'interfaceName'])
      }
    }
  },
  methods: {
    loadData(parameter) {
      return get(this.url.list, parameter)
    },
    // 加载表格数据
    async loadTableData() {
      this.loading = true
      try {
        const parameter = {
          pageNum: this.pagination.current,
          pageSize: this.pagination.pageSize,
          ...this.queryParam
        }
        const res = await this.loadData(parameter)
        if (res && res.success) {
          this.tableData = res.data || res.result || []
          this.pagination.total = res.totalRecords || 0
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.current = 1
      this.loadTableData()
    },
    // 当前页改变
    handleCurrentChange(current) {
      this.pagination.current = current
      this.loadTableData()
    },
    // 选择改变
    onSelectChange(selection) {
      this.selectedRowKeys = selection.map(item => item.id)
      this.selectedRows = selection
    },
    // 重写 refresh 方法
    refresh() {
      this.loadTableData()
    },
    addSecurity() {
      this.securityModal.form = { id: null }
      this.$refs.apiSecurityModal.visible = true
    },
    editSecurity(record) {
      this.securityModal.form = record
      this.$refs.apiSecurityModal.visible = true
    },
    handlerParamOk() {
      this.$refs.apiSecurityModal.visible = false
      this.$refs.apiSecurityModal.resetForm()
      this.refresh()
    },
    handlerParamCancel() {
      this.securityModal.form = null
      this.$refs.apiSecurityModal.visible = false
    },
    handleOk() {
      this.securityModal.form = null
      this.visible = false
    },
    deleteItem(id) {
      return new Promise((resolve, reject) => {
        if (!this.url.delete) {
          this.$message.warning('未设置url->delete')
          reject(new Error('未设置url->delete'))
          return
        }
        del(`${this.url.delete}/${id}`).then(res => {
          if (res.success) {
            this.$message.success('删除成功')
            // 刷新表格
            this.refresh()
            resolve()
          } else {
            this.$message.warning(res.msg)
            reject(new Error('删除失败！'))
          }
        })
      })
    }
  }
}
</script>
