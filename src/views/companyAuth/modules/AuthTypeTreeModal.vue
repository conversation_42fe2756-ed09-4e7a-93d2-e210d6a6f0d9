<template>
  <el-dialog
    v-model="visible"
    title="添加其他类别资源"
    width="640px"
    :close-on-click-modal="false"
    @close="$emit('cancel')"
  >
    <div v-loading="loading">
      <div>
        <dynamic-auth-group
          class="my-auth-group"
          :exclude="exclude"
          @curSel="getSelectTreeData"
        />
        <el-transfer
          v-model="targetKeys"
          class="tree-transfer"
          :data="dataSource"
          :titles="['可选项', '已选项']"
          :render-content="renderContent"
          @change="onChange"
        >
          <template #default="{ direction }">
            <el-tree
              v-if="direction === 'left'"
              :data="treeData"
              show-checkbox
              check-strictly
              default-expand-all
              :props="replaceFields"
              :checked-keys="[...targetKeys]"
              node-key="id"
              @check="onTreeCheck"
            />
          </template>
        </el-transfer>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('cancel')">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleOk"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import DynamicAuthGroup from '../dynamic-auth-group/index.vue'
import { post } from '@/utils/http/request.js'

function isChecked(selectedKeys, eventKey) {
  return selectedKeys.indexOf(eventKey) !== -1
}

export default {
  name: 'AuthTypeTreeModal',
  components: { DynamicAuthGroup },
  props: {
    model: {
      type: Object,
      default: () => null
    },
    parentId: {
      type: String,
      default: ''
    },
    exclude: {
      type: Array,
      default: () => []
    },
    vdata: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      targetKeys: [],
      dataSource: [],
      visible: false,
      loading: false,
      curAuthType: null,
      replaceFields: {
        key: 'id',
        title: 'name'
      },
      url: {
        save: '/voucher/s/obj/relationSave'
      }
    }
  },
  computed: {
    treeData() {
      if (this.curAuthType && Object.keys(this.vdata).length > 0) {
        const t = this.vdata[this.curAuthType] || []
        return t
      } else {
        return []
      }
    }
  },
  watch: {
    treeData: function (n, v) {
      if (n && n.length > 0) {
        this.dataSource = []
        this.flatten(JSON.parse(JSON.stringify(n)))
      }
    }
  },
  methods: {
    flatten(list = []) {
      list.map(v => {
        v.title = v.name
        v.key = v.id
        v.description = ''
        v.disabled = false
        this.dataSource.push(v)
        this.flatten(v.children || [])
      })
    },
    onChange(targetKeys) {
      this.targetKeys = targetKeys
    },
    onTreeCheck(checkedKeys, checkedNodes) {
      // 处理树形选择逻辑
      this.targetKeys = checkedKeys
    },
    renderContent(h, option) {
      return option.title
    },
    resetForm() {
      this.targetKeys = []
      this.dataSource = []
      this.curAuthType = null
      this.visible = false
    },
    getSelectTreeData($event) {
      this.curAuthType = $event
    },
    handleOk() {
      this.loading = true
      post(this.url.save, {
        parentId: this.parentId,
        children: this.targetKeys
      })
        .then(res => {
          if (res.success) {
            this.$message.success(res.msg)
            this.$emit('ok', true)
            this.visible = false
            this.resetForm()
          } else {
            this.$message.error(res.msg)
          }
        })
        .finally(() => {
          setTimeout(() => {
            this.loading = false
          }, 1000)
        })
    }
  }
}
</script>
<style lang="less">
.tree-transfer .ant-transfer-list:first-child {
  width: 48%;
  flex: none;
}
</style>
