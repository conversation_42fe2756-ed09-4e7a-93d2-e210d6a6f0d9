<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="540px"
    :close-on-click-modal="false"
    @close="$emit('cancel')"
  >
    <el-form
      ref="userForm"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="姓名" prop="fullName">
            <span class="ant-span">{{ form.fullName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="账号" prop="username">
            <span class="ant-span">{{ form.username }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="新密码" prop="password">
            <el-input
              v-model="form.password"
              type="password"
              autocomplete="new-password"
              show-password
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('cancel')">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleOk"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import pick from 'lodash.pick'
import md5 from 'md5'
import { post } from '@/utils/http/request.js'

// 表单验证规则
const rules = {
  password: [{ required: true, message: '请输入密码' }]
}

export default {
  name: 'ResetPwdModal',
  props: {
    model: {
      type: Object,
      default: () => null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 }
      }
    }
    return {
      rules: rules,
      layout: 'inline',
      form: {
        fullName: '',
        username: '',
        password: '123456'
      },
      visible: false,
      loading: false,
      // 存储当前编辑的userId
      userId: '',
      // 当前编辑或查看的权限组数据
      title: '重置用户密码',
      url: {
        reset: '/voucher/s/user/updateUserAndAuth'
      }
    }
  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal) {
        if (this.model.data) {
          // 将data中的
          const user = this.model.data.user
          this.userId = user.userId
          this.form = {
            fullName: user.fullName,
            username: user.username,
            password: '123456'
          }
          console.log('this.form:', this.form)
        }
      }
    }
  },
  methods: {
    resetForm() {
      this.form = {
        fullName: '',
        username: '',
        password: '123456'
      }
      this.userId = ''
      this.modalType = null
      this.$refs.userForm.resetFields()
      this.visible = false
    },
    handleOk() {
      this.$refs.userForm.validate(valid => {
        if (valid) {
          this.loading = true
          const params = {}
          params.user = {}
          if (this.userId) {
            params.user = pick(this.form, ['password'])
            params.user.password = md5(params.user.password)
            post(`${this.url.reset}/${this.userId}`, params)
              .then(res => {
                if (res.success) {
                  this.$message.success('重置成功!')
                  this.resetForm()
                  this.$emit('ok')
                } else {
                  this.$message.error(res.msg)
                }
              })
              .finally(e => {
                setTimeout(() => {
                  this.loading = false
                }, 1000)
              })
          }
        }
      })
    }
  }
}
</script>
