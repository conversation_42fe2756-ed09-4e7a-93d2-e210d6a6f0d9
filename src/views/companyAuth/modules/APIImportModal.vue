<template>
  <el-dialog
    v-model="visible"
    title="导入swagger配置"
    width="440px"
    :close-on-click-modal="false"
    @close="$emit('cancel')"
  >
    <el-form
      ref="ruleForm"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-row>
        <el-col>
          <el-form-item label="分组名称" prop="groupName">
            <el-input v-model="form.groupName" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="API地址" prop="url">
            <el-input v-model="form.url" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('cancel')">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleOk"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { get } from '@/utils/http/request.js'

const rules = {
  url: [{ required: true, message: '请输入文档路径', trigger: 'blur' }],
  groupName: [{ required: true, message: '请输入分组名称', trigger: 'blur' }]
}

export default {
  name: 'APIImportModal',
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 }
      }
    }
    this.formItemLayoutWithOutLabel = {
      wrapperCol: {
        xs: { span: 24, offset: 0 },
        sm: { span: 20, offset: 4 }
      }
    }
    return {
      rules: rules,
      form: {
        url: '',
        groupName: ''
      },
      visible: false,
      loading: false,
      url: {
        transformApi: '/voucher/s/ins/transformApi'
      }
    }
  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal) {
      }
    }
  },
  methods: {
    handleOk() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          get(this.url.transformApi, { data: this.form }).then(res => {
            if (res.success) {
              this.$message.success(res.msg)
              this.$emit('ok')
              this.visible = false
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    resetForm() {}
  }
}
</script>
