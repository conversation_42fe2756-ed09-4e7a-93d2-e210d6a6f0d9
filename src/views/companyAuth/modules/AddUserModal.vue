<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="640px"
    destroy-on-close
    :close-on-click-modal="false"
    @close="$emit('cancel')"
  >
    <el-form
      ref="userForm"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-width="100px"
      :class="{ 'detail-form': modalType === 'detail' }"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="姓名" prop="fullName">
            <span v-if="modalType === 'detail'" class="ant-span">{{
              form.fullName
            }}</span>
            <el-input v-else v-model="form.fullName" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="sex">
            <span v-if="modalType === 'detail'" class="ant-span">{{
              form.sex
            }}</span>
            <el-select v-else v-model="form.sex" placeholder="请选择">
              <el-option value="男" label="男" />
              <el-option value="女" label="女" />
            </el-select>
          </el-form-item>
        </el-col>
        <!--          <a-col :span="8">
            <a-form-model-item label="电话" prop="phone">
              <span class="ant-span" v-if="modalType == 'detail'">{{ form.phone }}</span>
              <a-input v-model="form.phone" v-else />
            </a-form-model-item>
          </a-col>-->
        <el-col :span="12">
          <el-form-item label="账号" prop="username">
            <span v-if="modalType === 'detail'" class="ant-span">{{
              form.username
            }}</span>
            <el-input v-else v-model="form.username" />
          </el-form-item>
        </el-col>
        <el-col v-if="modalType === 'add'" :span="12">
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="form.password"
              type="password"
              autocomplete="new-password"
              show-password
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话" prop="phone">
            <span v-if="modalType === 'detail'" class="ant-span">{{
              form.phone
            }}</span>
            <el-input v-else v-model="form.phone" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <span v-if="modalType === 'detail'" class="ant-span">{{
              form.email
            }}</span>
            <el-input v-else v-model="form.email" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="身份证" prop="idCard">
            <span v-if="modalType === 'detail'" class="ant-span">{{
              form.idCard
            }}</span>
            <el-input v-else v-model="form.idCard" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <span v-if="modalType === 'detail'" class="ant-span">{{
              form.remark
            }}</span>
            <el-input v-else v-model="form.remark" type="textarea" />
          </el-form-item>
        </el-col>
      </el-row>
      <edit-user-auth
        v-model:value="currentObjData"
        :show-edit="modalType !== 'detail'"
      />

      <el-row>
        <el-col :span="24">
          <el-form-item />
        </el-col>
      </el-row>
    </el-form>
    <auth-group-modal
      ref="authGroupModal"
      :model="curDomain"
      @ok="handleAuthGroupModalOk"
      @cancel="handleAuthGroupModalCancel"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('cancel')">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleOk"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import pick from 'lodash.pick'
import AuthGroupModal from './AuthGroupModal.vue'
import editUserAuth from './editUserAuth.vue'
import md5 from 'md5'
import { post, put } from '@/utils/http/request.js'

// 表单验证规则
const rules = {
  fullName: [{ required: true, message: '请输入姓名' }],
  username: [{ required: true, message: '请输入用户名' }],
  password: [{ required: true, message: '请输入密码' }],
  sex: [{ required: true, message: '请选择性别' }],
  privateAuths: [{ required: false, message: '请设置权限组' }],
  publicAuths: [{ required: true, message: '请设置基础权限组' }]
}

export default {
  name: 'AddUserModal',
  components: {
    AuthGroupModal,
    editUserAuth
  },
  props: {
    model: {
      type: Object,
      default: () => null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 }
      }
    }
    this.formItemLayoutWithOutLabel = {
      wrapperCol: {
        xs: { span: 24, offset: 0 },
        sm: { span: 12, offset: 4 }
      }
    }
    this.formItemAuthGroupLayout = {
      labelCol: {
        xs: { span: 24, offset: 0 },
        sm: { span: 4, offset: 0 }
      },
      wrapperCol: {
        xs: { span: 24, offset: 0 },
        sm: { span: 19, offset: 0 }
      }
    }
    return {
      rules: rules,
      layout: 'inline',
      form: {
        fullName: '',
        sex: '男',
        username: '',
        password: '123456',
        phone: '',
        email: '',
        remark: '',
        idCard: '',
        publicAuths: [],
        privateAuths: []
      },
      visible: false,
      loading: false,
      modalType: null,
      // 存储当前编辑的userId
      userId: '',
      // 当前编辑或查看的权限组数据
      curDomain: {
        model: 'add',
        payload: null
      },
      title: '新建用户',
      url: {
        add: '/voucher/s/user/companyUserAuth',
        edit: userId => `/voucher/s/user/companyUserAuth/${userId}`
      },
      currentObjData: {}
    }
  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal) {
        this.modalType = this.model.type
        if (this.modalType === 'add') {
          this.title = '新建用户'
        } else if (this.modalType === 'edit') {
          this.title = '编辑权限'
        } else {
          this.title = '用户详情'
        }
        if (this.model.data) {
          // 将data中的
          const user = this.model.data.user
          const auth = this.model.data.auth
          const tt = []
          tt.push({
            checkedKeys: auth ? auth.publicAuths : [],
            objectId: '',
            objectName: '基础权限'
          })
          this.userId = user.userId
          this.form = {
            fullName: user.fullName,
            sex: user.sex,
            username: user.username,
            password: '',
            phone: user.phone,
            email: user.email,
            remark: user.remark,
            idCard: user.idCard,
            publicAuths: tt,
            privateAuths: auth
              ? auth.privateAuths.map(v => {
                  const t = {
                    checkedKeys: v.auths,
                    objectId: v.groupId,
                    objectName: v.groupName
                  }
                  return t
                })
              : []
          }
          console.log('this.form:', this.form)
          const currentObjData = {}
          auth.privateAuths.forEach(auth => {
            const auths = auth.auths
            for (const authElement in auths) {
              const data = []
              auths[authElement].forEach(current => {
                data.push(current.objectId)
              })
              currentObjData[authElement] = data
            }
          })
          this.currentObjData = currentObjData
        }
      } else {
        this.resetForm()
      }
    }
  },
  methods: {
    addAuthGroup(type) {
      this.curDomain = {
        model: 'add',
        type: type,
        payload: null
      }
      this.$refs.authGroupModal.visible = true
    },
    editAuthGroup(event, type, item) {
      event.stopPropagation()
      this.curDomain = {
        model: 'edit',
        type: type,
        payload: item
      }
      this.$refs.authGroupModal.visible = true
    },
    removeAuthGroup(event, type, item) {
      event.stopPropagation()
      if (type === 'common') {
        this.form.publicAuths = []
        return
      }
      const index = this.form.privateAuths.indexOf(item)
      if (index !== -1) {
        this.form.privateAuths.splice(index, 1)
      }
    },
    resetForm() {
      this.visible = false
      this.form = {
        fullName: '',
        sex: '男',
        username: '',
        password: '123456',
        phone: '',
        email: '',
        remark: '',
        idCard: '',
        publicAuths: [],
        privateAuths: []
      }
      this.userId = ''
      this.modalType = null
      this.$refs.userForm.resetFields()
      this.currentObjData = {}
    },
    handleAuthGroupChange(value, type) {
      let data = []
      if (type === 'auth') {
        data = this.form.privateAuths.filter(v => v.objectId === value)
      } else {
        data = this.form.publicAuths
      }
      if (data.length > 0) {
        this.curDomain = {
          model: 'detail',
          type: type,
          payload: data[0]
        }
        this.$refs.authGroupModal.visible = true
      }
    },
    handleAuthGroupModalCancel() {
      if (this.curDomain) {
        this.curDomain = null
      }
      this.handleCancel('authGroupModal')
    },
    handleAuthGroupModalOk(data) {
      // 处理权限组
      console.log('this.curDomain:', this.curDomain)

      if (this.curDomain.type === 'auth') {
        if (this.curDomain.model !== 'detail') {
          if (this.curDomain.model === 'edit') {
            this.form.privateAuths = this.form.privateAuths.map(v => {
              if (v.objectId === data.objectId) {
                v = { ...data }
              }
              return v
            })
          } else {
            this.form.privateAuths.push(data)
          }
        }
      } else {
        // 处理基础权限组，由于基础权限只有一个，故无论是新增、查看、编辑均采用以下操作
        this.form.publicAuths = []
        this.form.publicAuths.push(data)
      }

      this.curDomain = null
      this.$refs.authGroupModal.resetForm()
      this.$refs.authGroupModal.visible = false
    },
    handleOk() {
      console.log('handleOk', this.currentObjData)
      /* if (true) {
        return
      } */
      this.$refs.userForm.validate(valid => {
        if (valid) {
          this.loading = true
          const params = {
            auth: {
              privateAuths: [
                {
                  auths: this.currentObjData
                }
              ]
            }
          }
          params.user = pick(this.form, [
            'fullName',
            'sex',
            'username',
            'password',
            'phone',
            'email',
            'remark',
            'idCard'
          ])
          if (this.userId) {
            // params.user = pick(this.form, ['fullName', 'sex', 'username', 'phone', 'email', 'remark', 'idCard'])
            put(this.url.edit(this.userId), params)
              .then(res => {
                if (res.success) {
                  this.$message.success('修改成功!')
                  this.resetForm()
                  this.$emit('ok')
                } else {
                  this.$message.error(res.msg)
                }
              })
              .finally(e => {
                setTimeout(() => {
                  this.loading = false
                }, 1000)
              })
          } else {
            params.user.password = md5(params.user.password)
            post(this.url.add, params)
              .then(res => {
                if (res.success) {
                  this.$message.success('操作成功!')
                  this.resetForm()
                  this.$emit('ok')
                } else {
                  this.$message.error(res.msg)
                }
              })
              .finally(e => {
                setTimeout(() => {
                  this.loading = false
                }, 1000)
              })
          }
        }
      })
    },
    handleCommonAuthChange() {
      this.editAuthGroup(this.form.publicAuths[0])
    }
  }
}
</script>
