<template>
  <el-dialog
    v-model="visible"
    title="访问控制配置"
    :style="{ top: '110px' }"
    :width="540"
    @close="
      () => {
        $emit('cancel')
      }
    "
  >
    <div v-loading="loading">
      <el-form
        ref="ruleForm"
        :label-width="formLayout.labelCol.sm.span * 12 + 'px'"
        :model="form"
        :rules="rules"
      >
        <el-row>
          <el-form-item label="所属">
            {{ model.interfaceName ? model.interfaceName : '全局配置' }}
          </el-form-item>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="控制类型" prop="handler">
              <el-radio-group v-model="form.handler">
                <el-radio-button v-if="model.interfaceId" value="visitor">
                  访客
                </el-radio-button>
                <el-radio-button v-if="model.interfaceId" value="pass">
                  不验证
                </el-radio-button>
                <el-radio-button value="black"> 黑名单</el-radio-button>
                <el-radio-button value="white"> 白名单</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="参数">
              <el-input
                v-model="form.param"
                :rows="4"
                placeholder="验证参数，IP请一行一个"
                type="textarea"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('cancel')">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="handleOk"
          >确 定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { post, put } from '@/utils/http/request.js'
import { ElMessage } from 'element-plus'

const rules = {
  handler: [{ required: true, message: '请选择控制类型', trigger: 'blur' }]
}

export default {
  name: 'APISecurityModal',
  props: {
    model: {
      type: Object,
      default: () => {
        return {
          interfaceId: null,
          interfaceName: null,
          form: {
            id: null,
            handler: '',
            param: ''
          }
        }
      }
    }
  },
  data() {
    return {
      rules: rules,
      id: null,
      form: {
        handler: '',
        interfaceId: '',
        param: ''
      },
      visible: false,
      loading: false,
      url: {
        saveParam: '/voucher/s/ins/param',
        editParam: '/voucher/s/ins/param'
      },
      formLayout: {
        // Keep for reference, but el-form handles layout differently
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 14 }
        }
      }
    }
  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal) {
        console.log('model', this.model)
        this.id = this.model.form.id
        this.form.interfaceId = this.model.interfaceId
        this.form.handler = this.model.form.handler
        this.form.param = this.model.form.param
      }
    }
  },
  methods: {
    handleOk() {
      this.$refs.ruleForm.validate(valid => {
        console.log('handleOk.valid', valid)
        if (valid) {
          this.loading = true
          if (this.id) {
            put(`${this.url.editParam}/${this.id}`, this.form)
              .then(res => {
                if (res.success) {
                  ElMessage.success(res.msg)
                  this.resetForm()
                  this.$emit('ok')
                } else {
                  ElMessage.error(res.msg)
                }
              })
              .finally(() => {
                this.loading = false
              })
          } else {
            post(this.url.saveParam, this.form)
              .then(res => {
                if (res.success) {
                  ElMessage.success(res.msg)
                  this.resetForm()
                  this.$emit('ok')
                } else {
                  ElMessage.error(res.msg)
                }
              })
              .finally(() => {
                this.loading = false
              })
          }
        }
      })
    },
    resetForm() {
      this.form.handler = ''
      this.form.interfaceId = ''
      this.form.param = ''
      this.form.id = null
      this.$refs.ruleForm.resetFields()
    }
  }
}
</script>
