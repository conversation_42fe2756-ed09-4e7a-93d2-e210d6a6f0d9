<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="540px"
    :close-on-click-modal="false"
    @close="$emit('cancel')"
  >
    <el-form
      ref="ruleForm"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-row>
        <el-col>
          <el-form-item label="接口名称" prop="name">
            <el-input v-model="form.name" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="请求方法" prop="method">
            <el-radio-group v-model="form.method">
              <el-radio-button value="GET">GET</el-radio-button>
              <el-radio-button value="POST">POST</el-radio-button>
              <el-radio-button value="PUT">PUT</el-radio-button>
              <el-radio-button value="DELETE">DELETE</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="请求地址" prop="url">
            <el-input v-model="form.url" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="是否启用" prop="enable">
            <el-switch v-model="form.enable" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('cancel')">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleOk"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { post, put } from '@/utils/http/request.js'
import pick from 'lodash.pick'

const rules = {
  method: [{ required: true, message: '请选择请求方法', trigger: 'blur' }],
  url: [{ required: true, message: '请输入请求地址', trigger: 'blur' }],
  name: [{ required: true, message: '请输入接口名称', trigger: 'blur' }],
  enable: [{ required: true, message: '请选择是否启用', trigger: 'blur' }]
}

export default {
  name: 'InterfaceModal',
  props: {
    model: {
      type: Object,
      default: () => {
        return {
          id: null,
          parentId: null
        }
      }
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 }
      }
    }
    this.formItemLayoutWithOutLabel = {
      wrapperCol: {
        xs: { span: 24, offset: 0 },
        sm: { span: 20, offset: 4 }
      }
    }
    return {
      rules: rules,
      title: '新建接口信息',
      id: null,
      form: {
        method: 'GET',
        url: null,
        name: null,
        enable: true
      },
      visible: false,
      loading: false,
      authTypeSel: null,
      commitNew: true,
      url: {
        group: '/voucher/s/ins'
      },
      showNullErr: false
    }
  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal) {
        this.id = this.model.id
        if (this.id) {
          this.form = pick(this.model, [
            'method',
            'url',
            'name',
            'parentId',
            'enable'
          ])
        } else {
          this.form.parentId = this.model.parentId
        }
      }
    }
  },
  methods: {
    handleOk() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          if (!this.form.sort) {
            this.form.sort = 100
          }
          if (this.id) {
            put(`${this.url.group}/${this.id}`, this.form)
              .then(res => {
                if (res.success) {
                  this.$message.success(res.msg)
                  this.resetForm()
                  this.$emit('ok')
                } else {
                  this.$message.error(res.msg)
                }
              })
              .finally(e => {
                setTimeout(() => {
                  this.loading = false
                }, 1000)
              })
          } else {
            post(this.url.group, this.form).then(res => {
              if (res.success) {
                this.$message.success(res.msg)
                this.resetForm()
                this.$emit('ok')
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        }
      })
    },
    resetForm() {
      this.id = ''
      this.form.method = 'GET'
      this.form.url = null
      this.form.name = null
      this.form.enable = true
    }
  }
}
</script>
