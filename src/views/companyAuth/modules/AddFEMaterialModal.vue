<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="640px"
    :close-on-click-modal="false"
    @close="$emit('cancel')"
  >
    <el-form
      ref="feResourceForm"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="资源类型" prop="type">
            <span v-if="model.type === 'detail'" class="form-read-only">
              {{ form.type === '00015' ? '菜单' : '按钮' }}
            </span>
            <template v-else>
              <el-radio-group
                v-model="form.type"
                :disabled="model.type === 'add' && !model.parentId"
              >
                <el-radio value="00015"> 菜单 </el-radio>
                <el-radio value="00019"> 按钮 </el-radio>
              </el-radio-group>
            </template>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资源名称" prop="name">
            <span v-if="model.type === 'detail'" class="form-read-only">{{
              form.name
            }}</span>
            <el-input v-else v-model="form.name" />
          </el-form-item>
        </el-col>
        <template v-if="form.type === '00019'">
          <el-col :span="12">
            <el-form-item label="控件标识" prop="key">
              <span v-if="model.type === 'detail'" class="form-read-only">{{
                form.key
              }}</span>
              <el-input v-else v-model="form.key" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="父级菜单" prop="parentId">
              <el-tree-select
                v-model="form.parentId"
                filterable
                style="width: 100%"
                placeholder="Please select"
                clearable
                default-expand-all
                :data="treeData"
                :props="{
                  children: 'children',
                  label: 'title',
                  value: 'id'
                }"
                @change="onMenuChange"
              />
            </el-form-item>
          </el-col>
        </template>
        <template v-else-if="form.type === '00015'">
          <el-col :span="12">
            <el-form-item label="菜单路径" prop="path">
              <span v-if="model.type === 'detail'" class="form-read-only">{{
                form.path
              }}</span>
              <el-input v-else v-model="form.path" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="父级菜单" prop="parentId">
              <el-tree-select
                v-model="form.parentId"
                filterable
                style="width: 100%"
                placeholder="Please select"
                clearable
                default-expand-all
                :data="treeData"
                :props="{
                  children: 'children',
                  label: 'title',
                  value: 'id'
                }"
                @change="onMenuChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="前端组件" prop="component">
              <span v-if="model.type === 'detail'" class="form-read-only">{{
                form.component
              }}</span>
              <el-input v-else v-model="form.component" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单路由名" prop="key">
              <span v-if="model.type === 'detail'" class="form-read-only">{{
                form.key
              }}</span>
              <el-input v-else v-model="form.key" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="默认路由" prop="redirect">
              <span v-if="model.type === 'detail'" class="form-read-only">{{
                form.redirect
              }}</span>
              <el-input v-else v-model="form.redirect" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单图标" prop="icon">
              <span v-if="model.type === 'detail'" class="form-read-only">{{
                form.icon
              }}</span>
              <el-input v-else v-model="form.icon" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <span v-if="model.type === 'detail'" class="form-read-only">{{
                form.sort
              }}</span>
              <el-input v-else v-model="form.sort" type="number" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="游客菜单" prop="visit">
              <span v-if="model.type === 'detail'" class="form-read-only">{{
                form.visit ? '是' : '否'
              }}</span>
              <el-radio-group v-else v-model="form.visit">
                <el-radio :value="true"> 是 </el-radio>
                <el-radio :value="false"> 否 </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </template>
        <el-col :span="12">
          <el-form-item label="是否启用" prop="enable">
            <span v-if="model.type === 'detail'" class="form-read-only">{{
              form.enable ? '已启用' : '已停用'
            }}</span>
            <el-radio-group v-else v-model="form.enable">
              <el-radio :value="true"> 是 </el-radio>
              <el-radio :value="false"> 否 </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否可见" prop="hidden">
            <span v-if="model.type === 'detail'" class="form-read-only">{{
              form.hidden ? '隐藏' : '可见'
            }}</span>
            <el-radio-group v-else v-model="form.hidden">
              <el-radio :value="false"> 是 </el-radio>
              <el-radio :value="true"> 否 </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('cancel')">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleOk"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { get, post, put } from '@/utils/http/request.js'
import pick from 'lodash.pick'

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入资源名称' }],
  type: [{ required: true, message: '请选择资源类型' }],
  component: [{ required: true, message: '请输入资源名称' }],
  key: [{ required: true, message: '请输入资源名称' }]
}

export default {
  name: 'AddFEMaterialModal',
  props: {
    model: {
      type: Object,
      default: () => null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      }
    }
    return {
      rules: rules,
      form: {
        name: '',
        type: '00015',
        path: '',
        component: '',
        key: '',
        redirect: '',
        icon: '',
        sort: 1,
        visit: false,
        enable: true,
        hidden: false
      },
      // 控制authGroupModal显示隐藏
      visible: false,
      // 控制authGroupModal loading
      loading: false,
      roleType: null,
      title: '',
      url: {
        addResource: '/voucher/s/res',
        editResource: '/voucher/s/res'
      },
      treeData: []
    }
  },
  watch: {
    visible: function (n, v) {
      if (n && this.model) {
        this.setFormData()
      }
    }
  },
  mounted() {
    this.getMenu()
  },
  methods: {
    resetForm() {
      this.roleType = null
      this.parentId = ''
      this.form = {
        name: '',
        type: '00015',
        path: '',
        component: '',
        key: '',
        redirect: '',
        icon: '',
        parentId: null,
        sort: 1,
        visit: false,
        enable: true,
        hidden: false
      }
      this.$refs.feResourceForm.resetFields()
    },
    handleOk() {
      if (this.model.type === 'detail') {
        this.visible = false
        this.resetForm()
        this.$emit('ok')
        return
      }
      this.$refs.feResourceForm.validate(valid => {
        if (valid) {
          this.loading = true
          const t = pick(this.form, [
            'name',
            'type',
            'sort',
            'visit',
            'enable',
            'hidden',
            'key',
            'parentId'
          ])
          const t1 = pick(this.form, ['path', 'component', 'redirect', 'icon'])
          const attributes = []
          Object.keys(t1).forEach(v => {
            attributes.push({
              key: v,
              value: t1[v]
            })
          })
          if (!t.parentId) {
            t.parentId = this.model.parentId
          }
          if (this.model.type === 'add' || this.model.type === 'copy') {
            post(this.url.addResource, { ...t, attributes })
              .then(res => {
                if (res.success) {
                  this.$emit('ok', true)
                  this.visible = false
                  this.resetForm()
                } else {
                  this.$message.error(res.msg)
                }
              })
              .finally(() => {
                setTimeout(() => {
                  this.loading = false
                }, 1000)
              })
          } else {
            put(`${this.url.editResource}/${this.model.data.id}`, {
              ...t,
              attributes
            })
              .then(res => {
                if (res.success) {
                  this.$emit('ok', true)
                  this.visible = false
                  this.resetForm()
                } else {
                  this.$message.error(res.msg)
                }
              })
              .finally(() => {
                setTimeout(() => {
                  this.loading = false
                }, 1000)
              })
          }
        }
      })
    },
    setFormData() {
      if (this.model.type === 'add') {
        this.title = '新建前端资源'
        this.form.parentId = this.model.parentId
      } else {
        // 1.从data中拿到attributes
        const attributes = this.model.data.attributes
        // 2.从attributes 拿到path', 'component', 'redirect', 'icon
        attributes.forEach(v => {
          this.form[v.key] = v.value
        })
        const t = pick(this.model.data, [
          'name',
          'type',
          'sort',
          'visit',
          'enable',
          'hidden',
          'key',
          'parentId'
        ])
        this.form = Object.assign(this.form, t)
        console.log('this.form:', this.form)
        if (this.model.type === 'edit') {
          this.title = '编辑前端资源'
        } else {
          this.title = '前端资源详情'
        }
      }
    },
    onMenuChange(_value) {
      console.log('change', _value)
      this.form.parentId = _value
    },
    getMenu() {
      get('/voucher/s/res/all').then(res => {
        if (res && res.success) {
          this.treeData = res.data
        }
      })
    }
  }
}
</script>
