<template>
  <el-dialog
    v-model="visible"
    title="权限类型查询参数选择"
    width="640px"
    :close-on-click-modal="false"
    @close="$emit('cancel')"
  >
    <el-form
      ref="ruleForm"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item>
            <el-radio-group
              :model-value="authTypeSel"
              @change="handleAuthTypeChange"
            >
              <el-radio-button
                v-for="auth in authTypes"
                :key="auth.code"
                :value="auth.code"
              >
                {{ auth.name }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item>
            <el-tree
              v-model:checked-keys="checkedKeys"
              show-checkbox
              :data="treeData"
              :props="replaceFields"
              node-key="id"
              @check="onCheck"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('cancel')">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleOk"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
// 表单验证规则
import { get } from '@/utils/http/request.js'

const rules = {}

export default {
  name: 'AuthParamModal',
  props: {
    model: {
      type: Object,
      default: () => null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 }
      }
    }
    this.formItemLayoutWithOutLabel = {
      wrapperCol: {
        xs: { span: 24, offset: 0 },
        sm: { span: 20, offset: 4 }
      }
    }
    return {
      treeData: [],
      wholeData: [],
      checkedKeys: [],
      wholeCheckedKeys: {},
      checkedInfos: {},
      replaceFields: {
        children: 'children',
        title: 'name',
        key: 'id'
      },
      rules: rules,
      form: {},
      visible: false,
      loading: false,
      authTypes: [],
      authTypeSel: null,
      url: {
        list: '/voucher/s/obj/type/types',
        tree: '/voucher/s/obj/companyTree'
      }
    }
  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal) {
        // 清空
        this.wholeCheckedKeys = {}
        this.checkedKeys = {}
        // 保存已选择数据
        this.checkedInfos = this.model.checkedInfos
        // 已选择数据放入
        for (const type in this.model.checkedInfos) {
          if (this.model.checkedInfos.hasOwnProperty(type)) {
            const keys = []
            const typeValue = this.model.checkedInfos[type]
            for (const key in typeValue) {
              if (typeValue.hasOwnProperty(key)) {
                keys.push(key)
              }
            }
            this.wholeCheckedKeys[type] = keys
          }
        }
        console.log('checked', this.model.checkedInfos, this.wholeCheckedKeys)
        // 获得上一次选择的type，没有就取第一个
        this.authTypeSel = this.model.authTypeSel
        if (!this.authTypeSel) {
          this.authTypeSel = this.authTypes[0].code
        }
        // 初始化
        this.checkedKeys = this.wholeCheckedKeys[this.authTypeSel] || []
        this.treeData = this.wholeData[this.authTypeSel] || []
      }
    },
    authTypeSel: function (newVal, oldVal) {
      if (newVal) {
        this.checkedKeys = this.wholeCheckedKeys[this.authTypeSel] || []
        console.log('checkedKeys', this.checkedKeys)
      }
    }
  },
  mounted() {
    this.getAuthData()
  },
  methods: {
    handleAuthTypeChange(e) {
      this.authTypeSel = e.target.value
      this.treeData = this.wholeData[this.authTypeSel] || []
    },

    onCheck(checkedKeys, info) {
      if (!this.checkedInfos[this.authTypeSel]) {
        this.checkedInfos[this.authTypeSel] = {}
      }

      if (info.checked) {
        this.checkedInfos[this.authTypeSel][info.node.eventKey] =
          info.node.title
      } else {
        delete this.checkedInfos[this.authTypeSel][info.node.eventKey]
      }

      this.wholeCheckedKeys[this.authTypeSel] = checkedKeys
      this.checkedKeys = checkedKeys
    },
    handleOk() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.$emit('ok', {
            authTypeSel: this.authTypeSel,
            checkedInfos: this.checkedInfos
          })
        }
      })
    },
    getAuthData() {
      get(this.url.tree, {
        all: true
      }).then(res => {
        if (res.success) {
          this.wholeData = res.data || []
          get(`${this.url.list}`).then(res => {
            if (res.success) {
              this.authTypes = res.data || []
              if (this.authTypes.length) {
                this.authTypes = this.authTypes.filter(
                  typeInfo => this.wholeData[typeInfo.code]
                )

                if (!this.authTypeSel && this.authTypes.length) {
                  this.authTypeSel = this.authTypes[0].code
                  this.treeData = this.wholeData[this.authTypeSel] || []
                }
              }
            }
          })
        }
      })
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
      this.form.objectId = ''
      this.form.objectName = ''
      this.treeData = []
      this.wholeCheckedKeys = {}
      this.checkedKeys = []
      this.authTypeSel = null
      this.visible = false
    }
  }
}
</script>
