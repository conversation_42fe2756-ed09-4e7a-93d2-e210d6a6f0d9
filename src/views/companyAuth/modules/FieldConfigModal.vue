<template>
  <el-dialog
    v-model="visible"
    title="API配置"
    width="440px"
    :close-on-click-modal="false"
    @close="$emit('cancel')"
  >
    <div v-loading="loading">
      <gj-directory-tree
        v-model:checkedKeys="apiCheckedKeys"
        checkable
        :selectable="false"
        :treeData="apiData"
        :defaultExpandParent="true"
        :replace-fields="replaceFields"
        @check="handleCheck"
      />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('cancel')">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleOk"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import GjDirectoryTree from '../Tree/GjDirectoryTree.vue'
import ApiTreeUtil from '../util/apiTreeUtil'
import { get, post } from '@/utils/http/request.js'
export default {
  name: 'FieldConfigModal',
  components: { GjDirectoryTree },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    model: {
      type: Object,
      default: () => {
        return {
          id: null
        }
      }
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      replaceFields: {
        label: 'name',
        children: 'children'
      },
      checkedAction: false,
      apiData: [],
      apiCheckedKeys: [],
      url: {
        apiUrl: '/voucher/s/ins/group/tree?all=true',
        apiSave: '/voucher/s/res/relation',
        apiGet: '/voucher/s/res/relation'
      }
    }
  },
  watch: {
    show: function (n) {
      this.visible = n
    },
    visible: function (n) {
      if (n) {
        this.getCkeckedKeys()
      }
    }
  },
  created() {
    this.getAllAPITreeData()
  },
  methods: {
    handleOk() {
      if (!this.checkedAction) {
        this.visible = false
        this.$emit('ok')
        return
      }
      const params = {
        parentId: this.model.id,
        children: this.apiCheckedKeys
      }
      this.loading = true
      post(this.url.apiSave, params)
        .then(res => {
          if (res.success) {
            this.$message.success(res.msg)
            this.visible = false
            this.$emit('ok')
          } else {
            this.$message.error(res.msg)
          }
        })
        .finally(() => {
          setTimeout(() => {
            this.loading = false
          }, 1000)
        })
    },
    getAllAPITreeData() {
      if (this.apiData.length > 0) {
        return
      }
      get(this.url.apiUrl).then(res => {
        if (res.success) {
          console.log('111.api', res)
          this.apiData = ApiTreeUtil.modifyAPIDataKey(res.data || [])
        }
      })
    },
    resetForm() {
      this.apiCheckedKeys = []
      this.visible = false
    },
    getCkeckedKeys() {
      get(`${this.url.apiGet}/${this.model.id}`).then(res => {
        if (res.success) {
          this.apiCheckedKeys = res.data && res.data.map(v => v.interfaceId)
        }
      })
    },
    handleCheck() {
      this.checkedAction = true
    }
  }
}
</script>
