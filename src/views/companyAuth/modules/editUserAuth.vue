<template>
  <div>
    <el-row v-for="item in objTypes" :key="item.code">
      <el-divider content-position="left">
        {{ item.name }}
      </el-divider>
      <el-space wrap>
        <el-tag v-for="obj in currentObjData[item.code]" :key="obj">
          {{ objMap.get(obj) }}
        </el-tag>
      </el-space>
    </el-row>
    <el-row v-if="showEdit">
      <el-tabs v-model="activeName">
        <el-tab-pane
          v-for="item in objTypes"
          :key="item.code"
          :label="item.name"
          :name="item.code"
        >
          <el-tree
            :default-checked-keys="currentObjData[item.code]"
            show-checkbox
            check-strictly
            default-expand-all
            :data="objTree[item.code]"
            :props="{
              key: 'id',
              label: 'name',
              children: 'children'
            }"
            node-key="id"
            @check="(checkedKeys, e) => onCheck(item.code, checkedKeys, e)"
          />
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>

<script>
import { get } from '@/utils/http/request.js'

export default {
  name: 'EditUserAuth',
  props: {
    value: {
      type: Object,
      default: () => {
        return {}
      }
    },
    resultType: {
      type: String,
      default: 'object'
    },
    showEdit: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      url: {
        objTypes: '/voucher/s/obj/type/types?type=00102',
        objTree: '/voucher/s/obj/companyTree'
      },
      activeName: '',
      objTypes: [],
      objTree: {},
      objTypeMap: {},
      currentObjData: this.value || {},
      objMap: new Map()
    }
  },
  watch: {
    currentObjData: {
      deep: true,
      handler: function (data) {
        console.log('this.resultType', this.resultType)
        if (this.resultType === 'array') {
          const result = this.buildDetails()
          console.log("this.resultType === 'array'", result)
          this.$emit('change', result)
          // this.$emit('update:value', result)
        } else if (this.resultType === 'object') {
          console.log("this.resultType === 'object'", data)
          this.$emit('change', data)
          this.$emit('update:value', data)
        }
      }
    },
    value(_new) {
      this.currentObjData = _new
      // console.log(_new)
    }
  },
  mounted() {
    this.currentObjData = this.value || {}
    this.getObjTypes()
    this.getObjTree()
  },
  methods: {
    getObjTypes() {
      get(this.url.objTypes).then(res => {
        if (res && res.success) {
          const array = []
          res.data.forEach(item => {
            if (!item.hidden) {
              array.push({
                code: item.code,
                name: item.name
              })
            }
            this.objTypeMap[item.code] = item.name
          })
          this.objTypes = array
          this.activeName = array[0]?.code || ''
        }
      })
    },
    getObjTree() {
      get(this.url.objTree).then(res => {
        if (res && res.success) {
          this.objTree = res.data
          for (const dataKey in res.data) {
            this.buildObjMap(res.data[dataKey])
          }
        }
      })
    },
    buildObjMap(data) {
      data.forEach(item => {
        this.objMap.set(item.id, item.name)
        if (item.children) {
          this.buildObjMap(item.children)
        }
      })
    },
    onCheck(type, checkedKeys, e) {
      console.log('onCheck', type, checkedKeys, e)
      this.currentObjData[type] = e.checkedKeys
    },
    buildDetails() {
      const detail = []
      let tags = []
      for (const key in this.currentObjData) {
        const data = this.currentObjData[key]
        const objName = this.objTypeMap[key]
        tags = tags.concat(data)
        detail.push({
          name: objName,
          key: key,
          value: JSON.stringify(data)
        })
      }
      detail.push({
        name: '标签',
        key: 'tags',
        value: JSON.stringify(tags)
      })
      return detail
    }
  }
}
</script>
