<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="440px"
    :close-on-click-modal="false"
    @close="$emit('cancel')"
  >
    <el-form
      ref="ruleForm"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-row>
        <el-col>
          <el-form-item label="文件夹名称" prop="name">
            <el-input v-model="form.name" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="排序">
            <el-input-number v-model="form.sort" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('cancel')">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleOk"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { post, put } from '@/utils/http/request.js'
import pick from 'lodash.pick'

const rules = {
  name: [{ required: true, message: '请输入文件夹名称', trigger: 'blur' }]
}

export default {
  name: 'FolderModal',
  props: {
    model: {
      type: Object,
      default: () => {
        return {
          id: null,
          parentId: null
        }
      }
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 }
      }
    }
    this.formItemLayoutWithOutLabel = {
      wrapperCol: {
        xs: { span: 24, offset: 0 },
        sm: { span: 20, offset: 4 }
      }
    }
    return {
      rules: rules,
      title: '新建API文件夹',
      id: null,
      form: {
        name: '',
        parentId: '',
        sort: 0
      },
      visible: false,
      loading: false,
      authTypeSel: null,
      commitNew: true,
      url: {
        group: '/voucher/s/ins/group'
      },
      showNullErr: false
    }
  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal) {
        this.id = this.model.id
        if (this.id) {
          this.form = pick(this.model, ['name', 'parentId', 'sort'])
        } else {
          this.form.parentId = this.model.parentId
        }
      }
    }
  },
  methods: {
    handleOk() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          if (!this.form.sort) {
            this.form.sort = 100
          }
          if (this.id) {
            put(`${this.url.group}/${this.id}`, this.form)
              .then(res => {
                if (res.success) {
                  this.$message.success(res.msg)
                  this.resetForm()
                  this.$emit('ok')
                } else {
                  this.$message.error(res.msg)
                }
              })
              .finally(e => {
                setTimeout(() => {
                  this.loading = false
                }, 1000)
              })
          } else {
            post(this.url.group, this.form).then(res => {
              if (res.success) {
                this.$message.success(res.msg)
                this.resetForm()
                this.$emit('ok')
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        }
      })
    },
    resetForm() {
      this.id = ''
      this.form.name = ''
      this.form.parentId = ''
      this.form.sort = ''
    }
  }
}
</script>
