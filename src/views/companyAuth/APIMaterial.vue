<template>
  <el-card body-class="h-full" class="h-full" shadow="never">
    <div class="table-page-search-wrapper h-full">
      <el-row :gutter="20" class="h-full">
        <el-col :span="6" class="!flex flex-col">
          <el-button
            type="primary"
            :plain="true"
            class="w-full !mb-2"
            @click="importSwagger"
          >
            导入swagger配置
          </el-button>
          <el-button
            type="primary"
            :plain="true"
            class="w-full !ml-0 !mb-2"
            @click="securitySetting"
          >
            全局访问控制
          </el-button>
          <div class="auth-tree-box !flex-1">
            <el-input
              v-model="searchValue"
              style="margin-bottom: 20px"
              placeholder="输入内容以搜索"
              clearable
              @input="searchChanged"
            >
              <template #suffix>
                <el-icon><SearchIcon /></el-icon>
              </template>
            </el-input>
            <el-button
              v-if="!treeData"
              type="primary"
              :plain="true"
              style="width: 100%"
              @click="handleAddMember({ key: '1' }, '')"
            >
              新建API文件夹
            </el-button>
            <el-tree
              :data="treeData"
              :props="replaceFields"
              node-key="id"
              :expand-on-click-node="false"
              @node-click="nodeSelect"
            >
              <template #default="{ data }">
                <el-row>
                  <el-col :span="12" class="my-tree-title">
                    <span v-if="data.name.indexOf(searchValue) > -1">
                      {{ data.name.substr(0, data.name.indexOf(searchValue)) }}
                      <span style="color: #f50">{{ searchValue }}</span>
                      {{
                        data.name.substr(
                          data.name.indexOf(searchValue) + searchValue.length
                        )
                      }}
                    </span>
                    <span v-else>{{ data.name }}</span>
                  </el-col>
                  <el-col
                    :span="8"
                    :offset="4"
                    class="tree-tools"
                    @click="stopPropagation"
                  >
                    <el-dropdown
                      @command="command => addFolder({ key: command })"
                    >
                      <el-button type="primary" size="small" circle>
                        <el-icon><PlusIcon /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item :command="data.parentId"
                            >添加同级</el-dropdown-item
                          >
                          <el-dropdown-item :command="data.id"
                            >添加下级</el-dropdown-item
                          >
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                    <el-button
                      type="primary"
                      size="small"
                      circle
                      @click="
                        editFolder(data.name, data.id, data.parentId, data.sort)
                      "
                    >
                      <el-icon><EditIcon /></el-icon>
                    </el-button>
                    <span @click="stopPropagation">
                      <el-popconfirm
                        title="确定要删除吗?"
                        @confirm="() => deleteFolder(data.id)"
                      >
                        <template #reference>
                          <el-button type="danger" size="small" circle>
                            <el-icon><DeleteIcon /></el-icon>
                          </el-button>
                        </template>
                      </el-popconfirm>
                    </span>
                  </el-col>
                </el-row>
              </template>
            </el-tree>
          </div>
        </el-col>
        <el-col v-if="folderSelectedId" :span="17">
          <div class="table-operator">
            <span>{{ pathMap[folderSelectedId] || '' }}</span>
          </div>
          <div class="table-operator">
            <el-button type="primary" @click="addInterface()">
              <el-icon class="el-icon--left">
                <PlusIcon />
              </el-icon>
              添加新接口
            </el-button>
          </div>
          <el-table
            ref="table"
            v-loading="loading"
            :data="tableData"
            row-key="id"
            @selection-change="onSelectChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" width="80">
              <template #default="{ $index }">
                {{
                  (pagination.current - 1) * pagination.pageSize + $index + 1
                }}
              </template>
            </el-table-column>
            <el-table-column label="接口名称" prop="name" />
            <el-table-column label="请求方法" prop="method" />
            <el-table-column label="请求地址" prop="url" />
            <el-table-column label="备注" prop="remark">
              <template #default="{ row }">
                <el-text class="w-150px mb-2" truncated
                  >{{ row.remark }}
                </el-text>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <a @click="editInterface(row)">编辑</a>
                <el-divider direction="vertical" />
                <a @click="securitySetting(row)">安全配置</a>
                <el-divider direction="vertical" />
                <el-popconfirm
                  title="确定要删除吗?"
                  @confirm="deleteItem(row.id)"
                >
                  <template #reference>
                    <a href="#">删除</a>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            style="margin-top: 20px; text-align: right"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-col>
      </el-row>
    </div>

    <folder-modal
      ref="folderModal"
      :model="model"
      @ok="handlerFolderOk"
      @cancel="modalCancel('folderModal')"
    />
    <interface-modal
      ref="interfaceModal"
      :model="insModal"
      @ok="handlerInterfaceOk"
      @cancel="modalCancel('interfaceModal')"
    />
    <APIImportModal
      ref="apiImportModal"
      :model="insModal"
      @ok="handlerFolderOk"
      @cancel="modalCancel('apiImportModal')"
    />
    <APISecurityListModal
      ref="apiSecurityListModal"
      :model="securityModal"
      @ok="handlerParamOk"
      @cancel="handlerParamOk"
    />
  </el-card>
</template>
<script>
import {
  Delete as DeleteIcon,
  Edit as EditIcon,
  Plus as PlusIcon,
  Search as SearchIcon
} from '@element-plus/icons-vue'
import { del, get } from '@/utils/http/request.js'
import FolderModal from '@/views/companyAuth/modules/FolderModal.vue'
import InterfaceModal from '@/views/companyAuth/modules/InterfaceModal.vue'
import APIImportModal from '@/views/companyAuth/modules/APIImportModal.vue'
import APISecurityListModal from '@/views/companyAuth/modules/APISecurityListModal.vue'

const columns = [
  {
    title: '#',
    scopedSlots: { customRender: 'serial' }
  },
  {
    title: '请求方式',
    dataIndex: 'method'
  },
  {
    title: '请求地址',
    dataIndex: 'url'
  },
  {
    title: '接口名称',
    dataIndex: 'name'
  },
  {
    title: '是否启用',
    dataIndex: 'enable',
    customRender: text => (text ? '是' : '否')
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '180px',
    scopedSlots: { customRender: 'action' }
  }
]

export default {
  name: 'APIMaterial',
  components: {
    APISecurityListModal,
    APIImportModal,
    InterfaceModal,
    FolderModal,
    SearchIcon,
    PlusIcon,
    EditIcon,
    DeleteIcon
  },
  data() {
    this.columns = columns
    return {
      searchValue: '',
      folderSelectedId: false,
      treeData: [],
      replaceFields: {
        children: 'interfaceGroups',
        title: 'name',
        key: 'id'
      },
      model: {
        id: null,
        parentId: ''
      },
      insModal: {
        id: null,
        parentId: ''
      },
      securityModal: {
        interfaceId: '',
        interfaceName: ''
      },
      url: {
        list: '/voucher/s/ins/list',
        delete: '/voucher/s/ins',
        tree: '/voucher/s/ins/group/tree?all=true&needInterface=false',
        deleteFolder: '/voucher/s/ins/group'
      },
      // 表格数据
      tableData: [],
      // 分页配置
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      queryParam: {},
      pathMap: {},
      loading: false
    }
  },
  created() {
    this.getFolders()
  },
  methods: {
    loadData(parameter) {
      return get(this.url.list, parameter)
    },
    // 加载表格数据
    async loadTableData() {
      if (!this.folderSelectedId) return

      this.loading = true
      try {
        const parameter = {
          pageNum: this.pagination.current,
          pageSize: this.pagination.pageSize,
          ...this.queryParam
        }
        const res = await this.loadData(parameter)
        if (res && res.success) {
          this.tableData = res.data || res.result || []
          this.pagination.total = res.totalRecords || 0
        } else {
          this.$message.error(res.msg || '加载数据失败')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.current = 1
      this.loadTableData()
    },
    // 当前页改变
    handleCurrentChange(current) {
      this.pagination.current = current
      this.loadTableData()
    },
    // 选择改变
    onSelectChange(selection) {
      this.selectedRowKeys = selection.map(item => item.id)
      this.selectedRows = selection
    },
    // 重写 refresh 方法
    refresh() {
      this.loadTableData()
    },
    importSwagger() {
      this.$refs.apiImportModal.visible = true
    },
    securitySetting(record) {
      this.securityModal.interfaceId = record.id
      this.securityModal.interfaceName = record.name
      this.$refs.apiSecurityListModal.visible = true
    },
    getFolders() {
      get(this.url.tree).then(res => {
        if (res.success) {
          this.pathMap = {}
          this.treeData = res.data
          this.getNodePath(this.treeData, '')
          console.log('111.treeData', this.treeData)
          console.log('111.treeData', this.pathMap)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    searchChanged(e) {
      console.log('searchChanged', this.searchValue)
    },
    addFolder(e) {
      this.model.id = ''
      this.model.name = ''
      this.model.parentId = e.key
      this.model.sort = 0
      this.$refs.folderModal.title = '新增API文件夹'
      this.$refs.folderModal.visible = true
    },
    editFolder(name, id, parentId, sort) {
      this.model.id = id
      this.model.name = name
      this.model.parentId = parentId
      this.model.sort = sort
      this.$refs.folderModal.title = '编辑API文件夹'
      this.$refs.folderModal.visible = true
    },
    deleteFolder(id) {
      del(`${this.url.deleteFolder}/${id}`).then(res => {
        if (res.success) {
          this.$message.success(res.msg)
          this.folderSelectedId = ''
          this.getFolders()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    stopPropagation(e) {
      e.stopPropagation()
    },
    modalCancel(modalRef) {
      this.$refs[modalRef].resetForm()
      this.$refs[modalRef].visible = false
    },
    handlerFolderOk() {
      this.folderSelectedId = ''
      this.getFolders()
      this.$refs.folderModal.visible = false
    },
    nodeSelect(node) {
      if (node && node.id) {
        let flag = true
        if (!this.folderSelectedId) {
          flag = false
        }
        this.folderSelectedId = node.id
        this.queryParam.parentId = this.folderSelectedId
        if (flag) {
          this.refresh()
        } else {
          this.loadTableData()
        }
      }
    },
    getNodePath(data, parentName) {
      data?.forEach(item => {
        const currentPathName = parentName + '/' + item.name
        this.pathMap[item.id] = currentPathName

        if (item.interfaceGroups) {
          this.getNodePath(item.interfaceGroups, currentPathName)
        }
      })
    },
    addInterface() {
      this.insModal = {}
      this.insModal.parentId = this.folderSelectedId
      this.$refs.interfaceModal.title = '新增接口信息'
      this.$refs.interfaceModal.visible = true
    },
    editInterface(record) {
      this.insModal = record
      this.$refs.interfaceModal.title = '编辑接口信息'
      this.$refs.interfaceModal.visible = true
    },
    handlerInterfaceOk() {
      this.refresh()
      this.$refs.interfaceModal.visible = false
    },
    handlerParamOk() {
      this.securityModal.interfaceId = ''
      this.securityModal.interfaceName = ''
      this.$refs.apiSecurityListModal.visible = false
    },
    deleteItem(id) {
      return new Promise((resolve, reject) => {
        if (!this.url.delete) {
          this.$message.warning('未设置url->delete')
          reject(new Error('未设置url->delete'))
          return
        }
        del(`${this.url.delete}/${id}`).then(res => {
          if (res.success) {
            this.$message.success('删除成功')
            // 刷新表格
            this.refresh()
            resolve()
          } else {
            this.$message.warning(res.msg)
            reject(new Error('删除失败！'))
          }
        })
      })
    }
  }
}
</script>
<style lang="less">
.auth-tree-box {
  border-right: 1px solid #e8e8e8;
  padding-right: 20px;
  overflow: hidden;

  .ant-tree-node-content-wrapper {
    width: 100%;
  }

  .tree-tools {
    display: none;
  }

  .ant-tree li .ant-tree-node-content-wrapper {
    position: relative;
  }

  .ant-tree li .ant-tree-node-content-wrapper:hover {
    .tree-tools {
      display: block;
    }
  }
}

.my-auth-group {
  margin-bottom: 30px;
}
</style>
