<template>
  <el-card shadow="never" :body-style="{ height: '100%' }" style="height: 100%">
    <div
      class="table-page-search-wrapper"
      style="
        display: flex;
        flex-direction: column;
        height: 100%;
        overflow: hidden;
      "
    >
      <h3>权限类别：</h3>
      <dynamic-auth-group
        :show="apiShow"
        class="my-auth-group"
        @curSel="getTreeList"
      />
      <el-row :gutter="24" style="flex: 1; height: 0">
        <el-col :span="8" style="height: 100%">
          <el-card shadow="never" style="height: 100%">
            <template #header>
              <span>权限资源</span>
            </template>
            <gj-tree
              v-model:selected-keys="selectedKeys"
              :tree-data="gData"
              checkOne
              checkable
              check-strictly
              :replace-fields="replaceFields"
              @select="onAuthTreeSelect"
            />
          </el-card>
        </el-col>
        <el-col v-if="authSelected" :span="8" style="height: 100%">
          <el-card shadow="never" style="height: 100%">
            <template #header>
              <div
                style="
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                "
              >
                <span>前端资源</span>
                <el-button type="primary" link @click="saveFeSource"
                  >保存</el-button
                >
              </div>
            </template>
            <gj-tree
              v-model:checkedKeys="feCheckedKeys"
              checkable
              :selectable="false"
              :tree-data="feData"
              :whole-child-keys="feWholeKeys"
              :need-check-all="true"
              :replace-fields="replaceFieldsFE"
              :checkStrictly="true"
            />
          </el-card>
        </el-col>
        <el-col v-if="authSelected && apiShow" :span="8" style="height: 100%">
          <el-card shadow="never" style="height: 100%">
            <template #header>
              <div
                style="
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                "
              >
                <span>API资源</span>
                <el-button type="primary" link @click="saveAPISource"
                  >保存</el-button
                >
              </div>
            </template>
            <gj-directory-tree
              v-model:checkedKeys="apiCheckedKeys"
              checkable
              :selectable="false"
              :treeData="apiData"
              :whole-child-keys="apiWholeKeys"
              :need-check-all="true"
              :defaultExpandParent="true"
              :replace-fields="replaceFields"
            />
          </el-card>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>
<script>
import DynamicAuthGroup from './dynamic-auth-group/index.vue'
import { get, post } from '@/utils/http/request.js'
import GjTree from './Tree/GjTree.vue'
import GjDirectoryTree from './Tree/GjDirectoryTree.vue'
import ApiTreeUtil from './util/apiTreeUtil'

export default {
  name: 'AuthSettings',
  components: { DynamicAuthGroup, GjTree, GjDirectoryTree },
  data() {
    return {
      // 当前选择的权限类别
      curAuthType: null,
      selectedKeys: [],
      // 当前权限资源tree data
      gData: [],
      // 所有权限资源tree data
      allAuthData: null,
      // 前端资源 tree data
      feData: [],
      feCheckedKeys: [],
      feWholeKeys: [],
      // api资源 tree data
      apiShow: false,
      apiData: [],
      apiCheckedKeys: [],
      authSelected: false,
      selectedAuthId: '',
      apiWholeKeys: [],
      url: {
        authTree: '/voucher/s/obj/companyTree',
        allAuthTree: '/voucher/s/obj/tree?all=true',
        feTree: '/voucher/s/res/my',
        apiTree: '/voucher/s/ins/group/tree?all=true',
        authFETree: '/voucher/s/res/menu',
        authAPITree: '/voucher/s/ins/authority/all',
        saveFeSrc: '/voucher/s/res/resource',
        saveApiSrc: '/voucher/s/ins/authority/deploy'
      },
      replaceFields: {
        label: 'name',
        children: 'children',
        value: 'id'
      },
      replaceFieldsFE: {
        label: 'title',
        children: 'children',
        value: 'id'
      }
    }
  },
  watch: {
    curAuthType: function (n, o) {
      if (n) {
        this.selectedKeys = []
        this.feCheckedKeys = []
        this.apiCheckedKeys = []
        this.authSelected = false
        this.getAllTreeData(n)
        // this.getAuthTableData(n)
      }
    }
  },
  created() {
    if (this.$route.name === 'Auth Setting') {
      this.apiShow = true
    }
  },
  mounted() {
    this.getAllFETreeData()
    if (this.$route.name === 'Auth Setting') {
      this.getAllAPITreeData()
    }
  },
  methods: {
    getAllChildren(list, container, key = 'id') {
      if (!list || !list.length) {
        return
      }
      list.map(v => {
        container.push(v[key])
        if (v.children && v.children.length > 0) {
          this.getAllChildren(v.children, container, key)
        }
      })
    },
    getAllChildrenWithCheck(allList, hasRoleList, container, key = 'id') {
      if (!hasRoleList || !hasRoleList.length || !allList || !allList.length) {
        return
      }
      const allKey = []
      this.getAllChildren(allList, allKey)
      const allKeySet = new Set(allKey)

      const hasList = []
      this.getAllChildren(hasRoleList, hasList)

      hasList.forEach(item => {
        if (allKeySet.has(item)) {
          container.push(item)
        }
      })
    },
    getChildWithContainer(list, container, key = 'id') {
      if (!list || !list.length) {
        return
      }
      list.map(v => {
        if (v.children && v.children.length > 0) {
          this.getChildWithContainer(v.children, container, key)
        } else {
          container.push(v[key])
        }
      })
    },
    getTreeList($event) {
      this.curAuthType = $event
    },
    getAllTreeData(n) {
      if (this.allAuthData && Object.keys(this.allAuthData).length > 0) {
        this.gData = this.allAuthData[n]
        return
      }
      get(this.apiShow ? this.url.allAuthTree : this.url.authTree).then(res => {
        if (res.success) {
          this.allAuthData = res.data
          this.gData = this.allAuthData[n]
        }
      })
    },
    getAllFETreeData() {
      console.log('getAllFETreeData start')
      get(this.url.feTree).then(res => {
        console.log('getAllFETreeData success', res)
        if (res.success) {
          this.feData = this.modifyFEDataKey(res.data || [])
          this.feWholeKeys = []
          this.getChildWithContainer(this.feData, this.feWholeKeys)
        }
      })
    },
    getAllAPITreeData() {
      get(this.url.apiTree).then(res => {
        if (res.success) {
          this.apiData = ApiTreeUtil.modifyAPIDataKey(res.data || [])
          this.apiWholeKeys = []
          this.getChildWithContainer(this.apiData, this.apiWholeKeys)
        }
      })
    },
    getAuthRelatedFEData(objectId) {
      get(`${this.url.authFETree}/${objectId}`).then(res => {
        if (res.success) {
          if (res.data.length > 0) {
            this.feCheckedKeys = []
            this.getAllChildrenWithCheck(
              this.feData,
              res.data,
              this.feCheckedKeys
            )
            console.log('getAuthRelatedFEData.feData', this.feData)
            console.log('getAuthRelatedFEData.res.data', res.data)
            console.log(
              'getAuthRelatedFEData.feCheckedKeys',
              this.feCheckedKeys
            )
            if (!this.feWholeKeys.length && this.feData.length > 0) {
              this.getChildWithContainer(this.feData, this.feWholeKeys)
            }
          }
        }
      })
    },
    getAuthRelatedAPIData(objectId) {
      get(`${this.url.authAPITree}`, { objectId }).then(res => {
        if (res.success) {
          if (res.data.length > 0) {
            this.apiCheckedKeys = res.data.map(v => v.interfaceId)
            if (!this.apiWholeKeys.length && this.apiData.length > 0) {
              this.getChildWithContainer(this.apiData, this.apiWholeKeys)
            }
          }
        }
      })
    },
    modifyFEDataKey(list = []) {
      return list.map(v => {
        v.title = v.meta.title
        this.modifyFEDataKey(v.children || [])
        return v
      })
    },
    onAuthTreeSelect(keys, e) {
      this.apiCheckedKeys = []
      this.feCheckedKeys = []
      if (keys?.length > 0) {
        this.authSelected = true
        this.selectedAuthId = keys[0]
        this.getAuthRelatedFEData(keys[0])
        if (this.apiShow) {
          this.getAuthRelatedAPIData(keys[0])
        }
      } else {
        this.authSelected = false
        this.selectedAuthId = ''
      }
    },
    saveFeSource() {
      const params = {}
      params.businessId = this.selectedAuthId
      params.reResources = []
      const t = this.feCheckedKeys.map(v => {
        return { reResourceId: v, rsResources: [] }
      })
      params.reResources = t
      post(this.url.saveFeSrc, params).then(res => {
        if (res.success) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    saveAPISource() {
      const params = {}
      params.object = this.selectedAuthId
      params.interfaces = this.apiCheckedKeys
      post(this.url.saveApiSrc, params).then(res => {
        if (res.success) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="less"></style>
