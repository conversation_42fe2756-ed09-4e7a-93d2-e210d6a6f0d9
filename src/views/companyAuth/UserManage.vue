<template>
  <el-card shadow="never" :body-style="{ height: '100%' }" style="height: 100%">
    <el-row :gutter="8" style="height: 100%">
      <el-col
        :span="6"
        style="display: flex; flex-direction: column; height: 100%"
      >
        <dynamic-auth-group
          :is-add="true"
          class="my-auth-group"
          @curSel="getTreeList"
        />
        <div class="auth-tree-box">
          <el-input
            v-model="searchValue"
            style="margin-bottom: 20px"
            placeholder="输入内容以搜索"
            clearable
            @input="onChange"
          >
            <template #suffix>
              <el-icon><SearchIcon /></el-icon>
            </template>
          </el-input>
          <el-tree
            ref="tree"
            :expanded-keys="expandedKeys"
            :auto-expand-parent="autoExpandParent"
            :data="gData"
            :props="replaceFields"
            :current-node-key="currentSelectedKey"
            :show-checkbox="true"
            :check-strictly="true"
            node-key="id"
            style="flex: 1; overflow: hidden auto"
            @node-expand="onExpand"
            @node-click="onTreeNodeSelect"
            @check="onTreeNodeCheck"
          >
            <template #default="{ data }">
              <el-row>
                <el-col :span="12" class="my-tree-title">
                  <span v-if="data.name.indexOf(searchValue) > -1">
                    {{ data.name.substr(0, data.name.indexOf(searchValue)) }}
                    <span style="color: #f50">{{ searchValue }}</span>
                    {{
                      data.name.substr(
                        data.name.indexOf(searchValue) + searchValue.length
                      )
                    }}
                  </span>
                  <span v-else>{{ data.name }}</span>
                </el-col>
              </el-row>
            </template>
          </el-tree>
        </div>
      </el-col>
      <el-col
        :span="18"
        style="display: flex; flex-direction: column; height: 100%"
      >
        <div class="table-page-search-wrapper">
          <el-form :inline="true" class="ant-advanced-search-form">
            <el-row :gutter="48">
              <el-col :md="2" :sm="24">
                <el-button type="primary" @click="handleAdd">新建</el-button>
              </el-col>
              <el-col :md="6" :sm="24">
                <el-form-item label="姓名">
                  <el-input v-model="queryParam.fullName" />
                </el-form-item>
              </el-col>
              <el-col :md="6" :sm="24">
                <el-form-item label="用户名">
                  <el-input v-model="queryParam.username" />
                </el-form-item>
              </el-col>
              <template v-if="advanced">
                <el-col :md="8" :sm="24">
                  <el-form-item label="权限对象">
                    <el-select
                      v-model="objSelectIds"
                      multiple
                      @visible-change="showObjParam"
                      @remove-tag="handleDeselect"
                    >
                      <el-option
                        v-for="i in objOptions"
                        :key="i.val"
                        :value="i.val"
                        :label="i.key"
                      >
                        {{ i.key }}
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :md="8" :sm="24">
                  <el-form-item label="创建日期">
                    <el-date-picker
                      v-model="creatDateRange"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      @change="onCreatDateChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="8" :sm="24">
                  <el-form-item label="登录日期">
                    <el-date-picker
                      v-model="lastLoginTimeRange"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      @change="onLasLoginTimeChange"
                    />
                  </el-form-item>
                </el-col>
              </template>
              <el-col :md="(!advanced && 8) || 24" :sm="24">
                <span
                  class="table-page-search-submitButtons"
                  :style="
                    (advanced && { float: 'right', overflow: 'hidden' }) || {}
                  "
                >
                  <el-button type="primary" @click="refresh(true)"
                    >查询</el-button
                  >
                  <el-button style="margin-left: 8px" @click="handleSearchReset"
                    >重置</el-button
                  >
                  <a style="margin-left: 8px" @click="toggleAdvanced">
                    {{ advanced ? '收起' : '展开' }}
                    <el-icon>
                      <ArrowUp v-if="advanced" />
                      <ArrowDown v-else />
                    </el-icon>
                  </a>
                </span>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <el-table
          ref="table"
          v-loading="loading"
          :data="tableData"
          row-key="user.userId"
          style="flex: 1; overflow: hidden auto"
          @selection-change="onSelectChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="序号" width="80">
            <template #default="{ $index }">
              {{ (pagination.current - 1) * pagination.pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="姓名" prop="user.fullName">
            <template #default="{ row }">
              {{ row.user.fullName }}
              <el-tag
                v-if="
                  row.user.managerType === 1 &&
                  queryParam.objectIds &&
                  queryParam.objectIds.length > 0
                "
                type="success"
                >主管</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column label="用户名" prop="user.username" />
          <el-table-column label="性别" prop="user.sex" />
          <el-table-column label="电话" prop="user.phone" />
          <el-table-column
            label="创建时间"
            prop="user.translation.createDateStr"
          />
          <el-table-column
            label="最后登录时间"
            prop="user.translation.lastLoginTimeStr"
          />
          <el-table-column label="操作" width="230" fixed="right">
            <template #default="{ row }">
              <a @click="handleDetail(row)">详情</a>
              <el-divider direction="vertical" />
              <a @click="handleEdit(row)">编辑</a>
              <el-divider direction="vertical" />
              <!-- <a @click="resetPwd(row)">重置密码</a>
              <el-divider direction="vertical" /> -->

              <el-popconfirm
                title="您确定要删除此用户?"
                confirm-button-text="确定"
                cancel-button-text="取消"
                @confirm="leaveCompany(row.user.userId)"
              >
                <template #reference>
                  <a href="#">删除</a>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          style="margin-top: 20px; text-align: right"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-col>
    </el-row>

    <add-user-modal
      ref="createModal"
      :model="mdl"
      @cancel="handleCancel('createModal')"
      @ok="handleOk"
    />
    <reset-pwd-modal
      ref="resetPwdModal"
      :model="mdl"
      @cancel="handleCancel('resetPwdModal')"
      @ok="handleOk"
    />
    <auth-param-modal
      ref="authParamModal"
      :model="curDomain"
      @ok="handleAuthParamModalOk"
      @cancel="handleCancel('authParamModal')"
    />
    <!-- <step-by-step-modal ref="modal" @ok="handleOk" /> -->
  </el-card>
</template>

<script>
import {
  Search as SearchIcon,
  ArrowUp,
  ArrowDown
} from '@element-plus/icons-vue'
import AddUserModal from './modules/AddUserModal.vue'
import ResetPwdModal from './modules/ResetPwdModal.vue'
import AuthParamModal from './modules/AuthParamModal.vue'
import DynamicAuthGroup from './dynamic-auth-group/index.vue'
import URLS from '@api/URLS'
import { del, get, post } from '@/utils/http/request.js'

const getParentKey = (key, tree) => {
  let parentKey
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.children) {
      if (node.children.some(item => item.id === key)) {
        parentKey = node.id
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children)
      }
    }
  }
  return parentKey
}

const statusMap = {
  0: {
    status: 'default',
    text: '关闭'
  },
  1: {
    status: 'processing',
    text: '运行中'
  },
  2: {
    status: 'success',
    text: '已上线'
  },
  3: {
    status: 'error',
    text: '异常'
  }
}

export default {
  name: 'UserManage',
  components: {
    DynamicAuthGroup,
    AuthParamModal,
    ResetPwdModal,
    AddUserModal,
    SearchIcon,
    ArrowUp,
    ArrowDown
    // StepByStepModal
  },
  filters: {
    statusFilter(type) {
      return statusMap[type].text
    },
    statusTypeFilter(type) {
      return statusMap[type].status
    }
  },
  data() {
    return {
      // add user model
      visible: false,
      queryParam: {},
      advanced: false,
      confirmLoading: false,
      loading: false,
      mdl: null,
      curDomain: {
        checkedInfos: []
      },
      objSelectIds: [],
      objOptions: [],
      url: {
        tree: '/voucher/s/obj/companyTree',
        // list: '/voucher/s/company/userList',
        list: '/voucher/s/user/companyInfo',
        setManager: '/voucher/s/userDr/setManager',
        cancelManager: '/voucher/s/userDr/cancelManager',
        secure: userId => `/s/companyleaveCompany/${userId}`,
        leave: userId => `/voucher/s/company/leaveCompany/${userId}`,
        delUser: userId => `/voucher/s/user/${userId}`
      },
      expandedKeys: [],
      autoExpandParent: true,
      gData: [],
      replaceFields: {
        children: 'children',
        title: 'name',
        key: 'id'
      },
      selectedKeys: [],
      currentSelectedKey: null, // 当前选中的节点key，用于单选
      curAuthType: null,
      searchValue: '',
      creatDateRange: [],
      lastLoginTimeRange: [],
      // 表格数据
      tableData: [],
      // 分页配置
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  watch: {
    curAuthType: function (n, o) {
      if (n) {
        this.getAllTreeData(n)
      }
    }
  },
  created() {
    // getRoleList({ t: new Date() })
  },
  mounted() {
    this.loadTableData()
  },
  methods: {
    handleCancel(refName) {
      this.$refs[refName].visible = false
    },
    loadData(parameter) {
      return get(this.url.list, parameter)
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    // 加载表格数据
    async loadTableData() {
      this.loading = true
      try {
        const parameter = {
          ...this.queryParam,
          pageNum: this.pagination.current,
          pageSize: this.pagination.pageSize
        }
        const res = await this.loadData(parameter)
        if (res && res.success) {
          this.tableData = res.data || res.result || []
          this.pagination.total = res.totalRecords || 0
        } else {
          this.$message.error(res.msg || '加载数据失败')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.current = 1
      this.loadTableData()
    },
    // 当前页改变
    handleCurrentChange(current) {
      this.pagination.current = current
      this.loadTableData()
    },
    // 选择改变
    onSelectChange(selection) {
      this.selectedRowKeys = selection.map(item => item.user.userId)
      this.selectedRows = selection
    },
    // 重写 refresh 方法
    refresh() {
      this.loadTableData()
    },
    onLasLoginTimeChange(dates) {
      console.log(dates)
      this.queryParam.lastLoginTimeStart =
        dates[0].format('YYYY-MM-DD') + ' 00:00:00'
      this.queryParam.lastLoginTimeEnd =
        dates[1].format('YYYY-MM-DD') + ' 23:59:59'
    },
    onCreatDateChange(dates) {
      console.log(dates)
      this.queryParam.creatTimeStart =
        dates[0].format('YYYY-MM-DD') + ' 00:00:00'
      this.queryParam.creatTimeEnd = dates[1].format('YYYY-MM-DD') + ' 23:59:59'
    },
    handleAdd() {
      this.mdl = { data: null, type: 'add' }
      this.$refs.createModal.visible = true
    },
    handleDetail(record) {
      this.$refs.createModal.visible = true
      this.mdl = { data: record, type: 'detail' }
    },
    handleEdit(record) {
      this.$refs.createModal.visible = true
      this.mdl = { data: record, type: 'edit' }
    },
    updateManagerType(record) {
      console.log('updateManagerType', this.queryParam.objectIds)
      if (record.user.managerType === 1) {
        post(this.url.cancelManager, {
          userId: record.user.userId,
          objectId: this.queryParam.objectIds
        }).then(res => {
          if (res && res.success) {
            this.handleOk()
            this.$message.success(res.msg)
          } else {
            this.$message.error('操作失败')
          }
        })
      } else {
        post(this.url.setManager, {
          userId: record.user.userId,
          objectId: this.queryParam.objectIds
        }).then(res => {
          if (res && res.success) {
            this.handleOk()
            this.$message.success(res.msg)
          } else {
            this.$message.error('操作失败')
          }
        })
      }
    },
    resetPwd(record) {
      this.mdl = { data: record }
      this.$refs.resetPwdModal.visible = true
    },
    showObjParam() {
      this.$refs.authParamModal.visible = true
    },
    handleOk() {
      this.refresh()
    },
    handleDelete(record) {
      del(this.url.secure(record.user.userId)).then(res => {
        if (res.success) {
          // 刷新表格
          this.refresh()
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handleSubmit(e) {
      e.preventDefault()
      this.form.validateFields((err, values) => {
        if (!err) {
          console.log('Received values of form: ', values)
        }
      })
    },
    handleSelectChange(value) {
      console.log(value)
      this.form.setFieldsValue({
        note: `Hi, ${value === 'male' ? 'man' : 'lady'}!`
      })
    },
    handleSearchReset() {
      this.queryParam = {}
      this.objOptions = []
      this.objSelectIds = []
      this.selectedKeys = []
      this.currentSelectedKey = null // 重置当前选中的节点
      this.creatDateRange = []
      this.lastLoginTimeRange = []
      this.pagination.current = 1
      // 清空树的选择状态
      if (this.$refs.tree) {
        this.$refs.tree.setCheckedKeys([])
        this.$refs.tree.setCurrentKey(null)
      }
      this.refresh()
    },
    handleDeselect(value) {
      this.objSelectIds = this.objSelectIds.filter(t => t !== value)
      this.queryParam.objectIds = this.objSelectIds.join(',')

      for (const type in this.curDomain.checkedInfos) {
        if (this.curDomain.checkedInfos.hasOwnProperty(type)) {
          const checkedInfo = this.curDomain.checkedInfos[type]
          for (const key in checkedInfo) {
            if (checkedInfo.hasOwnProperty(key)) {
              if (key === value) {
                delete checkedInfo[key]
                if (Object.keys(checkedInfo).length === 0) {
                  delete this.curDomain.checkedInfos[type]
                }
                return
              }
            }
          }
        }
      }
    },
    handleAuthParamModalOk(data) {
      this.objOptions = []
      this.objSelectIds = []
      this.queryParam.objectIds = ''
      const checkedInfos = data.checkedInfos
      console.log('test', checkedInfos)

      for (const type in checkedInfos) {
        if (checkedInfos.hasOwnProperty(type)) {
          for (const key in checkedInfos[type]) {
            if (checkedInfos[type].hasOwnProperty(key)) {
              this.objOptions.push({
                key: checkedInfos[type][key],
                val: key
              })
              this.objSelectIds.push(key)
            }
          }
        }
      }
      this.curDomain = data
      this.queryParam.objectIds = this.objSelectIds.join(',')
      this.$refs.authParamModal.visible = false
    },
    leaveCompany(userId) {
      del(this.url.delUser(userId)).then(res => {
        if (res && res.success) {
          this.$message.success(res.msg)
          this.refresh()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getTreeList($event) {
      // 切换radio button时 置为null
      this.selectedKeys = []
      this.currentSelectedKey = null
      this.treeNodeSelectedId = null
      this.curAuthType = $event
      this.queryParam.objectIds = ''
      // 清空树的选择状态
      if (this.$refs.tree) {
        this.$refs.tree.setCheckedKeys([])
        this.$refs.tree.setCurrentKey(null)
      }
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    onTreeNodeSelect(data, node) {
      // console.log('节点点击:', data, node)
      // // 实现单选逻辑：如果点击的是当前选中的节点，则取消选择；否则选中新节点
      // if (this.currentSelectedKey === data.id) {
      //   // 取消选择
      //   this.currentSelectedKey = null
      //   this.selectedKeys = []
      //   this.queryParam.objectIds = undefined
      //   // 取消复选框选中状态
      //   this.$refs.tree.setCheckedKeys([])
      // } else {
      //   // 选中新节点
      //   this.currentSelectedKey = data.id
      //   this.selectedKeys = [data.id]
      //   this.queryParam.objectIds = data.id
      //   // 设置复选框选中状态
      //   this.$refs.tree.setCheckedKeys([data.id])
      // }
      // this.refresh()
    },
    // 处理复选框点击事件
    onTreeNodeCheck(data, checked) {
      // 获取当前选中的节点
      const checkedKeys = checked.checkedKeys

      if (checkedKeys.length === 0) {
        // 如果没有选中任何节点，清空选择
        this.currentSelectedKey = null
        this.selectedKeys = []
        delete this.queryParam.objectIds
      } else {
        // 只保留最后一个选中的节点（实现单选）
        const lastCheckedKey = data.id
        this.currentSelectedKey = lastCheckedKey
        this.selectedKeys = [lastCheckedKey]
        this.queryParam.objectIds = lastCheckedKey
        // 确保只有一个节点被选中
        this.$refs.tree.setCheckedKeys([lastCheckedKey])
      }
      this.refresh()
    },
    onChange(e) {
      const value = e
      const expandedKeys = this.gData
        .map(item => {
          if (item.name.indexOf(value) > -1) {
            return getParentKey(item.id, this.gData)
          }
          return null
        })
        .filter((item, i, self) => item && self.indexOf(item) === i)
      Object.assign(this, {
        expandedKeys,
        searchValue: value,
        autoExpandParent: true
      })
    },
    getAllTreeData(n, refresh = false) {
      if (this.allData && Object.keys(this.allData).length > 0 && !refresh) {
        this.gData = this.allData[n]
        return
      }
      get(this.url.tree).then(res => {
        if (res.success) {
          this.allData = res.data
          this.gData = this.allData[n]
        }
      })
    },
    deleteUserExtend(userId, key) {
      post(URLS.API_VOUCHER + '/s/user/extend/deleteByUserIdAndKey', {
        userId: userId,
        exKey: key
      }).then(res => {
        if (res.success) {
          this.$message.success(res.msg)
          this.refresh()
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
#components-layout-demo-basic {
  background: none;
}

#components-layout-demo-basic .ant-layout-sider {
  background: none;
}

#components-layout-demo-basic .ant-layout-content {
  background: none;
}

#components-layout-demo-basic > .ant-layout {
  margin-bottom: 48px;
}

#components-layout-demo-basic > .ant-layout:last-child {
  margin: 0;
}

.auth-tree-box {
  border-right: 1px solid #e8e8e8;
  padding-right: 20px;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;

  .ant-tree-node-content-wrapper {
    width: 100%;
    // padding: 0 !important;
  }

  // .my-tree-title {
  // padding: 0 5px;
  //   cursor: pointer;
  // }
  .tree-tools {
    display: none;
  }

  // .ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected {
  // background-color: transparent;
  // .my-tree-title {
  //   background-color: fade(@primary-color, 60%);
  // }
  // }
  .ant-tree li .ant-tree-node-content-wrapper {
    position: relative;
  }

  .ant-tree li .ant-tree-node-content-wrapper:hover {
    // background-color: transparent;
    .tree-tools {
      display: block;
    }

    // .my-tree-title {
    //   background-color: fade(@primary-color, 20%);
    // }
  }
}

.my-auth-group {
  margin-bottom: 30px;
}
</style>
