<template>
  <div>
    <div v-if="editable">
      <el-input
        style="width: 40px"
        size="default"
        :model-value="value"
        @input="handleChange"
        @keyup.enter="onCheck(0)"
      />
      <el-icon style="margin-left: 10px" class="element" @click="onCheck(0)"
        ><Check
      /></el-icon>
    </div>
    <div v-else>
      <font>{{ value }}</font>
      <el-icon style="margin-left: 10px" class="element" @click="onCheck(1)"
        ><Edit
      /></el-icon>
    </div>
  </div>
</template>

<script>
import { Check, Edit } from '@element-plus/icons-vue'

export default {
  name: 'EditCell',
  components: {
    Check,
    Edit
  },
  props: {
    sortNum: {
      type: Number
    },
    index: {
      type: Number
    },
    tableLength: {
      type: Number
    }
  },
  data() {
    return {
      value: this.sortNum,
      editable: false,
      len: this.tableLength,
      ind: this.index
    }
  },
  watch: {
    sortNum(newVal) {
      // console.log('子组件接收到的新值111:', newVal);
      // 在这里可以对新值进行进一步处理或触发其他操作
      this.value = newVal
    },
    tableLength(newVal) {
      this.len = newVal
    },
    index(newVal) {
      this.ind = newVal
    }
  },
  methods: {
    info(type, msg, selType) {
      this.$notification[type]({
        message: msg,
        description: selType
      })
    },
    onCheck(type) {
      if (type === 0) {
        if (this.value === '') {
          this.info('error', '错误', '请输入正确的数据类型')
          this.editable = true
          return
        }
        if (isNaN(this.value)) {
          this.info('error', '错误', '请输入正确的数据类型')
          this.editable = true
          return
        }
        if (parseInt(this.value) < 0) {
          this.info('error', '错误', '只能输入大于零的整数')
          this.editable = true
          return
        }
        if (parseInt(this.value) > this.len && this.ind === this.len) {
          // this.info('error', '错误', '填写的数不能超过最大的排序数')
          this.value = this.len
          this.editable = false
          return
        }
        if (!Number.isInteger(parseInt(this.value))) {
          this.info('error', '错误', '只能输入整数')
          this.editable = true
          return
        }
        this.editable = false
        this.$emit('change', parseInt(this.value))
      }
      if (type === 1) {
        this.editable = true
      }
    },
    handleChange(value) {
      this.value = value
    },
    handleNumericInput() {
      // 使用正则表达式将非数字字符替换为空字符串
      this.value = this.value.replace(/\D/g, '')
    },
    validateInput(rule, value, callback) {
      if (value && !/^\d+$/.test(value)) {
        callback(new Error('只能输入数字'))
      } else {
        callback()
      }
    }
  }
}
</script>

<style scoped>
.element {
  transition: transform 0.3s ease;
}

.element:hover {
  transform: scale(1.5); /* 使用scale属性来实现元素的缩放 */
}
</style>
