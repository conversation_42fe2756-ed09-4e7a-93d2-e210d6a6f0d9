<template>
  <el-dialog v-model="visible" :title="title" width="1000">
    <el-form
      ref="formRef"
      :model="formData"
      label-width="120px"
      class="check-form"
      :rules="rules"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="检查表单名称" prop="taskName">
            <el-input
              v-model="formData.taskName"
              placeholder="请输入检查表单名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检查类别" prop="checkType">
            <dict-selector
              v-model="formData.checkType"
              parentKey="008000"
              placeholder="请选择检查类别"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检查日期" prop="dateRange">
            <el-date-picker
              v-model="formData.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 检查项目 -->
    <div class="mb-2 flex align-center justify-between">
      <div class="title-text">检查项目</div>
      <el-button type="primary" @click="addTableVisible"> 选择 </el-button>
    </div>
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="tableData"
      border
      row-key="id"
      style="width: 100%"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center' }"
      height="400"
      class="check-table"
    >
      <el-table-column
        label="序号"
        width="100"
        align="center"
        :sortable="false"
      >
        <template #default="{ row, $index }">
          <el-input
            v-model="row.sort"
            size="small"
            style="width: 60px; text-align: center"
            @change="onSortChange(row, $index)"
          />
        </template>
      </el-table-column>
      <el-table-column label="自查项目名称" prop="itemName" />
      <el-table-column label="操作" width="80" align="center">
        <template #default="{ $index }">
          <el-button type="danger" size="small" @click="removeRow($index)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 检查建筑 -->
    <div class="mb-2 flex align-center justify-between mt-2">
      <div class="title-text">检查建筑</div>
      <el-button type="primary" @click="addTableVisibleBuilding">
        选择
      </el-button>
    </div>
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="tableDataBuilding"
      border
      row-key="id"
      style="width: 100%"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center' }"
      height="400"
      class="check-table"
    >
      <el-table-column
        label="序号"
        width="100"
        align="center"
        type="index"
        :index="(index: number) => index + 1"
      />
      <el-table-column label="建筑名称" prop="name" />
      <el-table-column label="建筑地址" prop="address" />
      <el-table-column label="操作" width="80" align="center">
        <template #default="{ $index }">
          <el-button
            type="danger"
            size="small"
            @click="removeRowBuilding($index)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog v-model="tablevisible" title="请选择" width="1000">
    <checkItemsTable
      v-if="tablevisible"
      ref="tableRef"
      :tableDataChose="tableDataChose"
      :url="choseTabelUrl"
      :column="column"
      :searchParams="searchParams"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="choseSubmit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, watch, ref, provide } from 'vue'
import FormComposition from '@/composition/FormCompostion'
import checkItemsTable from '@/components/self/checkItemsTable.vue'
import DictSelector from '@c/Dict/dict'

let column = ref([])
let searchParams = ref()

const emits = defineEmits(['ok'])
const dealRecord = (record: any) => {
  console.log('dealRecord', record)
  formData.value.dateRange = [record.startCheckDate, record.endCheckDate]
  tableData.value = record?.itemConfigs?.map((item: any, index: number) => ({
    ...item,
    sort: index + 1, // 确保每个项都有唯一的 sort 值
    id: item.checkItemConfigId
  }))
  tableDataBuilding.value = record?.buildInfos?.map(
    (item: any, index: number) => ({
      ...item,
      sort: index + 1, // 确保每个项都有唯一的 sort 值
      id: item.buildId,
      name: item.buildName,
      address: item.buildAddress
    })
  )
}

const rules = reactive({
  taskName: [
    { required: true, message: '请输入检查表单名称', trigger: 'blur' }
  ],
  checkType: [{ required: true, message: '请选择检查类别', trigger: 'blur' }],
  dateRange: [{ required: true, message: '请选择检查日期', trigger: 'blur' }]
})

const tableRef = ref()
const dealSubmitParams = (data: any) => {
  // 防御性判断，避免 tableRef 未挂载时报错
  const selectedItems = tableData.value
  data.itemConfigs = selectedItems.map((item: any, index) => ({
    checkItemConfigId: item.id,
    sortNumber: index + 1
  }))
  data.buildIds = tableDataBuilding.value.map((item: any) => item.id)
  data.startCheckDate = data.dateRange[0]
  data.endCheckDate = data.dateRange[1]
  console.log('dealSubmitParams', data)
}
const {
  visible,
  title,
  formData,
  formRef,
  loading,
  handleAdd,
  handleEdit,
  handleSubmit,
  handleClose
} = FormComposition({
  urls: {
    normal: '/build/self-check/task-config',
    detail: '/build/self-check/task-config',
    save: '/build/self-check/task-config',
    rowKey: 'id'
  },
  dealRecord,
  dealSubmitParams,
  emits,
  addInit: () => {
    formData.value.checkItems = []
    tableData.value = []
    tableDataBuilding.value = []
  }
})

const tablevisible = ref(false)
const tableData = ref([])
const choseSubmit = () => {
  // 获取选中的检查项
  const selectedItems = tableRef.value?.getSelection() || []
  if (selectedItems.length === 0) {
    return
  }
  if (choseTabelUrl.value == '/build/self-check/item-config') {
    tableData.value = selectedItems.map((item: any, index: number) => ({
      ...item,
      sort: index + 1 // 确保每个项都有唯一的 sort 值
    }))
  } else {
    console.log('selectedItems', selectedItems)
    tableDataBuilding.value = selectedItems
  }
  tablevisible.value = false
}

// 修改序号后排序
const onSortChange = (row: any, index: number) => {
  // 先按 sort 升序排序
  tableData.value.sort((a: any, b: any) => a.sort - b.sort)
  // 重新赋值 sort，保证唯一且连续
  tableData.value.forEach((item: any, idx: number) => {
    item.sort = idx + 1
  })
}

// 删除行
const removeRow = (index: number) => {
  tableData.value.splice(index, 1)
  // 删除后重排序号
  tableData.value.forEach((item: any, idx: number) => {
    item.sort = idx + 1
  })
}
// 删除行
const removeRowBuilding = (index: number) => {
  tableDataBuilding.value.splice(index, 1)
  // 删除后重排序号
  tableDataBuilding.value.forEach((item: any, idx: number) => {
    item.sort = idx + 1
  })
}
const tableDataChose = ref([])
const choseTabelUrl = ref('')
const addTableVisible = () => {
  column.value = [
    {
      prop: 'itemName',
      label: '自查项目名称'
    }
  ]
  searchParams.value = [
    {
      prop: 'itemName',
      label: '自查项目名称'
    }
  ]
  tableDataChose.value = tableData.value
  choseTabelUrl.value = '/build/self-check/item-config'
  tablevisible.value = true
}
const addTableVisibleBuilding = () => {
  column.value = [
    {
      prop: 'name',
      label: '建筑名称'
    },
    {
      prop: 'address',
      label: '建筑地址'
    }
  ]
  searchParams.value = [
    {
      prop: 'nameOrAddress',
      label: '建筑名称/地址'
    }
  ]
  tableDataChose.value = tableDataBuilding.value
  choseTabelUrl.value = '/build/build'
  tablevisible.value = true
}
const tableDataBuilding = ref([])
defineExpose({ handleAdd, handleEdit })
</script>

<style lang="scss" scoped>
.option-list {
  margin-top: 8px;
  margin-left: 100px;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.option-label {
  width: 60px;
  padding: 2px 4px;
  margin-right: 6px;
  font-weight: bold;
  text-align: right;
  background: #eee;
  border-radius: 2px;
}

.align-center {
  align-items: center;
}

.title-text {
  margin-right: 20px;
  font-weight: bolder;
}

.check-table {
  .el-table__body-wrapper {
    max-height: 400px; /* 设置最大高度 */
    overflow-y: auto; /* 超出部分滚动 */
  }

  ::v-deep .el-input__inner {
    text-align: center !important;
  }
}
</style>
