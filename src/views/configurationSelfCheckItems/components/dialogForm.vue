<script setup lang="ts">
import { reactive, watch, ref } from 'vue'
import FormComposition from '@/composition/FormCompostion'
import { Plus, Minus, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import { ElMessage } from 'element-plus'

const emits = defineEmits(['ok'])
const dealRecord = (record: any) => {
  console.log('dealRecord', record)
  formData.value.itemResultType = String(record.itemResultType)
}

const rules = reactive({
  itemName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }]
})

const dealSubmitParams = (data: any) => {
  data.options = data.options.map((opt: any) => {
    return {
      ...opt
    }
  })
  console.log('dealSubmitParams', data)
}
const validate = data => {
  console.log('dealSubmitParams', data)
  let subflag = true
  data.options.map((opt: any) => {
    if (!opt.optionDesc) {
      subflag = false
    }
  })
  if (!subflag) {
    ElMessage.error('请填写所有选项描述')
    return false
  }
  return true
}
const {
  visible,
  title,
  formData,
  formRef,
  loading,
  handleAdd,
  handleEdit,
  handleSubmit,
  handleClose
} = FormComposition({
  urls: {
    normal: '/build/self-check/item-config',
    detail: '/build/self-check/item-config',
    rowKey: 'id'
  },
  dealRecord,
  dealSubmitParams,
  emits,
  addInit: () => {
    if (!formData.value.options || formData.value.options.length === 0) {
      formData.value.options = [{ optionDesc: '', optionNo: 1 }]
    }
  },
  validate
})

const addOption = () => {
  formData.value.options.push({
    optionDesc: '',
    optionNo: formData.value.options.length + 1
  })
}
const removeOption = (idx: number) => {
  if (formData.value.options.length > 1) {
    formData.value.options.splice(idx, 1)
    updateOptionSort()
  }
}
const moveUp = (idx: number) => {
  if (idx > 0) {
    const opts = formData.value.options
    ;[opts[idx - 1], opts[idx]] = [opts[idx], opts[idx - 1]]
    updateOptionSort()
  }
}
const moveDown = (idx: number) => {
  const opts = formData.value.options
  if (idx < opts.length - 1) {
    ;[opts[idx + 1], opts[idx]] = [opts[idx], opts[idx + 1]]
    updateOptionSort()
  }
}
const updateOptionSort = () => {
  formData.value.options.forEach((opt, i) => {
    opt.optionNo = i + 1
  })
}

defineExpose({ handleAdd, handleEdit })
</script>

<template>
  <el-dialog v-model="visible" :title="title" width="1000">
    <el-form
      ref="formRef"
      :model="formData"
      label-width="120px"
      :rules="rules"
      class="check-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="自查项名称" prop="itemName">
            <el-input v-model="formData.itemName" />
          </el-form-item>
          <el-form-item label="">
            <el-row>
              <el-col :span="24">
                <draggable
                  v-model="formData.options"
                  item-key="optionNo"
                  class="option-list"
                  @end="updateOptionSort"
                >
                  <template #item="{ element: opt, index: idx }">
                    <div class="option-item">
                      <span class="option-label">选项{{ idx + 1 }}：</span>
                      <el-input
                        v-model="opt.optionDesc"
                        style="width: 220px; margin-right: 8px"
                      />
                      <span style="width: 32px; text-align: center">{{
                        opt.optionNo
                      }}</span>
                      <el-button
                        v-if="formData.options.length > 1"
                        circle
                        size="small"
                        @click="removeOption(idx)"
                      >
                        <el-icon><Minus /></el-icon>
                      </el-button>
                      <el-button
                        v-if="idx === formData.options.length - 1"
                        circle
                        size="small"
                        @click="addOption"
                      >
                        <el-icon><Plus /></el-icon>
                      </el-button>
                      <el-button
                        v-if="idx > 0"
                        circle
                        size="small"
                        @click="moveUp(idx)"
                      >
                        <el-icon><ArrowUp /></el-icon>
                      </el-button>
                      <el-button
                        v-if="idx < formData.options.length - 1"
                        circle
                        size="small"
                        @click="moveDown(idx)"
                      >
                        <el-icon><ArrowDown /></el-icon>
                      </el-button>
                    </div>
                  </template>
                </draggable>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.option-list {
  margin-top: 8px;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.option-label {
  width: 60px;
  padding: 2px 4px;
  margin-right: 6px;
  font-weight: bold;
  text-align: right;
  background: #eee;
  border-radius: 2px;
}
</style>
