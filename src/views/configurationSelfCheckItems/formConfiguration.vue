<template>
  <div class="mb-3">
    <el-form :inline="true" label-width="auto" width="100%">
      <el-form-item label="检查表单名称">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入检查表单名称"
        />
      </el-form-item>
      <el-form-item label="任务编号">
        <el-input
          v-model="queryParams.taskNo"
          placeholder="请输入检查表单名称"
        />
      </el-form-item>
      <el-form-item label="检查类别">
        <dict-selector
          v-model="queryParams.checkTypes"
          parentKey="008000"
          placeholder="请选择检查类别"
          multiple
          style="width: 192px"
        />
      </el-form-item>
      <el-form-item label="检查结果">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择检查结果"
          style="width: 192px"
        >
          <el-option label="已完成" value="1" />
          <el-option label="未完成" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="检查开始日期">
        <el-date-picker
          v-model="queryParams.startCheckDate"
          type="date"
          placeholder="请选择检查开始日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="检查结束日期">
        <el-date-picker
          v-model="queryParams.endCheckDate"
          type="date"
          placeholder="请选择检查结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()"> 查询 </el-button>
        <el-button type="primary" @click="resetTable"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </div>
  <div class="mb-2">
    <el-button
      v-auth="'SelfInspectionFormConfigurationAdd'"
      type="primary"
      @click="handleAdd"
    >
      新增
    </el-button>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
  >
    <el-table-column
      type="index"
      label="序号"
      width="60"
      align="center"
      :index="(index: number) => index + 1"
    />
    <el-table-column label="检查表单名称" prop="taskName" />
    <el-table-column label="任务编号" prop="taskNo" />
    <el-table-column label="检查类别" prop="translation.checkTypeStr">
      <template #default="{ row }">
        {{ row.translation?.checkTypeStr || '-' }}
      </template>
    </el-table-column>
    <el-table-column label="检查结果" prop="status">
      <template #default="{ row }">
        {{ row.status === 1 ? '已完成' : '未完成' }}
      </template>
    </el-table-column>
    <el-table-column label="检查开始日期" prop="startCheckDate" />
    <el-table-column label="检查结束日期" prop="endCheckDate" />
    <el-table-column label="检查建筑数量" prop="totalCheckBuildNum" />
    <el-table-column label="完成检查建筑数量" prop="finishCheckBuildNum" />
    <el-table-column label="操作" width="200">
      <template #default="scope">
        <el-button
          size="small"
          type="primary"
          link
          @click="handleTaskDetial(scope.row)"
        >
          任务完成详情
        </el-button>
        <el-button
          v-auth="'SelfInspectionFormConfigurationEdit'"
          size="small"
          type="primary"
          link
          @click="handleEdit(scope.row)"
        >
          编辑
        </el-button>
        <el-popconfirm
          class="box-item"
          title="确认删除该资源吗"
          placement="top-start"
          @confirm="handleDelete(scope.row)"
        >
          <template #reference>
            <el-button
              v-auth="'SelfInspectionFormConfigurationDel'"
              size="small"
              type="danger"
              link
            >
              删除
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>
  <dialogForm ref="formRef" @ok="loadTableData(1)" />
  <el-dialog v-model="tablevisible" title="任务完成详情" width="1000">
    <TaskDetial v-if="tablevisible" :defaultParams="defaultParams" />
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="choseSubmit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ElButton, ElTable, ElTableColumn } from 'element-plus'
import { onMounted, ref } from 'vue'
import TableComposition from '@/composition/TableComposition'
import { CascaderProps } from 'element-plus'
import { get } from '@/utils/http/request'
import { useRouter } from 'vue-router'
import dialogForm from '@v/configurationSelfCheckItems/components/dialogFormConfiguration.vue'
const router = useRouter()
import DictSelector from '@c/Dict/dict'
import TaskDetial from './components/taskDetial.vue'

const defaultParams = ref({})
const tablevisible = ref(false)
const choseSubmit = () => {
  tablevisible.value = false
}
const handleTaskDetial = (row: any) => {
  tablevisible.value = true
  defaultParams.value = row
}

const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit
} = TableComposition({
  urls: {
    list: '/build/self-check/task-config/page',
    normal: '/build/self-check/task-config',
    rowKey: 'id'
  }
})
// 搜索
const search = () => {
  console.log('search', queryParams.value)
  loadTableData(1)
}

const areaProp: CascaderProps = {
  lazy: true,
  checkStrictly: true,
  lazyLoad(node, resolve) {
    console.log('lazyLoad', node)
    const { level } = node
    const params = {
      pCode: null
    }
    if (level === 0) {
      params.pCode = '-1'
    } else {
      params.pCode = node.value
    }
    get(`/build/city/children/${params.pCode}`).then(res => {
      console.log('获取到数据:', res)
      if (res && res.success) {
        const nodes = res.data.map(item => ({
          value: item.code,
          label: item.name,
          leaf: level === 3
        }))
        resolve(nodes)
      }
    })
  }
}
onMounted(() => {
  loadTableData(1)
})
</script>
