<template>
  <div style="width: 100%">
    <el-row :gutter="20" style="width: 100%">
      <el-col v-for="(item, idx) in cardList" :key="idx" :span="12">
        <el-card class="mb-4">
          <div class="flex justify-between items-center mb-2">
            <span>{{ item.title }}</span>
            <div>
              <el-button type="primary" link size="small">下载模板</el-button>
            </div>
          </div>
          <g-upload
            v-model:value="item.fileIds"
            :file-data-list="item.fileDataList"
            :type="item.uploadType || 'normal'"
            :disabled="false"
            :multiple="false"
            list-type="slot"
            drag
            style="width: 100%"
          >
            <div>
              <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
              <div class="el-upload__text">点击或将文件拖拽到这里上传</div>
              <div class="el-upload__tip">
                支持扩展名：.rar .zip .doc .docx .pdf .jpg ...
              </div>
            </div>
          </g-upload>
        </el-card>
      </el-col>
    </el-row>
    <div class="flex justify-end mt-4">
      <el-button type="primary" @click="submit">提交</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ref, watch } from 'vue'
import GUpload from '@/components/upload/g-upload.vue'
import { ArrowLeft, UploadFilled } from '@element-plus/icons-vue'
import { get, post } from '@/utils/http/request'
import { ElMessage } from 'element-plus'
const router = useRouter()
const props = defineProps<{
  currentId: string
}>()

const cardList = ref([
  {
    title: '月防火检查登记表',
    time: '2025-06-10 09:34:40',
    fileIds: [],
    fileDataList: [],
    uploadType: 'RegularReportAttachment',
    fileName: '',
    reportType: 1
  },
  {
    title: '其他附件（可上传维保报告附件等内容）',
    time: '2024-11-14 15:50:35',
    fileIds: [],
    fileDataList: [],
    uploadType: 'RegularReportAttachment',
    fileName: '',
    reportType: 4
  },
  {
    title: '动火作业记录',
    time: '2025-06-10 09:34:40',
    fileIds: [],
    fileDataList: [],
    uploadType: 'RegularReportAttachment',
    fileName: '',
    reportType: 2
  },
  {
    title: '日常巡查巡检记录',
    time: '',
    fileIds: [],
    fileDataList: [],
    uploadType: 'RegularReportAttachment',
    fileName: '',
    reportType: 3
  }
])
watch(
  () => props.currentId,
  newVal => {
    if (newVal) {
      cardList.value.forEach(item => {
        item.fileIds = []
        item.fileDataList = []
      })
    }
  },
  { immediate: true }
)
const submit = async () => {
  const companyId = props.currentId || ''
  const submitParams = cardList.value.filter(item => item.fileIds.length > 0)
  const records = submitParams.map(item => ({
    reportType: item.reportType,
    fileIds: item.fileIds
  }))
  const params = {
    companyId,
    records
  }
  try {
    const res = await post('build/regular-report-record', params)
    if (res && res.success) {
      // 可根据实际情况提示或跳转
      ElMessage.success('提交成功')
      cardList.value.forEach(item => {
        item.fileIds = []
        item.fileDataList = []
      })
    } else {
      ElMessage.error(res?.message || '提交失败')
    }
  } catch (e) {
    ElMessage.error('提交异常')
  }
}
</script>

<style lang="scss" scoped>
.upload-demo {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  cursor: pointer;
  background: #fafbfc;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  transition:
    border-color 0.2s,
    background 0.2s;

  &:hover {
    background: #f0f7ff;
    border-color: #409eff;
  }

  .el-icon--upload {
    margin-bottom: 8px;
    font-size: 36px;
    color: #a0a0a0;
  }

  .el-upload__text {
    margin-bottom: 4px;
    font-size: 15px;
    color: #606266;
  }

  .el-upload__tip {
    font-size: 12px;
    color: #b0b0b0;
  }
}
</style>
