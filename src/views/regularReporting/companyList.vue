<template>
  <div class="mb-3">
    <el-form :inline="true" label-width="auto">
      <el-form-item label="公司名称">
        <el-input v-model="queryParams.name" placeholder="请输入公司名称" />
      </el-form-item>
      <el-form-item label="公司编码">
        <el-input v-model="queryParams.code" placeholder="请输入公司编码" />
      </el-form-item>
      <el-form-item label="最后上报时间">
        <el-date-picker
          v-model="queryParams.lastReportTimeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="行政区划" prop="areas">
        <el-cascader
          v-model="queryParams.areas"
          :props="areaProp"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()"> 查询 </el-button>
        <el-button type="primary" @click="resetTable"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
  >
    <el-table-column label="单位名称" prop="name" />
    <el-table-column label="单位代码" prop="code" />
    <el-table-column label="省" prop="translation.provinceStr" />
    <el-table-column label="市" prop="translation.cityStr" />
    <el-table-column label="区" prop="translation.areaStr" />
    <el-table-column label="街道" prop="translation.streetStr" />
    <el-table-column label="最后上报时间" prop="lastReportTime" />
    <el-table-column label="操作" width="200">
      <template #default="scope">
        <el-button
          v-auth="'regularReportingReport'"
          size="small"
          type="warning"
          link
          @click="toDetail(scope.row)"
        >
          上报
        </el-button>
        <el-button
          v-auth="'regularReportingHistoryBtn'"
          size="small"
          type="success"
          link
          @click="toHistory(scope.row)"
        >
          历史记录
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>
</template>
<script setup lang="ts">
import { ElButton, ElTable, ElTableColumn } from 'element-plus'
import { onMounted } from 'vue'
import TableComposition from '@/composition/TableComposition'
import { CascaderProps } from 'element-plus'
import { get } from '@/utils/http/request'
import { useRouter } from 'vue-router'
const router = useRouter()
const emits = defineEmits(['changeTab'])
const toDetail = (row: any) => {
  emits('changeTab', { id: row.id, type: 'detail', name: row.name })
}
const toHistory = (row: any) => {
  emits('changeTab', { id: row.id, type: 'history', name: row.name })
}
const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit
} = TableComposition({
  urls: {
    list: '/build/company/page',
    normal: '/build/company',
    rowKey: 'id'
  }
})
// 搜索
const search = () => {
  if (queryParams.value.areas) {
    queryParams.value.province = queryParams.value.areas[0] || ''
    queryParams.value.city = queryParams.value.areas[1] || ''
    queryParams.value.area = queryParams.value.areas[2] || ''
    queryParams.value.street = queryParams.value.areas[3] || ''
  }
  if (queryParams.value.lastReportTimeRange) {
    queryParams.value.startLastReportTime = queryParams.value
      .lastReportTimeRange[0]
      ? `${queryParams.value.lastReportTimeRange[0]}T00:00:00`
      : ''
    queryParams.value.endLastReportTime = queryParams.value
      .lastReportTimeRange[1]
      ? `${queryParams.value.lastReportTimeRange[1]}T23:59:59`
      : ''
  }
  console.log('search', queryParams.value)
  loadTableData(1)
}

const areaProp: CascaderProps = {
  lazy: true,
  checkStrictly: true,
  lazyLoad(node, resolve) {
    console.log('lazyLoad', node)
    const { level } = node
    const params = {
      pCode: null
    }
    if (level === 0) {
      params.pCode = '-1'
    } else {
      params.pCode = node.value
    }
    get(`/build/city/children/${params.pCode}`).then(res => {
      console.log('获取到数据:', res)
      if (res && res.success) {
        const nodes = res.data.map(item => ({
          value: item.code,
          label: item.name,
          leaf: level === 3
        }))
        resolve(nodes)
      }
    })
  }
}
onMounted(() => {
  loadTableData(1)
})
</script>
