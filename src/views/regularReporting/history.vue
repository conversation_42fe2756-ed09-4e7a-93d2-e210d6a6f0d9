<template>
  <div class="mb-3" style="min-width: 200px">
    <el-form ref="formRef" :inline="true" style="width: 100%">
      <el-form-item label="上报类型">
        <el-select
          v-model="queryParams.reportType"
          placeholder="请选择上报类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in cardList"
            :key="item.reportType"
            :label="item.title"
            :value="item.reportType"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="上报时间">
        <el-date-picker
          v-model="queryParams.lastReportTimeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search"> 查询 </el-button>
        <el-button type="primary" @click="resetTable"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </div>
  <el-row :gutter="20" style="width: 100%">
    <el-table
      :data="tableData"
      border
      row-key="id"
      style="width: 100%"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center' }"
    >
      <el-table-column
        type="index"
        label="序号"
        width="60"
        align="center"
        :index="indexMethod"
      />
      <el-table-column label="上报类型" prop="reportType">
        <template #default="scope">
          {{
            cardList.find(item => item.reportType === scope.row.reportType)
              ?.title || '-'
          }}
        </template>
      </el-table-column>
      <el-table-column label="上报时间" prop="createTime" />
      <el-table-column
        label="附件"
        prop="RegularReportAttachment"
        align="center"
      >
        <template #default="scope">
          <div
            v-if="
              scope.row.regularReportAttachment &&
              scope.row.regularReportAttachment.length
            "
          >
            <div
              v-for="file in scope.row.regularReportAttachment"
              :key="file.id"
              class="flex items-center"
            >
              <el-link
                :href="getFileUrl(file)"
                target="_blank"
                type="primary"
                >{{ file.originalName }}</el-link
              >
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-popconfirm
            class="box-item"
            title="确认删除该资源吗"
            placement="top-start"
            @confirm="handleDelete(scope.row)"
          >
            <template #reference>
              <el-button size="small" type="danger" link> 删除 </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </el-row>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { onMounted, ref, watch } from 'vue'
import GUpload from '@/components/upload/g-upload.vue'
import { ArrowLeft, Search, UploadFilled } from '@element-plus/icons-vue'
import { get, post } from '@/utils/http/request'
import { ElMessage } from 'element-plus'
import TableComposition from '@/composition/TableComposition'
import URLS from '@/api/URLS'
const router = useRouter()
const props = defineProps<{
  currentId: string
}>()

const cardList = ref([
  {
    title: '月防火检查登记表',
    time: '2025-06-10 09:34:40',
    fileIds: [],
    fileDataList: [],
    uploadType: '',
    fileName: '',
    reportType: 1
  },
  {
    title: '其他附件（可上传维保报告附件等内容）',
    time: '2024-11-14 15:50:35',
    fileIds: [],
    fileDataList: [],
    uploadType: '',
    fileName: '',
    reportType: 4
  },
  {
    title: '动火作业记录',
    time: '2025-06-10 09:34:40',
    fileIds: [],
    fileDataList: [],
    uploadType: '',
    fileName: '',
    reportType: 2
  },
  {
    title: '日常巡查巡检记录',
    time: '',
    fileIds: [],
    fileDataList: [],
    uploadType: '',
    fileName: '',
    reportType: 3
  }
])

const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit,
  defaultQueryParams
} = TableComposition({
  urls: {
    list: 'build/regular-report-record/page',
    normal: 'build/regular-report-record',
    rowKey: 'id'
  }
})
console.log('defaultQueryParams', tableData)

onMounted(() => {
  const companyId = props.currentId || ''
  defaultQueryParams.companyId = companyId
  loadTableData(1)
  window.dispatchEvent(new Event('resize'))
})
watch(
  () => props.currentId,
  newVal => {
    const companyId = newVal || ''
    defaultQueryParams.companyId = companyId
    loadTableData(1)
  },
  {
    immediate: true
  }
)

const indexMethod = (index: number) => index + 1

const getFileUrl = (file: any) => {
  // 你可以根据实际接口调整
  const url = URLS.imgFileUrl(file.id)
  console.log('getFileUrl', url)
  return url
}
const search = () => {
  if (queryParams.value.lastReportTimeRange) {
    queryParams.value.startCreateTime = queryParams.value.lastReportTimeRange[0]
      ? `${queryParams.value.lastReportTimeRange[0]}T00:00:00`
      : ''
    queryParams.value.endCreateTime = queryParams.value.lastReportTimeRange[1]
      ? `${queryParams.value.lastReportTimeRange[1]}T23:59:59`
      : ''
  }
  console.log('search', queryParams.value)
  loadTableData(1)
}
</script>

<style lang="scss" scoped>
.upload-demo {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  cursor: pointer;
  background: #fafbfc;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  transition:
    border-color 0.2s,
    background 0.2s;

  &:hover {
    background: #f0f7ff;
    border-color: #409eff;
  }

  .el-icon--upload {
    margin-bottom: 8px;
    font-size: 36px;
    color: #a0a0a0;
  }

  .el-upload__text {
    margin-bottom: 4px;
    font-size: 15px;
    color: #606266;
  }

  .el-upload__tip {
    font-size: 12px;
    color: #b0b0b0;
  }
}

.el-link {
  margin: auto !important;
}
</style>
