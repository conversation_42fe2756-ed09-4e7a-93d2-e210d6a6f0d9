<template>
  <el-tabs v-model="currentTab">
    <el-tab-pane
      v-for="item in tabList"
      :key="item.name"
      :label="item.label"
      :name="item.name"
    >
      <company-list
        v-if="item.name === 'company'"
        @changeTab="handleChangeTab"
      />
      <detail v-else-if="item.name === 'detail'" :current-id="currentId" />
      <history v-else-if="item.name === 'history'" :current-id="currentId" />
    </el-tab-pane>
  </el-tabs>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import CompanyList from '@v/regularReporting/companyList.vue'
import detail from '@v/regularReporting/detail.vue'
import history from '@v/regularReporting/history.vue'

onMounted(() => {
  console.log('onMounted')
})
const currentTab = ref('company')
const currentId = ref('')
const tabList = ref([
  {
    label: '单位列表',
    name: 'company'
  }
])
const handleChangeTab = (data: any) => {
  currentId.value = data.id
  currentTab.value = data.type
  if (data.type === 'detail') {
    if (tabList.value.find(item => item.name === 'detail')) {
      tabList.value.find(item => item.name === 'detail').label =
        `「${data.name}」上报`
      return
    }
    tabList.value.push({
      label: `「${data.name}」上报`,
      name: 'detail'
    })
  }
  if (data.type === 'history') {
    if (tabList.value.find(item => item.name === 'history')) {
      tabList.value.find(item => item.name === 'history').label =
        `「${data.name}」历史记录`
      return
    }
    tabList.value.push({
      label: `「${data.name}」历史记录`,
      name: 'history'
    })
  }
}
</script>
