<script setup lang="ts">
import { reactive, watch, ref } from 'vue'
import FormComposition from '@/composition/FormCompostion'
import URLS from '@/api/URLS'

const emits = defineEmits(['ok'])
const rules = reactive({
  itemName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
  itemResultType: [
    { required: true, message: '请输入单位编码', trigger: 'blur' }
  ]
})

// 添加新的数据结构定义
interface HistoryFile {
  id: string
  businessType: string
  path: string
  originalName: string
  fileType: string
  mimeType: string
}

interface HistoryItem {
  id: string
  dailyCheckRecordId: string
  status: number
  checkTime: string
  dailyCheckRecordFile: HistoryFile[]
}

// 状态映射函数
const getStatusText = (status: number): string => {
  const statusMap = {
    1: '合格',
    2: '不合格',
    3: '不合格待复查'
    // 可添加更多状态映射
  }
  return statusMap[status] || '未知状态'
}

const dealRecord = (record: any) => {
  console.log('dealRecord', record)
  // 映射接口返回数据到表单
  formData.value = {
    ...formData.value,
    id: record.id || '',
    buildId: record.buildId || '',
    buildName: record.buildName || '',
    checkTitle: record.checkTitle || '',
    checkNo: record.checkNo || '',
    checkType: record.checkType || '',
    checkTypeStr: record.translation?.checkTypeStr || '',
    status: record.status || 0,
    statusStr: getStatusText(record.status || 0),
    createTime: record.createTime || '',
    createUser: record.createUser || '',
    createUserStr: record.translation?.createUserStr || '',
    historyList: record.historyList || []
  }
  formData.value.itemResultType = String(record.itemResultType || '')
}

const dealSubmitParams = (data: any) => {
  console.log('dealSubmitParams', data)
}
const {
  visible,
  title,
  formData,
  formRef,
  loading,
  handleAdd,
  handleEdit,
  handleSubmit,
  handleClose
} = FormComposition({
  urls: {
    normal: '/build/daily-check/record',
    detail: '/build/daily-check/record',
    rowKey: 'id'
  },
  dealRecord,
  dealSubmitParams,
  emits
})
const getFileUrl = (file: any) => {
  // 你可以根据实际接口调整
  const url = URLS.imgFileUrl(file.id)
  console.log('getFileUrl', url)
  return url
}
defineExpose({ handleAdd, handleEdit })
</script>

<template>
  <el-dialog v-model="visible" :title="'查看'" width="1000">
    <el-form
      ref="formRef"
      :model="formData"
      label-width="120px"
      :rules="rules"
      class="check-form"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="检查标题" prop="checkTitle">
            <el-input v-model="formData.checkTitle" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建筑名称" prop="buildName">
            <el-input v-model="formData.buildName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检查编号" prop="checkNo">
            <el-input v-model="formData.checkNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检查类型" prop="checkTypeStr">
            <el-input v-model="formData.checkTypeStr" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-input v-model="formData.statusStr" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="创建时间" prop="createTime">
            <el-input v-model="formData.createTime" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="创建人" prop="createUserStr">
            <el-input v-model="formData.createUserStr" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="历史记录">
            <el-table :data="formData.historyList" border style="width: 100%">
              <el-table-column prop="checkTime" label="检查时间" />
              <el-table-column prop="status" label="状态">
                <template #default="scope">
                  {{ getStatusText(scope.row.status) }}
                </template>
              </el-table-column>
              <el-table-column label="相关文件">
                <template #default="scope">
                  <div
                    v-for="file in scope.row.dailyCheckRecordFile"
                    :key="file.id"
                  >
                    <el-link
                      :href="getFileUrl(file)"
                      target="_blank"
                      download
                      type="primary"
                      >{{ file.originalName }}</el-link
                    >
                  </div>
                  <div
                    v-if="
                      !scope.row.dailyCheckRecordFile ||
                      scope.row.dailyCheckRecordFile.length === 0
                    "
                  >
                    无相关文件
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.option-list {
  margin-top: 8px;
  margin-left: 100px;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.option-label {
  width: 60px;
  padding: 2px 4px;
  margin-right: 6px;
  font-weight: bold;
  text-align: right;
  background: #eee;
  border-radius: 2px;
}
</style>
