<template>
  <div class="mb-3">
    <el-form :inline="true" label-width="auto">
      <el-form-item label="建筑名称">
        <el-input
          v-model="queryParams.buildName"
          placeholder="请输入建筑名称"
        />
      </el-form-item>
      <el-form-item label="检查标题">
        <el-input
          v-model="queryParams.checkTitle"
          placeholder="请输入检查标题"
        />
      </el-form-item>
      <el-form-item label="检查编号">
        <el-input v-model="queryParams.checkNo" placeholder="请输入检查编号" />
      </el-form-item>
      <el-form-item label="检查类型">
        <dict-selector
          v-model="queryParams.checkTypeList"
          parentKey="009000"
          placeholder="请选择检查类型"
          multiple
          style="width: 192px"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-select
          v-model="queryParams.statusList"
          placeholder="请选择状态"
          style="width: 192px"
          multiple
        >
          <el-option
            v-for="item in statusList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()"> 查询 </el-button>
        <el-button type="primary" @click="resetTable"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
  >
    <el-table-column
      type="index"
      label="序号"
      width="60"
      align="center"
      :index="(index: number) => index + 1"
    />
    <el-table-column label="建筑名称" prop="buildName" />
    <el-table-column label="检查标题" prop="checkTitle" />
    <el-table-column label="检查编号" prop="checkNo" />
    <el-table-column label="检查类型" prop="translation.checkTypeStr" />
    <el-table-column label="状态" prop="status">
      <template #default="{ row }">
        <el-text class="w-150px mb-2" truncated>
          {{ statusList.find(item => item.value === row.status)?.label }}
        </el-text>
      </template>
    </el-table-column>
    <el-table-column label="创建时间" prop="createTime" />
    <el-table-column label="操作" width="200">
      <template #default="scope">
        <el-button
          size="small"
          type="primary"
          link
          @click="handleEdit(scope.row)"
        >
          查看
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>
  <dialogForm ref="formRef" @ok="loadTableData(1)" />
</template>
<script setup lang="ts">
import { ElButton, ElTable, ElTableColumn } from 'element-plus'
import { onMounted, ref } from 'vue'
import TableComposition from '@/composition/TableComposition'
import { useRouter } from 'vue-router'
import dialogForm from '@v/dailyInspection/components/dialogForm.vue'
import DictSelector from '@c/Dict/dict'
const router = useRouter()

const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit
} = TableComposition({
  urls: {
    list: '/build/daily-check/record/page',
    normal: '',
    rowKey: 'id'
  }
})
// 搜索
const search = () => {
  console.log('search', queryParams.value)
  loadTableData(1)
}
const statusList = ref([
  {
    label: '合格',
    value: 1
  },
  {
    label: '不合格',
    value: 2
  },
  {
    label: '不合格待复查',
    value: 3
  }
])

onMounted(() => {
  loadTableData(1)
})
</script>
