<script setup lang="ts">
import {
  ElButton,
  ElMessage,
  ElTable,
  ElTableColumn,
  ElTag
} from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'
import TableComposition from '@/composition/TableComposition'
import ComplainDetail from './complainDetail.vue'

const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit
} = TableComposition({
  urls: {
    list: '/build/complain',
    normal: '/build/complain',
    rowKey: 'id'
  }
})

// 新增表单相关状态
const formVisible = ref(false)
const formMode = ref<'add' | 'edit'>('add')
const currentFormData = ref({
  type: '',
  description: '',
  poster: '',
  posterMobile: '',
  buildId: '327710871595778048'
})
const detailVisible = ref(false)
const currentDetailData: any = ref({})

// 处理详情查看
const handleDetailClick = (row: any) => {
  currentDetailData.value = { ...row }
  detailVisible.value = true
}

// 表单提交
const handleFormSubmit = () => {
  loadTableData(1)
}

// 处理状态变更
const handleStatusChange = (val: any) => {
  loadTableData(1)
}

//状态码格式化
const statusConfig = computed(() => ({
  '0': { type: 'danger', text: '待处理' },
  '1': { type: 'warning', text: '处理中' },
  '2': { type: 'success', text: '处理完成' }
}))

const getStatusTagType = (status: any) => statusConfig.value[status]?.type || ''
const getStatusText = (status: any) =>
  statusConfig.value[status]?.text || '未知状态'

onMounted(() => {
  loadTableData(1)
})
</script>

<template>
  <div class="mb-3">
    <el-form :inline="true" label-width="auto">
      <el-form-item label="隐患名称">
        <el-input v-model="queryParams.type" placeholder="请输入隐患名称" />
      </el-form-item>
      <el-form-item label="上报人">
        <el-input v-model="queryParams.poster" placeholder="请输入上报人" />
      </el-form-item>
      <el-form-item label="联系方式">
        <el-input
          v-model="queryParams.posterMobile"
          placeholder="请输入上报人联系方式"
        />
      </el-form-item>
      <el-form-item label="建筑名称">
        <el-input
          v-model="queryParams.buildName"
          placeholder="请输入建筑名称"
        />
      </el-form-item>
      <el-form-item label="处理进度">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择处理进度"
          clearable
          style="width: 150px"
          @change="handleStatusChange"
        >
          <el-option value="0" label="待处理" />
          <el-option value="1" label="处理中" />
          <el-option value="2" label="处理完成" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="loadTableData(1)">查询</el-button>
        <el-button type="primary" @click="resetTable">重置</el-button>
      </el-form-item>
    </el-form>
  </div>

  <el-table
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
  >
    <el-table-column label="隐患类型" prop="type" />
    <el-table-column label="隐患描述" prop="description" />
    <el-table-column label="上报人" prop="poster" />
    <el-table-column label="联系方式" prop="posterMobile" />
    <el-table-column label="建筑名称" prop="buildName" />
    <el-table-column label="建筑所属单位" prop="companyName" />
    <el-table-column label="处理进度">
      <template #default="{ row }">
        <el-tag :type="getStatusTagType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="60">
      <template #default="scope">
        <el-button
          size="small"
          type="success"
          link
          @click="handleDetailClick(scope.row)"
        >
          详情
        </el-button>
      </template>
    </el-table-column>
  </el-table>

  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>

  <!-- 详情对话框 -->
  <ComplainDetail
    v-if="detailVisible"
    :id="currentDetailData.id"
    :visible="detailVisible"
    @update:visible="val => (detailVisible = val)"
  />
</template>
