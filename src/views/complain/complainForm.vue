<template>
  <el-dialog
    :model-value="props.visible"
    width="50%"
    :before-close="handleClose"
    @update:model-value="val => emit('update:visible', val)"
  >
    <el-form ref="formRef" :model="localFormData" label-width="120px">
      <el-form-item label="隐患类型" prop="type">
        <el-input v-model="localFormData.type" placeholder="请输入隐患类型" />
      </el-form-item>

      <el-form-item label="隐患描述" prop="description">
        <el-input
          v-model="localFormData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入隐患描述"
        />
      </el-form-item>

      <el-form-item label="上报人" prop="poster">
        <el-input v-model="localFormData.poster" placeholder="请输入上报人" />
      </el-form-item>

      <el-form-item label="联系方式" prop="posterMobile">
        <el-input
          v-model="localFormData.posterMobile"
          placeholder="请输入上报人联系方式"
        />
      </el-form-item>

      <el-form-item label="建筑id" prop="buildId">
        <el-input v-model="localFormData.buildId" />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { post } from '@/utils/http/request'

interface FormData {
  type: string
  description: string
  poster: string
  posterMobile: string
  buildId: string
}

const props = defineProps({
  visible: Boolean,
  formData: {
    type: Object as () => FormData,
    default: () => ({
      type: '',
      description: '',
      poster: '',
      posterMobile: '',
      buildId: '327710871595778048'
    })
  }
})

const emit = defineEmits([
  'update:visible',
  'submit-success',
  'update:form-data'
])

const formRef = ref<FormInstance>()

// 创建计算属性实现双向绑定
const localFormData = computed({
  get: () => props.formData,
  set: value => emit('update:form-data', value)
})

const handleClose = () => {
  emit('update:visible', false)
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    const response = await post('/build/complain', localFormData.value)

    if (response?.success) {
      ElMessage.success('操作成功')
      emit('submit-success')
      handleClose()
    } else {
      ElMessage.error(response?.msg || '操作失败')
    }
  } catch (error) {
    if (!error?.response) {
      ElMessage.error('请完善表单信息')
    } else {
      ElMessage.error(error.response?.data?.msg || '请求出错，请稍后重试')
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
