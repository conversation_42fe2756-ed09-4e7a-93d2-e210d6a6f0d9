<template>
  <el-dialog
    :model-value="props.visible"
    title="隐患详情"
    width="60%"
    :before-close="handleClose"
    @update:model-value="val => emit('update:visible', val)"
  >
    <el-descriptions :column="2" border>
      <el-descriptions-item label="隐患类型" width="150">
        {{ detailData.type || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="隐患描述" width="150">
        {{ detailData.description || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="上报人">
        {{ detailData.poster || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="联系方式">
        {{ detailData.posterMobile || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="建筑名称">
        {{ detailData.buildName || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="建筑所属单位">
        {{ detailData.companyName || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="处理进度">
        <el-tag :type="getStatusTagType(detailData.status)">
          {{ getStatusText(detailData.status) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ detailData.createTime || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="更新时间">
        {{ detailData.updateTime || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="处理人">
        {{ detailData.processor || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="处理时间">
        {{ detailData.processTime || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="处理备注">
        {{ detailData.processRemark || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="处理结果">
        {{ detailData.processResult || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="是否同步上报">
        {{ detailData.syncReport ? '是' : '否' }}
      </el-descriptions-item>
      <el-descriptions-item label="楼层名称">
        {{ detailData.floorName || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="上报图片">
        <template
          v-if="
            detailData.complainReportImage &&
            Object.keys(detailData.complainReportImage).length
          "
        >
          <el-image
            v-for="(img, key) in detailData.complainReportImage"
            :key="key"
            :src="img"
            style="width: 80px; height: 80px; margin-right: 8px"
          />
        </template>
        <template v-else> - </template>
      </el-descriptions-item>
      <el-descriptions-item label="处理图片">
        <template
          v-if="
            detailData.processReportImage &&
            Object.keys(detailData.processReportImage).length
          "
        >
          <el-image
            v-for="(img, key) in detailData.processReportImage"
            :key="key"
            :src="img"
            style="width: 80px; height: 80px; margin-right: 8px"
          />
        </template>
        <template v-else> - </template>
      </el-descriptions-item>
    </el-descriptions>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { get } from '@/utils/http/request'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  id: {
    type: String,
    default: ''
  }
})

const statusConfig = computed(() => ({
  0: { type: 'danger', text: '待处理' },
  1: { type: 'warning', text: '处理中' },
  2: { type: 'success', text: '处理完成' }
}))
const detailData = ref({
  id: '',
  type: '',
  description: '',
  poster: '',
  posterMobile: '',
  buildName: '',
  companyName: '',
  status: 0,
  createTime: '',
  updateTime: '',
  processor: '',
  processTime: '',
  processRemark: '',
  processResult: '',
  syncReport: false,
  floorName: '',
  createUser: '',
  updateUser: '',
  complainReportImage: {},
  processReportImage: {}
})
const getDetailData = async id => {
  try {
    const response = await get(`/build/complain/${id}`)
    return response.data || {}
  } catch (error) {
    console.error('获取详情数据失败:', error)
    return {}
  }
}
onMounted(() => {
  if (props.id) {
    getDetailData(props.id).then(data => {
      detailData.value = data
    })
  }
})

const getStatusTagType = status => statusConfig.value[status]?.type || ''
const getStatusText = status => statusConfig.value[status]?.text || '未知状态'

const emit = defineEmits(['update:visible'])

const handleClose = () => {
  emit('update:visible', false)
}
</script>
