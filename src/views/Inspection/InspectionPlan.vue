<template>
  <div class="mb-3">
    <el-form :inline="true" label-width="auto">
      <el-form-item label="名称">
        <el-input v-model="queryParams.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="状态">
        <el-select
          v-model="queryParams.statusList"
          placeholder="请选择状态"
          multiple
          style="width: 200px"
        >
          <el-option label="未开始" value="1" />
          <el-option label="进行中" value="2" />
          <el-option label="已结束" value="3" />
          <el-option label="已停用" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()"> 查询 </el-button>
        <el-button type="primary" @click="resetTable"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </div>
  <div class="mb-2">
    <el-button v-auth="'InspectionPlanAdd'" type="primary" @click="handleAdd">
      新增
    </el-button>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
  >
    <el-table-column
      type="index"
      label="序号"
      width="60"
      align="center"
      :index="(index: number) => index + 1"
    />
    <el-table-column label="公司名称" prop="companyName" />
    <el-table-column label="状态" prop="status">
      <template #default="{ row }">
        {{
          row.status == 1
            ? '未开始'
            : row.status == 2
              ? '进行中'
              : row.status == 3
                ? '已结束'
                : row.status == 4
                  ? '已停用'
                  : '-'
        }}
      </template>
    </el-table-column>
    <el-table-column label="名称" prop="name" />
    <el-table-column label="循环周期" prop="cycleTypeStr" />
    <el-table-column label="开始时间" prop="beginTime">
      <template #default="{ row }">
        <el-text class="w-150px mb-2" truncated>
          {{ row.beginTime.split(' ')[0] }}
        </el-text>
      </template>
    </el-table-column>
    <el-table-column label="结束时间" prop="endTime">
      <template #default="{ row }">
        <el-text class="w-150px mb-2" truncated>
          {{ row.endTime.split(' ')[0] }}
        </el-text>
      </template>
    </el-table-column>
    <el-table-column
      v-if="
        hasAuth('InspectionPlanDetial') ||
        hasAuth('InspectionPlanStart') ||
        hasAuth('InspectionPlanStop') ||
        hasAuth('InspectionPlanDel')
      "
      label="操作"
      width="200"
    >
      <template #default="scope">
        <el-button
          v-auth="'InspectionPlanDetial'"
          size="small"
          type="primary"
          link
          @click="handleDetial(scope.row)"
        >
          详情
        </el-button>
        <el-button
          v-if="scope.row.status === 4"
          v-auth="'InspectionPlanStart'"
          size="small"
          type="warning"
          link
          @click="handleStart(scope.row)"
        >
          启用
        </el-button>
        <el-button
          v-if="scope.row.status === 1 || scope.row.status === 2"
          v-auth="'InspectionPlanStop'"
          size="small"
          type="danger"
          link
          @click="handleStop(scope.row)"
        >
          禁用
        </el-button>
        <el-popconfirm
          class="box-item"
          title="确认删除该资源吗"
          placement="top-start"
          @confirm="handleDelete(scope.row)"
        >
          <template #reference>
            <el-button
              v-auth="'InspectionPlanDel'"
              size="small"
              type="danger"
              link
            >
              删除
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>
  <dialogForm ref="formRef" @ok="loadTableData(1)" />
  <planDialogDetial ref="detialRef" v-model="visible" :data="formData" />
</template>
<script setup lang="ts">
import { ElButton, ElTable, ElTableColumn, ElMessage } from 'element-plus'
import { onMounted, ref, computed } from 'vue'
import TableComposition from '@/composition/TableComposition'
import { useRouter } from 'vue-router'
import dialogForm from '@v/Inspection/components/planDialogForm.vue'
import { put } from '@/utils/http/request'
import planDialogDetial from '@v/Inspection/components/planDialogDetial.vue'
const router = useRouter()
import { hasAuth } from '@/router/utils'
const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete
} = TableComposition({
  urls: {
    list: '/build/check/plan/page',
    normal: '/build/check/plan',
    rowKey: 'id'
  }
})
// 搜索
const search = () => {
  console.log('search', queryParams.value)
  loadTableData(1)
}
const handleStart = row => {
  console.log('handleStart', row)
  put('/build/check/plan/enable/' + row.id).then(res => {
    if (res && res.success) {
      ElMessage({
        message: '启动成功',
        type: 'success'
      })
      loadTableData(1)
    } else {
      ElMessage({
        message: res.msg,
        type: 'error'
      })
    }
  })
}
const handleStop = row => {
  console.log('handleStop', row)
  put('/build/check/plan/disable/' + row.id).then(res => {
    if (res && res.success) {
      ElMessage({
        message: '禁用成功',
        type: 'success'
      })
      loadTableData(1)
    } else {
      ElMessage({
        message: res.msg,
        type: 'error'
      })
    }
  })
}
const detialRef = ref()
const visible = ref(false)
const formData = ref<any>({})
const handleDetial = row => {
  visible.value = true
  formData.value = row
}
onMounted(() => {
  loadTableData(1)
})
</script>
