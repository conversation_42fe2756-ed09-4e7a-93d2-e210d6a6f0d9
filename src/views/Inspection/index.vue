<template>
  <div class="mb-3">
    <el-form :inline="true" label-width="auto">
      <el-form-item label="巡检点">
        <el-input v-model="queryParams.position" placeholder="请输入巡检点" />
      </el-form-item>
      <el-form-item label="标签编号">
        <el-input v-model="queryParams.code" placeholder="请输入标签编号" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()"> 查询 </el-button>
        <el-button type="primary" @click="resetTable"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </div>
  <div class="mb-2">
    <el-button v-auth="'InspectionAdd'" type="primary" @click="handleAdd">
      新增
    </el-button>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
  >
    <el-table-column
      type="index"
      label="序号"
      width="60"
      align="center"
      :index="(index: number) => index + 1"
    />
    <el-table-column label="巡检点" prop="position" />
    <el-table-column label="标签编号" prop="code" />
    <el-table-column label="点位类型" prop="translation.pointTypeStr" />
    <el-table-column label="关联单位" prop="companyName" />
    <el-table-column label="操作" width="200">
      <template #default="scope">
        <el-button
          size="small"
          type="success"
          link
          @click="showCode(scope.row)"
        >
          查看二维码
        </el-button>
        <el-button
          v-auth="'InspectionEdit'"
          size="small"
          type="primary"
          link
          @click="handleEdit(scope.row)"
        >
          编辑
        </el-button>
        <el-popconfirm
          class="box-item"
          title="确认删除该资源吗"
          placement="top-start"
          @confirm="handleDelete(scope.row)"
        >
          <template #reference>
            <el-button v-auth="'InspectionDel'" size="small" type="danger" link>
              删除
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>
  <dialogForm ref="formRef" @ok="loadTableData(1)" />
  <!-- 二维码弹窗 -->
  <el-dialog
    v-model="qrcodeDialogVisible"
    title="建筑二维码"
    width="350px"
    :close-on-click-modal="false"
  >
    <div v-if="qrcodeUrl" class="text-center">
      <img
        :src="qrcodeUrl"
        alt="二维码"
        style="display: block; width: 250px; margin: 0 auto"
      />
    </div>
    <div v-else class="text-center text-gray-400">暂无二维码</div>
  </el-dialog>
</template>
<script setup lang="ts">
import { ElButton, ElMessage, ElTable, ElTableColumn } from 'element-plus'
import { onMounted, ref } from 'vue'
import TableComposition from '@/composition/TableComposition'
import { useRouter } from 'vue-router'
import dialogForm from '@v/Inspection/components/dialogForm.vue'
import URLS from '@/api/URLS'
const router = useRouter()

const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit
} = TableComposition({
  urls: {
    list: '/build/check/point/page',
    normal: '/build/check/point',
    rowKey: 'id'
  }
})
// 搜索
const search = () => {
  console.log('search', queryParams.value)
  loadTableData(1)
}

onMounted(() => {
  loadTableData(1)
})
const qrcodeDialogVisible = ref(false)
const qrcodeUrl = ref('')
const showCode = data => {
  if (data.checkPointQrCode[0]?.id) {
    qrcodeUrl.value = URLS.imgFileUrl(data.checkPointQrCode[0]?.id)
    qrcodeDialogVisible.value = true
  } else {
    console.log('未找到二维码图片')
    qrcodeUrl.value = ''
    qrcodeDialogVisible.value = false
    ElMessage.warning('未找到二维码图片')
  }
}
</script>
