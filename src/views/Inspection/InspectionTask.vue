<template>
  <div class="mb-3">
    <el-form :inline="true" label-width="auto">
      <el-form-item label="名称">
        <el-input v-model="queryParams.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="状态">
        <el-select
          v-model="queryParams.statusList"
          placeholder="请选择状态"
          multiple
          style="width: 200px"
        >
          <el-option label="待巡检" value="1" />
          <el-option label="巡检中" value="2" />
          <el-option label="已巡检" value="3" />
          <el-option label="已取消" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否逾期">
        <el-select
          v-model="queryParams.overdue"
          placeholder="请选择"
          style="width: 200px"
        >
          <el-option label="未逾期" value="0" />
          <el-option label="逾期" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          v-model="queryParams.timeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()"> 查询 </el-button>
        <el-button type="primary" @click="resetTable"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </div>
  <div class="mb-2">
    <el-button
      v-auth="'InspectionTaskAdd'"
      type="primary"
      @click="handleCompose"
    >
      生成任务
    </el-button>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
  >
    <el-table-column
      type="index"
      label="序号"
      width="60"
      align="center"
      :index="(index: number) => index + 1"
    />
    <el-table-column label="单位名称" prop="companyName" />
    <el-table-column label="名称" prop="name" />
    <el-table-column label="状态" prop="status">
      <template #default="{ row }">
        {{
          row.status === 1
            ? '待巡检'
            : row.status === 2
              ? '巡检中'
              : row.status === 3
                ? '已巡检'
                : row.status === 4
                  ? '已取消'
                  : '-'
        }}
      </template>
    </el-table-column>
    <el-table-column label="是否逾期" prop="overdue">
      <template #default="{ row }">
        {{ row.overdue === 0 ? '未逾期' : row.overdue === 1 ? '逾期' : '-' }}
      </template>
    </el-table-column>
    <el-table-column label="计划开始时间" prop="planBeginTime" />
    <el-table-column label="计划结束时间" prop="planEndTime" />
    <el-table-column label="实际结束时间" prop="endTime" />
    <el-table-column label="操作" width="200">
      <template #default="scope">
        <el-button
          size="small"
          type="primary"
          link
          @click="handleDetial(scope.row)"
        >
          详情
        </el-button>
        <el-button
          v-show="scope.row.status === 1"
          v-auth="'InspectionTaskCancel'"
          size="small"
          type="warning"
          link
          @click="handleCancel(scope.row)"
        >
          取消任务
        </el-button>
        <el-popconfirm
          class="box-item"
          title="确认删除该资源吗"
          placement="top-start"
          @confirm="handleDelete(scope.row)"
        >
          <template #reference>
            <el-button
              v-auth="'InspectionTaskDel'"
              size="small"
              type="danger"
              link
            >
              删除
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>
  <el-dialog v-model="tablevisible" title="请选择" width="1000">
    <checkItemsTable
      v-if="tablevisible"
      ref="tableRef"
      :tableDataChose="tableDataChose"
      :url="choseTabelUrl"
      :column="column"
      :searchParams="searchParams"
      @ok="
        () => {
          tablevisible = false
          loadTableData(1)
        }
      "
    />
  </el-dialog>
  <planDialogDetial ref="detialRef" v-model="visible" :data="formData" />
</template>
<script setup lang="ts">
import { ElButton, ElTable, ElTableColumn, ElMessage } from 'element-plus'
import { onMounted, ref, computed } from 'vue'
import TableComposition from '@/composition/TableComposition'
import { useRouter } from 'vue-router'
import { put } from '@/utils/http/request'
import planDialogDetial from '@v/Inspection/components/taskDialogDetial.vue'
import checkItemsTable from '@v/Inspection/components/checkItemsTable/index.vue'
const router = useRouter()
import { hasAuth } from '@/router/utils'
const tablevisible = ref(false)
const tableDataChose = ref([])
const choseTabelUrl = ref('/build/check/plan/page')
const column = [
  {
    prop: 'companyName',
    label: '公司名称'
  },
  {
    prop: 'status',
    label: '状态'
  },
  {
    prop: 'name',
    label: '名称'
  },
  {
    prop: 'cycleTypeStr',
    label: '循环周期'
  },
  {
    prop: 'endTime',
    label: '开始时间'
  },
  {
    prop: 'endTime',
    label: '结束时间'
  }
]
const searchParams = [
  {
    prop: 'position',
    label: '巡检点'
  }
]
const tableRef = ref()
const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete
} = TableComposition({
  urls: {
    list: '/build/check/task/page',
    normal: '/build/check/task',
    rowKey: 'id'
  }
})
// 搜索
const search = () => {
  console.log('search', queryParams.value)
  queryParams.value.startTime = queryParams.value.timeRange
    ? queryParams.value.timeRange[0] + 'T00:00:00'
    : ''
  queryParams.value.endTime = queryParams.value.timeRange
    ? queryParams.value.timeRange[1] + 'T23:59:59'
    : ''
  loadTableData(1)
}
const handleCompose = () => {
  tablevisible.value = true
}
const handleCancel = row => {
  console.log('handleCancel', row)
  put('/build/check/task/cancel/' + row.id).then(res => {
    if (res && res.success) {
      ElMessage({
        message: '取消成功',
        type: 'success'
      })
      loadTableData(1)
    } else {
      ElMessage({
        message: res.msg,
        type: 'error'
      })
    }
  })
}

const detialRef = ref()
const visible = ref(false)
const formData = ref<any>({})
const handleDetial = row => {
  visible.value = true
  formData.value = row
}
onMounted(() => {
  loadTableData(1)
})
</script>
