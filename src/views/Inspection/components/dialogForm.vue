<script setup lang="ts">
import { reactive, watch, ref } from 'vue'
import FormComposition from '@/composition/FormCompostion'
import { get } from '@/utils/http/request'
import DictSelector from '@c/Dict/dict'
import PaginationSelect from '@/components/paginationSelect/index.vue'
const emits = defineEmits(['ok'])
const dealRecord = (record: any) => {
  console.log('dealRecord', record)
  formData.value.itemResultType = String(record.itemResultType)
}

const rules = reactive({
  position: [{ required: true, message: '请输入巡查点', trigger: 'blur' }],
  code: [{ required: true, message: '请输入标签编号', trigger: 'blur' }],
  pointType: [{ required: true, message: '请输选择点位类型', trigger: 'blur' }],
  companyId: [{ required: true, message: '请输选择单位', trigger: 'blur' }]
})

const dealSubmitParams = (data: any) => {
  console.log('dealSubmitParams', data)
}
const {
  visible,
  title,
  formData,
  formRef,
  loading,
  handleAdd,
  handleEdit,
  handleSubmit,
  handleClose
} = FormComposition({
  urls: {
    normal: '/build/check/point',
    detail: '/build/check/point',
    rowKey: 'id'
  },
  dealRecord,
  dealSubmitParams,
  emits,
  addInit: async () => {
    const code = await getCode()
    formData.value.code = code
  }
})

const getCode = async () => {
  const data = await get('/build/snowflakeId')
  return data
}

defineExpose({ handleAdd, handleEdit })
</script>

<template>
  <el-dialog v-model="visible" :title="title" width="500">
    <el-form
      ref="formRef"
      :model="formData"
      label-width="120px"
      :rules="rules"
      class="check-form"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="巡检点" prop="position">
            <el-input v-model="formData.position" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="标签编号" prop="code">
            <el-input v-model="formData.code" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="点位类型" prop="pointType">
            <dict-selector v-model="formData.pointType" parentKey="011000" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="被巡查单位" prop="companyId">
            <pagination-select
              v-model="formData.companyId"
              label-key="name"
              value-key="id"
              placeholder="请选择被巡查单位"
              :filterable="true"
              :url="'/build/company/page'"
              search-key="name"
              :disabled="title === '编辑'"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.option-list {
  margin-top: 8px;
  margin-left: 100px;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.option-label {
  width: 60px;
  padding: 2px 4px;
  margin-right: 6px;
  font-weight: bold;
  text-align: right;
  background: #eee;
  border-radius: 2px;
}
</style>
