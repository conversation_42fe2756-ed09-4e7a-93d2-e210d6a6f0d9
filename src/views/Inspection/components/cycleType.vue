<script setup lang="ts">
import { reactive, Ref, ref } from 'vue'

const value: any = defineModel()

const handleBuildChange = e => {
  console.log(e)
  if (e == 2) {
    cycleTimeList.value = [{ startTime: '00:00', endTime: '23:59' }]
  } else {
    cycleTimeList.value = []
  }
}
const cycleTypeOption = [
  {
    label: '不循环',
    value: '1'
  },
  {
    label: '每天',
    value: '2'
  },
  {
    label: '每周（按时间点）',
    value: '3'
  },
  {
    label: '每周（按时间段）',
    value: '4'
  },
  {
    label: '每月（按时间点）',
    value: '5'
  },
  {
    label: '每月（按时间段）',
    value: '6'
  },
  {
    label: '每季度',
    value: '7'
  }
]
const formRef = ref()
const submit = () => {
  formRef.value.validate(async valid => {
    console.log(valid, 'valid')
    if (valid) {
      if (formData.value.timeRange) {
        formData.value.startTime = formData.value.timeRange[0] || ''
        formData.value.endTime = formData.value.timeRange[1] || ''
      }
      if (formData.value?.index) {
        cycleTimeList.value[formData.value?.index] = {
          ...cycleTimeList.value[formData.value?.index],
          ...formData.value
        }
      } else {
        cycleTimeList.value.push({ ...formData.value })
      }
      visible.value = false
      console.log(cycleTimeList.value)
    }
  })
}
const visible = ref(false)

const formData: Ref<any> = ref({})

const rules = reactive({
  timeRange: [{ required: true, message: '请选择时间', trigger: 'blur' }],
  selectDay: [{ required: true, message: '请选择时间', trigger: 'blur' }],
  startDay: [{ required: true, message: '请选择时间', trigger: 'blur' }]
})

const dayOption = [
  {
    label: '星期一',
    value: '1'
  },
  {
    label: '星期二',
    value: '2'
  },
  {
    label: '星期三',
    value: '3'
  },
  {
    label: '星期四',
    value: '4'
  },
  {
    label: '星期五',
    value: '5'
  },
  {
    label: '星期六',
    value: '6'
  },
  {
    label: '星期日',
    value: '7'
  }
]

const cycleTimeList = ref([])
const delItem = (item, index) => {
  cycleTimeList.value.splice(index, 1)
}
const editItem = (item, index) => {
  formData.value = { ...item, index: String(index) }
  visible.value = true
}
const addtime = () => {
  formData.value = {}
  visible.value = true
}
const getDaytext = val => {
  let arr = []
  if (['3', '4'].includes(value.value)) {
    arr = dayOption
  } else if (['5', '6'].includes(value.value)) {
    arr = monDay()
  }
  return arr.find(item => item.value == val)?.label || ''
}
const monDay = (): any => {
  let arr = []
  for (let i = 0; i < 31; i++) {
    arr.push({
      label: `每月${i + 1}日`,
      value: i + 1
    })
  }
  return arr
}
defineExpose({ cycleTimeList })
</script>

<template>
  <div class="w-full">
    <el-select
      v-model="value"
      :style="{
        width: ['2', '3', '4', '5', '6'].includes(String(value))
          ? '460px'
          : '100%'
      }"
      @change="handleBuildChange"
    >
      <el-option
        v-for="item in cycleTypeOption"
        :key="item.value"
        :value="item.value"
        :label="item.label"
      />
    </el-select>
    <el-button
      v-show="['2', '3', '4', '5', '6'].includes(String(value))"
      type="default"
      size="default"
      @click="addtime"
      >新增时间
    </el-button>

    <div class="list max-h-52 overflow-auto">
      <div
        v-for="(item, index) in cycleTimeList"
        :key="index"
        class="flex justify-between"
      >
        <div>
          时间{{ index + 1 }}：
          <template v-if="item.selectDay">
            <span v-for="(list, index) in item.selectDay" :key="index">
              {{ getDaytext(list) + '  ' }}
            </span>
          </template>
          <span v-if="item.startTime || item.endTime">
            {{ item.startTime }} ~ {{ item.endTime }}
          </span>
          <span v-if="item.startDay || item.endDay">
            {{ getDaytext(item.startDay) }} ~ {{ getDaytext(item.endDay) }}
          </span>
        </div>
        <div>
          <el-link type="primary" class="mr-2.5" @click="editItem(item, index)"
            >编辑</el-link
          >
          <el-link type="danger" @click="delItem(item, index)">删除</el-link>
        </div>
      </div>
    </div>

    <el-dialog v-model="visible" title="新增巡检周期时间范围" width="600">
      <el-form
        ref="formRef"
        :model="formData"
        label-width="120px"
        :rules="rules"
        class="check-form"
      >
        <el-row :gutter="20">
          <el-col v-if="value == 3" :span="24">
            <el-form-item label="选择" prop="selectDay">
              <el-select v-model="formData.selectDay" multiple>
                <el-option
                  v-for="item in dayOption"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="value == 5" :span="24">
            <el-form-item label="选择" prop="selectDay">
              <el-select v-model="formData.selectDay" multiple>
                <el-option
                  v-for="item in monDay()"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="['2', '3', '5'].includes(value)" :span="24">
            <el-form-item label="时间范围" prop="timeRange">
              <el-time-picker
                v-model="formData.timeRange"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间范围"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="value == 4" :span="24">
            <el-form-item label="时间范围" prop="startDay">
              <div class="flex">
                <el-select v-model="formData.startDay" style="width: 220px">
                  <el-option
                    v-for="item in dayOption"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                  />
                </el-select>
                ~
                <el-select v-model="formData.endDay" style="width: 220px">
                  <el-option
                    v-for="item in dayOption"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                  />
                </el-select>
              </div>
            </el-form-item>
          </el-col>
          <el-col v-if="value == 6" :span="24">
            <el-form-item label="时间范围" prop="startDay">
              <div class="flex">
                <el-select v-model="formData.startDay" style="width: 220px">
                  <el-option
                    v-for="item in 31"
                    :key="item"
                    :value="item"
                    :label="`每月${item}日`"
                  />
                </el-select>
                ~
                <el-select v-model="formData.endDay" style="width: 220px">
                  <el-option
                    v-for="item in 31"
                    :key="item"
                    :value="item"
                    :label="`每月${item}日`"
                  />
                </el-select>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submit"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 18px !important;
}
</style>
