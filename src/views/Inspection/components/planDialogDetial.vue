<template>
  <el-dialog v-model="visible" :title="title" width="800" @close="handleClose">
    <el-descriptions title="">
      <el-descriptions-item label="计划编号：">
        {{ formData.no || '' }}
      </el-descriptions-item>
      <el-descriptions-item label="循环周期：">{{
        formData.cycleTypeStr || ''
      }}</el-descriptions-item>
      <el-descriptions-item label="开始时间：">
        {{ formData.beginTime?.split(' ')[0] || '' }}
      </el-descriptions-item>
      <el-descriptions-item label="结束时间：">{{
        formData.endTime?.split(' ')[0] || ''
      }}</el-descriptions-item>
      <el-descriptions-item
        v-if="formData.cycleTimeList?.length"
        label="循环时间："
      >
        <div class="list max-h-52 overflow-auto inline-flex">
          <div
            v-for="(item, index) in formData.cycleTimeList"
            :key="index"
            class="flex justify-between inline pr-2.5"
          >
            <div>
              时间{{ index + 1 }}：
              <template v-if="item.selectDay">
                <span v-for="(list, index) in item.selectDay" :key="index">
                  {{ getDaytext(list) }}&nbsp;&nbsp;
                </span>
              </template>
              <span v-if="item.startDay || item.endDay">
                {{ getDaytext(item.startDay) }} ~
                {{ getDaytext(item.endDay) }}&nbsp;&nbsp;
              </span>
              <span v-if="item.startTime || item.endTime">
                {{ formatDate(item.startTime, 'HH:mm') }} ~
                {{ formatDate(item.endTime, 'HH:mm') }}
              </span>
            </div>
          </div>
        </div>
      </el-descriptions-item>
    </el-descriptions>
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      row-key="id"
      style="width: 100%"
      max-height="400"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center' }"
    >
      <el-table-column
        type="index"
        label="序号"
        width="60"
        align="center"
        :index="(index: number) => index + 1"
      />
      <el-table-column label="巡检点" prop="position" />
    </el-table>
    <div class="flex justify-end mt-2">
      <el-pagination
        v-model:current-page="pageInfo.pageNum"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="pageSizeList"
        :page-size="pageInfo.pageSize"
        :total="pageInfo.totalRecords"
        @size-change="handlePaginationSizeChange"
        @current-change="handlePaginationPageChange"
      />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineModel, defineProps, watch } from 'vue'
import { get } from '@/utils/http/request'
import { useUserStore } from '@/store/modules/user'
import TableComposition from '@/composition/TableComposition'
import { formatDate } from '@/utils/date'
const dayOption = [
  {
    label: '星期一',
    value: '1'
  },
  {
    label: '星期二',
    value: '2'
  },
  {
    label: '星期三',
    value: '3'
  },
  {
    label: '星期四',
    value: '4'
  },
  {
    label: '星期五',
    value: '5'
  },
  {
    label: '星期六',
    value: '6'
  },
  {
    label: '星期日',
    value: '7'
  }
]
const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit,
  defaultQueryParams
} = TableComposition({
  urls: {
    list: '/build/check/plan/pageCheckPoint',
    normal: '/build/check/plan/pageCheckPoint',
    rowKey: 'id'
  }
})
const monDay = (): any => {
  let arr = []
  for (let i = 0; i < 31; i++) {
    arr.push({
      label: `每月${i + 1}日`,
      value: i + 1
    })
  }
  return arr
}
const getDaytext = val => {
  let arr = []
  if (['3', '4'].includes(String(formData.value.cycleType))) {
    arr = dayOption
  } else if (['5', '6'].includes(String(formData.value.cycleType))) {
    arr = monDay()
  }
  return arr.find(item => item.value == val)?.label || ''
}
const userStore = useUserStore()
const visible = defineModel<boolean>()
const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  }
})
const title = ref('详情')
const formData = ref<any>({})
const tableDataChose = ref<any[]>([])

const handleClose = () => {
  visible.value = false
}

//获取详情
const getDetail = async () => {
  const data = await get('/build/check/plan/' + props.data.id)
  formData.value = data.data || {}
  tableDataChose.value = data.data?.checkPointList || []
  defaultQueryParams.checkPlanId = props.data.id
  loadTableData(1)
}
watch(
  () => visible.value,
  () => {
    if (visible.value) {
      getDetail()
    }
  }
)
// 获取巡检点

defineExpose({ getDetail })
</script>
