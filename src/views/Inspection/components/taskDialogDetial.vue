<template>
  <el-dialog v-model="visible" :title="title" width="800" @close="handleClose">
    <el-descriptions title="">
      <el-descriptions-item label="计划编号：">
        {{ formData.no || '' }}
      </el-descriptions-item>
      <el-descriptions-item label="巡检时间：">
        {{ formData.endTime || '未开始巡检' }}
      </el-descriptions-item>
    </el-descriptions>
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      row-key="id"
      style="width: 100%"
      max-height="400"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center' }"
    >
      <el-table-column
        type="index"
        label="序号"
        width="60"
        align="center"
        :index="(index: number) => index + 1"
      />
      <el-table-column label="巡检点" prop="position" />
      <el-table-column label="编码" prop="code" />
      <el-table-column label="公司" prop="companyName" />
      <el-table-column label="点位类型" prop="translation.pointTypeStr" />
      <el-table-column label="是否已签到" prop="signIn">
        <template #default="{ row }">
          {{ row.signIn ? '已签到' : '未签到' }}
        </template>
      </el-table-column>
      <el-table-column label="签到时间" prop="signInTime" />
    </el-table>
    <div class="flex justify-end mt-2">
      <el-pagination
        v-model:current-page="pageInfo.pageNum"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="pageSizeList"
        :page-size="pageInfo.pageSize"
        :total="pageInfo.totalRecords"
        @size-change="handlePaginationSizeChange"
        @current-change="handlePaginationPageChange"
      />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineModel, defineProps, watch } from 'vue'
import { get } from '@/utils/http/request'
import { useUserStore } from '@/store/modules/user'
import TableComposition from '@/composition/TableComposition'
import { formatDate } from '@/utils/date'
const dayOption = [
  {
    label: '星期一',
    value: '1'
  },
  {
    label: '星期二',
    value: '2'
  },
  {
    label: '星期三',
    value: '3'
  },
  {
    label: '星期四',
    value: '4'
  },
  {
    label: '星期五',
    value: '5'
  },
  {
    label: '星期六',
    value: '6'
  },
  {
    label: '星期日',
    value: '7'
  }
]
const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit,
  defaultQueryParams
} = TableComposition({
  urls: {
    list: '/build/check/task/pageCheckPoint',
    normal: '/build/check/task/pageCheckPoint',
    rowKey: 'id'
  }
})
const monDay = (): any => {
  let arr = []
  for (let i = 0; i < 31; i++) {
    arr.push({
      label: `每月${i + 1}日`,
      value: i + 1
    })
  }
  return arr
}
const getDaytext = val => {
  let arr = []
  if (['3', '4'].includes(String(formData.value.cycleType))) {
    arr = dayOption
  } else if (['5', '6'].includes(String(formData.value.cycleType))) {
    arr = monDay()
  }
  return arr.find(item => item.value == val)?.label || ''
}
const userStore = useUserStore()
const visible = defineModel<boolean>()
const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  }
})
const title = ref('详情')
const formData = ref<any>({})
const tableDataChose = ref<any[]>([])

const handleClose = () => {
  visible.value = false
}

//获取详情
const getDetail = async () => {
  const data = await get('/build/check/task/' + props.data.id)
  formData.value = data.data || {}
  tableDataChose.value = data.data?.checkPointList || []
  defaultQueryParams.checkTaskId = props.data.id
  loadTableData(1)
}
watch(
  () => visible.value,
  () => {
    if (visible.value) {
      getDetail()
    }
  }
)
// 获取巡检点

defineExpose({ getDetail })
</script>
