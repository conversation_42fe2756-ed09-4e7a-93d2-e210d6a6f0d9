<template>
  <div class="mb-3">
    <el-form :inline="true" label-width="auto">
      <el-form-item label="名称">
        <el-input v-model="queryParams.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="状态">
        <el-select
          v-model="queryParams.statusList"
          placeholder="请选择状态"
          multiple
          style="width: 200px"
        >
          <el-option label="未开始" value="1" />
          <el-option label="进行中" value="2" />
          <el-option label="已结束" value="3" />
          <el-option label="已停用" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()"> 查询 </el-button>
        <el-button type="primary" @click="resetTable"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </div>
  <div class="mb-2">
    <el-button type="primary" @click="handleCompose"> 生成任务 </el-button>
  </div>
  <el-table
    ref="tableRef"
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
    class="check-table"
    @selection-change="handleSelectionChange"
    @select="handleSelect"
    @select-all="handleSelectAll"
  >
    <el-table-column
      type="selection"
      width="50"
      align="center"
      :selectable="row => row.status !== 4 || row.status === 3"
    />
    <el-table-column label="公司名称" prop="companyName" />
    <el-table-column label="状态" prop="status">
      <template #default="{ row }">
        {{
          row.status == 1
            ? '未开始'
            : row.status == 2
              ? '进行中'
              : row.status == 3
                ? '已结束'
                : row.status == 4
                  ? '已停用'
                  : '-'
        }}
      </template>
    </el-table-column>
    <el-table-column label="名称" prop="name" />
    <el-table-column label="循环周期" prop="cycleTypeStr" />
    <el-table-column label="开始时间" prop="beginTime">
      <template #default="{ row }">
        <el-text class="w-150px mb-2" truncated>
          {{ row.beginTime.split(' ')[0] }}
        </el-text>
      </template>
    </el-table-column>
    <el-table-column label="结束时间" prop="endTime">
      <template #default="{ row }">
        <el-text class="w-150px mb-2" truncated>
          {{ row.beginTime.split(' ')[0] }}
        </el-text>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="200">
      <template #default="scope">
        <el-button
          size="small"
          type="primary"
          link
          @click="handleDetial(scope.row)"
        >
          详情
        </el-button>
        <el-button
          v-if="scope.row.status === 4"
          size="small"
          type="warning"
          link
          @click="handleStart(scope.row)"
        >
          启用
        </el-button>
        <el-button
          v-if="scope.row.status === 1 || scope.row.status === 2"
          size="small"
          type="danger"
          link
          @click="handleStop(scope.row)"
        >
          禁用
        </el-button>
        <el-popconfirm
          class="box-item"
          title="确认删除该资源吗"
          placement="top-start"
          @confirm="handleDelete(scope.row)"
        >
          <template #reference>
            <el-button size="small" type="danger" link> 删除 </el-button>
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>
  <planDialogDetial ref="detialRef" v-model="visible" :data="formData" />
</template>
<script setup lang="ts">
import { ElButton, ElMessage, ElTable, ElTableColumn } from 'element-plus'
import { ref, onMounted, watch, nextTick, inject } from 'vue'
import TableComposition from '@/composition/TableComposition'
import { useRouter } from 'vue-router'
import { put } from '@/utils/http/request'
import planDialogDetial from '@v/Inspection/components/planDialogDetial.vue'
const emits = defineEmits(['ok'])
const router = useRouter()
const visible = ref(false)
const formData = ref<any>({})
const handleDetial = row => {
  visible.value = true
  formData.value = row
}
const handleStop = row => {
  console.log('handleStop', row)
  put('/build/check/plan/disable/' + row.id).then(res => {
    if (res && res.success) {
      ElMessage({
        message: '禁用成功',
        type: 'success'
      })
      loadTableData(1)
    } else {
      ElMessage({
        message: res.msg,
        type: 'error'
      })
    }
  })
}
const handleStart = row => {
  console.log('handleStart', row)
  put('/build/check/plan/enable/' + row.id).then(res => {
    if (res && res.success) {
      ElMessage({
        message: '启动成功',
        type: 'success'
      })
      loadTableData(1)
    } else {
      ElMessage({
        message: res.msg,
        type: 'error'
      })
    }
  })
}
const props = defineProps<{
  tableDataChose: any[]
  url: string
  column: any[]
}>()
const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit
} = TableComposition({
  urls: {
    list: props.url,
    normal: props.url,
    rowKey: 'id'
  }
})
// 搜索
const search = () => {
  console.log('search', queryParams.value)
  loadTableData(1)
}
onMounted(() => {
  loadTableData(1)
  selectedRows.value = props.tableDataChose || []
  console.log('checkItemsTable mounted', props.tableDataChose)
  // 首次加载后自动回显勾选
  restoreSelection()
})
const handleCompose = () => {
  console.log('handleCompose')
  const arr = selectedRows.value.map((row: any) => row.id)
  put('/build/check/task/generateTask', arr).then(res => {
    if (res && res.success) {
      ElMessage({
        message: res.msg,
        type: 'success'
      })
      emits('ok')
    } else {
      ElMessage({
        message: res.msg,
        type: 'error'
      })
    }
  })
}

// 监听 tableData 变化，自动回显勾选
watch(tableData, () => {
  restoreSelection()
})

const tableRef = ref()
// 分页勾选核心：全局维护已选项
const selectedRows = ref<any[]>([])
function uniqueByRowKey(arr: any[], rowKey = 'id') {
  const map = new Map()
  arr.forEach(item => {
    if (item && item[rowKey] !== undefined) {
      map.set(item[rowKey], item)
    }
  })
  return Array.from(map.values())
}
function handleSelectionChange(selection: any[]) {
  const currentPageIds = tableData.value.map((row: any) => row.id)
  selectedRows.value = selectedRows.value.filter(
    row => !currentPageIds.includes(row.id)
  )
  selectedRows.value = uniqueByRowKey([...selectedRows.value, ...selection])
  console.log('handleSelectionChange', selection, selectedRows.value)
}
function handleSelect(selection: any[], row: any) {
  handleSelectionChange(selection)
}
function handleSelectAll(selection: any[]) {
  handleSelectionChange(selection)
}
function restoreSelection() {
  if (!tableRef.value) return
  const currentPageIds = tableData.value.map((row: any) => row.id)
  const needSelect = selectedRows.value.filter(row =>
    currentPageIds.includes(row.id)
  )
  tableRef.value.clearSelection && tableRef.value.clearSelection()
  nextTick(() => {
    needSelect.forEach(row => {
      const realRow = tableData.value.find((r: any) => r.id === row.id)
      // 只有状态不为3的行才能被选中
      if ((realRow && realRow.status !== 3) || realRow.status !== 4) {
        tableRef.value.toggleRowSelection(realRow, true)
      }
    })
  })
}

const getSelection = () => {
  return selectedRows.value
}

defineExpose({ getSelection })
</script>
