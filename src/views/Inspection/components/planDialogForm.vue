<script setup lang="ts">
import { reactive, watch, ref, nextTick } from 'vue'
import FormComposition from '@/composition/FormCompostion'
import PaginationSelect from '@/components/paginationSelect/index.vue'
import checkItemsTable from '@/components/checkItemsTable/index.vue'
import DraggableTable from '@/components/DraggableTable/DraggableTable.vue'
import cycleType from './cycleType.vue'
import { getNow } from '@/utils/date'
import { ElMessage } from 'element-plus'
const emits = defineEmits(['ok'])
const dealRecord = (record: any) => {
  console.log('dealRecord', record)
  formData.value.itemResultType = String(record.itemResultType)
}

const rules = reactive({
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  cycleType: [{ required: true, message: '请输选择周期', trigger: 'blur' }],
  timeRange: [{ required: true, message: '请输选择时间', trigger: 'blur' }],
  companyId: [{ required: true, message: '请输选择单位', trigger: 'blur' }]
})

const dealSubmitParams = (data: any) => {
  data.beginTime = formData.value.timeRange[0] + ' 00:00:00' || ''
  data.endTime = formData.value.timeRange[1] + ' 23:59:59' || ''
  data.checkPointList = tableDataChose.value.map((item, index) => {
    return {
      checkPointId: item.id,
      seq: index + 1
    }
  })
  data.cycleTimeList = cycleTypeRef.value.cycleTimeList.map(item => {
    if (item.startTime) {
      item.startTime = getNow() + ' ' + item.startTime + ':00'
    }
    if (item.endTime) {
      item.endTime = getNow() + ' ' + item.endTime + ':00'
    }
    return {
      ...item
    }
  })
  console.log('dealSubmitParams', data)
}

const {
  visible,
  title,
  formData,
  formRef,
  loading,
  handleAdd,
  handleEdit,
  handleSubmit,
  handleClose
} = FormComposition({
  urls: {
    normal: '/build/check/plan',
    detail: '/build/check/plan',
    rowKey: 'id'
  },
  dealRecord,
  dealSubmitParams,
  emits,
  addInit: async () => {
    tableDataChose.value = []
    nextTick(() => {
      cycleTypeRef.value.cycleTimeList = []
    })
  },
  validate: (data: any) => {
    if (!tableDataChose.value.length) {
      ElMessage.error('请选择巡检点')
      return false
    }
    if (
      ['2', '3', '4', '5', '6'].includes(String(data.cycleType)) &&
      !cycleTypeRef.value.cycleTimeList.length
    ) {
      ElMessage.error('请新增巡检周期')
      return false
    }
    return true
  }
})

const addTableVisible = () => {
  tablevisible.value = true
}
const tablevisible = ref(false)
const tableDataChose = ref([])
const cycleTypeRef = ref()
const choseTabelUrl = ref('/build/check/point/page')
const column = [
  {
    prop: 'companyName',
    label: '单位'
  },
  {
    prop: 'position',
    label: '巡检点'
  },
  {
    prop: 'code',
    label: '标签编号'
  }
]
const searchParams = [
  {
    prop: 'position',
    label: '巡检点'
  }
]
const tableRef = ref()
const choseSubmit = () => {
  const selectedItems = tableRef.value?.getSelection() || []
  console.log(selectedItems)
  tableDataChose.value = selectedItems
  tablevisible.value = false
}
defineExpose({ handleAdd, handleEdit })
</script>

<template>
  <el-dialog v-model="visible" :title="title" width="700">
    <el-form
      ref="formRef"
      :model="formData"
      label-width="120px"
      :rules="rules"
      class="check-form"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="名称" prop="name">
            <el-input v-model="formData.name" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="循环周期" prop="cycleType">
            <cycleType ref="cycleTypeRef" v-model="formData.cycleType" />
          </el-form-item>
        </el-col>
        <el-form-item label="时间" prop="timeRange" label-width="130px">
          <el-date-picker
            v-model="formData.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            style="width: 548px"
          />
        </el-form-item>
        <el-col :span="24">
          <el-form-item label="被巡查单位" prop="companyId">
            <pagination-select
              v-model="formData.companyId"
              label-key="name"
              value-key="id"
              placeholder="请选择被巡查单位"
              :filterable="true"
              :url="'/build/company/page'"
              search-key="name"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="formData.companyId" :span="24">
          <el-form-item label="巡检点">
            <div class="flex justify-end w-full">
              <el-button type="primary" size="small" @click="addTableVisible">
                选择
              </el-button>
            </div>
          </el-form-item>
          <DraggableTable
            :data="tableDataChose"
            :columns="[
              { prop: 'index', label: '序号', type: 'index' },
              { prop: 'position', label: '巡检点' },
              { prop: 'code', label: '标签编号' }
            ]"
            @update:data="val => (tableDataChose = val)"
          />
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 提交 </el-button>
      </div>
    </template>

    <el-dialog v-model="tablevisible" title="请选择" width="1000">
      <checkItemsTable
        v-if="tablevisible"
        ref="tableRef"
        :tableDataChose="tableDataChose"
        :url="choseTabelUrl"
        :column="column"
        :searchParams="searchParams"
        :defaultParams="{ companyIdList: [formData.companyId] }"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="choseSubmit"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<style lang="scss" scoped>
.option-list {
  margin-top: 8px;
  margin-left: 100px;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.option-label {
  width: 60px;
  padding: 2px 4px;
  margin-right: 6px;
  font-weight: bold;
  text-align: right;
  background: #eee;
  border-radius: 2px;
}
</style>
