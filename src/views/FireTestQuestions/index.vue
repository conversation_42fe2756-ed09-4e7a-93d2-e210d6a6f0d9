<template>
  <div class="mb-3">
    <el-form :inline="true" label-width="auto">
      <el-form-item label="题目名称">
        <el-input
          v-model="queryParams.questionName"
          placeholder="请输入题目名称"
        />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="queryParams.daterange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()"> 查询 </el-button>
        <el-button type="primary" @click="resetTable"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </div>
  <div class="mb-2">
    <el-button
      v-auth="'FireTestQuestionsAdd'"
      type="primary"
      @click="handleAdd"
    >
      新增
    </el-button>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
  >
    <el-table-column
      type="index"
      label="序号"
      width="60"
      align="center"
      :index="(index: number) => index + 1"
    />
    <el-table-column label="题目" prop="questionName" />
    <el-table-column label="创建时间" prop="createTime" />
    <el-table-column label="操作" width="200">
      <template #default="scope">
        <el-button
          v-auth="'FireTestQuestionsEdit'"
          size="small"
          type="primary"
          link
          @click="handleEdit(scope.row)"
        >
          编辑
        </el-button>
        <el-popconfirm
          class="box-item"
          title="确认删除该资源吗"
          placement="top-start"
          @confirm="handleDelete(scope.row)"
        >
          <template #reference>
            <el-button
              v-auth="'FireTestQuestionsDel'"
              size="small"
              type="danger"
              link
            >
              删除
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>
  <dialogForm ref="formRef" @ok="loadTableData(1)" />
</template>
<script setup lang="ts">
import { ElButton, ElTable, ElTableColumn } from 'element-plus'
import { onMounted } from 'vue'
import TableComposition from '@/composition/TableComposition'
import { useRouter } from 'vue-router'
import dialogForm from '@v/FireTestQuestions/components/dialogForm.vue'
const router = useRouter()

const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit
} = TableComposition({
  urls: {
    list: '/build/fire-test/question/page',
    normal: '/build/fire-test/question',
    rowKey: 'id'
  }
})
// 搜索
const search = () => {
  console.log('search', queryParams.value)
  if (queryParams.value.daterange && queryParams.value.daterange.length === 2) {
    queryParams.value.createTimeStart =
      queryParams.value.daterange[0] + 'T00:00:00' || ''
    queryParams.value.createTimeEnd =
      queryParams.value.daterange[1] + 'T23:59:59' || ''
  }
  loadTableData(1)
}

onMounted(() => {
  loadTableData(1)
})
</script>
