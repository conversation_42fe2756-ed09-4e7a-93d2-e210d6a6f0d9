<script setup lang="ts">
import { onMounted, provide, ref } from 'vue'
import CompanyList from '@v/highRiseMananger/company/companyList.vue'
import BuildList from '@v/highRiseMananger/company/buildList.vue'
import FloorList from '@v/highRiseMananger/company/floorList.vue'
import FloorTenantList from '@v/highRiseMananger/company/FloorTenantList.vue'
const currentTab = ref('company')
const tabList = ref([
  {
    label: '单位列表',
    name: 'company'
  }
])
const currentCompanyId = ref('')
const currentBuildId = ref('')
const currentFloorId = ref('')
const handleChangeTab = (index: number, dataId: string, name: string) => {
  if (index === 0) {
    currentTab.value = 'build'
    if (currentCompanyId.value !== dataId) {
      console.log(currentCompanyId.value, dataId)
      currentCompanyId.value = dataId
      currentBuildId.value = ''
      tabList.value = [
        {
          label: '单位列表',
          name: 'company'
        },
        {
          label: `【${name}】-楼栋`,
          name: 'build'
        }
      ]
    }
  } else if (index === 1) {
    currentTab.value = 'floor'
    console.log('build', currentBuildId.value, dataId)
    if (currentBuildId.value !== dataId) {
      currentBuildId.value = dataId
      const tabs = []
      for (let i = 0; i < 2; i++) {
        tabs.push(tabList.value[i])
      }
      tabs.push({
        label: `【${name}】-楼层`,
        name: 'floor'
      })
      tabList.value = tabs
    }
  } else if (index === 2) {
    currentTab.value = `tenant-${dataId}`
    currentFloorId.value = dataId
    // 检查是否已存在该tab
    const exist = tabList.value.find(tab => tab.name === `tenant-${dataId}`)
    if (!exist) {
      tabList.value.push({
        label: `【${name}】-入驻单位`,
        name: `tenant-${dataId}`
      })
    }
  }
  console.log('handleChangeTab', tabList.value, currentTab)
}
onMounted(() => {
  console.log('onMounted')
})
provide('currentCompanyId', currentCompanyId)
</script>

<template>
  <el-tabs v-model="currentTab">
    <el-tab-pane
      v-for="item in tabList"
      :key="item.name"
      :label="item.label"
      :name="item.name"
    >
      <company-list
        v-if="item.name === 'company'"
        @changeTab="handleChangeTab"
      />
      <build-list
        v-else-if="item.name === 'build'"
        :company-id="currentCompanyId"
        @changeTab="handleChangeTab"
      />
      <floor-list
        v-else-if="item.name === 'floor'"
        :build-id="currentBuildId"
        @changeTab="handleChangeTab"
      />
      <FloorTenantList
        v-else-if="item.name.startsWith('tenant-')"
        :floor-id="item.name.replace('tenant-', '')"
        :build-id="currentBuildId"
      />
    </el-tab-pane>
  </el-tabs>
</template>
