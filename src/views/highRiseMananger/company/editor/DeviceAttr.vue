<script setup lang="ts">
import { NodeType } from '@/types/editor/EditorTypes'
import { emitter } from '@/utils/mitt'

const currentNodeData = defineModel('currentNodeData', {
  type: Object as () => NodeType
})
defineProps({
  // 编制类型
  type: {
    type: String,
    default: '4'
  }
})
const handleInput = (v: string) => {
  console.log('handleInput', v)
  emitter.emit('nodeNameChange', v)
}
</script>

<template>
  <div>设备属性</div>
  <div v-if="currentNodeData?.type === 'device'">
    <el-form ref="formRef" :model="currentNodeData" label-width="80px">
      <el-form-item label="设备名称">
        <el-input v-model="currentNodeData.name" @input="handleInput" />
      </el-form-item>
      <template v-if="type === '4'">
        <el-form-item label="设备编号">
          <el-input v-model="currentNodeData.no" />
        </el-form-item>
      </template>
    </el-form>
  </div>
  <el-empty v-else description="请点击选择设备后设置属性" />
</template>
