<script setup lang="ts">
import { onMounted, Ref, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { StencilNode, NodeType } from '@/types/editor/EditorTypes'
import DeviceAttr from '@v/highRiseMananger/company/editor/DeviceAttr.vue'
import { get, post, put } from '@/utils/http/request'
import URLS from '@/api/URLS'
import { ElMessage } from 'element-plus'
import AntvX6Composition from '@/composition/AntvX6Composition'
import { emitter } from '@/utils/mitt'
import BuildAttr from '@v/highRiseMananger/company/editor/BuildAttr.vue'
import EditorDataComposition from '@/composition/EditorDataComposition'

const title = ref('设备编制')
const type = ref('')
const businessId = ref<string>('')
const name = ref<string>('')

const route = useRoute()

const deviceTypeList = ref<StencilNode[]>([])

const deviceTypeListArr: Ref<StencilNode[]> = ref([])
const crrentOpiton = ref([])

/**
 * 获取消防设施列表
 * @param _dictCode
 */
const initDeviceTypeList = (_dictCode: string) => {
  get(URLS.buildDict(_dictCode)).then(res => {
    if (res && res.success) {
      deviceTypeList.value = res.data.map(item => {
        const config = JSON.parse(item.config)
        return {
          id: item.code,
          label: item.value,
          deviceType: config.deviceType,
          image: URLS.imgFileUrl(config.fileId)
        }
      })
      initStencilNode(deviceTypeList.value)
    }
  })
}

/**
 * 获取设备类型列表
 */
const initDeviceTypes = () => {
  get(URLS.BUILD + '/build/device-type/all').then(res => {
    if (res && res.success) {
      deviceTypeListArr.value = res.data
      console.log('获取到设备类型列表:', deviceTypeListArr.value)
      const deviceTypeList = res.data.map(item => {
        return {
          ...item,
          label: item.deviceTypeName,
          deviceType: item.deviceTypeCode,
          image: URLS.imgFileUrl(item.deviceTypeLogo[0].id),
          type: 'device',
          deviceTypeId: item.id,
          deviceAttrs: item.deviceTypeAttrs.map(attr => {
            return {
              ...attr,
              deviceTypeAttrId: attr.id
            }
          })
        }
      })
      console.log('获取到数据:', deviceTypeList)
      initStencilNode(deviceTypeList)
    }
  })
}
import imgUrl from '@/assets/bigScreen/location.png'
import checkImgurl from '@/assets/bigScreen/checkLocation.png'
const initBuildNumNode = () => {
  initStencilNode([
    {
      id: 'buildNum',
      label: '建筑物',
      image: imgUrl,
      checkImage: checkImgurl
    }
  ])
}

const toJson = () => {
  console.log(getJson())
}

const dataId = ref('')
const backGroupImageType = ref('')

/**
 * 保存编制数据
 */
const submit = () => {
  if (!bgFlag.value) {
    ElMessage({
      message: '请上传平面图',
      type: 'error'
    })
    return
  }
  let action
  if (dataId.value) {
    action = put(URLS.BUILD + '/build/config/' + dataId.value, {
      id: dataId.value,
      businessId: businessId.value,
      config: JSON.stringify(getJson()),
      type: type.value
    })
  } else {
    action = post(URLS.BUILD + '/build/config', {
      businessId: businessId.value,
      config: JSON.stringify(getJson()),
      type: type.value
    })
  }
  action.then(res => {
    if (res && res.success) {
      ElMessage({
        message: res.msg,
        type: 'success'
      })
      if (res.data) {
        dataId.value = res.data
      }
    } else {
      ElMessage({
        message: res.msg,
        type: 'error'
      })
    }
  })
}

const handleNodeClick = (node: any) => {
  console.log('handleNodeClick', node)
  if (type.value == '4') {
    node?.deviceAttrs?.map((item: any) => {
      fromJsonData.value[item.attrCode] = item.attrValue
      if (!item.attrValue && item.attrCode == 'name') {
        fromJsonData.value[item.attrCode] = node.label
      }
    })
    crrentOpiton.value = node?.deviceAttrs || []
  } else {
    fromJsonData.value = { ...node, name: node.deviceName || node.label }
    console.log(fromJsonData.value)
  }
}
const {
  init,
  getJson,
  fromJson,
  initStencilNode,
  initBackgroundNode,
  setLabel,
  currentNode
} = AntvX6Composition(handleNodeClick, false)
const { loadConfig, bgFlag } = EditorDataComposition(
  initBackgroundNode,
  fromJson,
  dataId
)
onMounted(() => {
  type.value = route?.query?.type?.toString()
  businessId.value = route?.query?.businessId?.toString()
  name.value = route?.query?.name?.toString()
  switch (type.value) {
    case '1':
      title.value = '单位建筑物标注'
      break
    case '4':
      title.value = '物联网设备编制'
      break
    case '5':
      title.value = '建筑消防设施编制'
      break
    case '6':
      title.value = '应急逃生路线编制'
      break
  }
  init(() => {
    switch (type.value) {
      case '1':
        initBuildNumNode()
        backGroupImageType.value = 'companyLayoutImage'
        break
      case '4':
        backGroupImageType.value = 'floorLayoutImage'
        initDeviceTypes()
        break
      case '5':
        backGroupImageType.value = 'floorLayoutImage'
        initDeviceTypeList('005000')
        break
    }
    loadConfig(businessId.value, type.value, backGroupImageType.value)
  })
  emitter.on('nodeNameChange', (v: string) => {
    setLabel(v)
  })
})
const changeData = (v: string, attrCode: string) => {
  console.log('changeData', v, fromJsonData.value, attrCode)
  currentNode.value.data?.deviceAttrs?.map((item: any) => {
    if (item.attrCode === attrCode) {
      item.attrValue = v
    }
  })
  if (attrCode === 'name') {
    currentNode.value.data.deviceName = v
  }
  if (attrCode === 'code') {
    currentNode.value.data.deviceCode = v
  }
  currentNode.value.prop('attrs/label/text', fromJsonData.value.name)
  console.log('currentNode', currentNode.value)
}
const color = ref('')
const textsize = ref('')

watch(
  () => currentNode?.value,
  (newVal, oldVal) => {
    console.log('currentNodeData changed', newVal, oldVal)
    if (newVal && newVal?.data?.type !== 'background') {
      console.log('currentNodeData changed', newVal)
      color.value = newVal.attrs.label.fill || ''
      textsize.value = newVal.attrs.label.fontSize || ''
    }
  },
  { immediate: true }
)
watch(
  () => color.value,
  (newVal, oldVal) => {
    console.log('color', newVal, oldVal)
    currentNode.value.attr('label', {
      fill: newVal
    })
  }
)
watch(
  () => textsize.value,
  (newVal, oldVal) => {
    console.log('textsize', newVal, oldVal)
    currentNode.value.attr('label', {
      fontSize: newVal
    })
  }
)
const fromJsonData: any = ref({})
</script>

<template>
  <div class="w-full h-full">
    <div>
      {{ title }} - {{ name }}
      <!-- <el-button type="primary" @click="toJson">json</el-button> -->
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
    <div id="container" class="flex h-full">
      <div id="stencil" class="w-[200px] h-full relative">1</div>
      <div id="graph-container" class="flex-1 h-full">2</div>
      <div class="w-[400px] p-[10px]">
        <build-attr
          v-if="type === '1'"
          v-model:current-node-data="currentNode"
          :company-id="businessId"
        />
        <div v-else>
          <div
            v-if="
              type === '5' &&
              currentNode &&
              currentNode?.data?.deviceType !== 'background'
            "
            class="mb-[10px]"
          >
            <div>设备属性</div>
            <el-form-item label="设备名称">
              <el-input
                v-model="fromJsonData.name"
                @input="
                  val => {
                    changeData(val, 'name')
                  }
                "
              />
            </el-form-item>
            <el-form-item label="字体颜色">
              <el-color-picker v-model="color" />
            </el-form-item>
            <el-form-item label="字体大小">
              <el-input v-model="textsize" type="number" />
            </el-form-item>
          </div>
          <div
            v-else-if="
              type === '4' &&
              currentNode?.data &&
              currentNode?.data?.deviceType !== 'background'
            "
          >
            <div>设备属性</div>
            <div v-if="crrentOpiton && crrentOpiton.length > 0">
              <el-form ref="formRef" label-width="80px">
                <el-form-item
                  v-for="(item, index) in crrentOpiton"
                  :key="index"
                  :label="item.attrName"
                >
                  <el-input
                    v-model="fromJsonData[item.attrCode]"
                    @input="
                      val => {
                        changeData(val, item.attrCode)
                      }
                    "
                  />
                </el-form-item>
                <el-form-item label="字体颜色">
                  <el-color-picker v-model="color" />
                </el-form-item>
                <el-form-item label="字体大小">
                  <el-input v-model="textsize" type="number" />
                </el-form-item>
              </el-form>
            </div>
          </div>
          <el-empty v-else description="请点击选择设备后设置属性" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.x6-widget-stencil {
  background-color: #fff !important;
}

.x6-widget-stencil-title {
  background-color: #fff;
}

.x6-widget-stencil-group-title {
  background-color: #fff !important;
}

.x6-widget-transform {
  padding: 0;
  margin: -1px 0 0 -1px;
  border: 1px solid #239edd;
}

.x6-widget-transform > div {
  border: 1px solid #239edd;
}

.x6-widget-transform > div:hover {
  background-color: #3dafe4;
}

.x6-widget-transform-active-handle {
  background-color: #3dafe4;
}

.x6-widget-transform-resize {
  border-radius: 0;
}

.x6-widget-selection-inner {
  border: 1px solid #239edd;
}

.x6-widget-selection-box {
  opacity: 0;
}
</style>
