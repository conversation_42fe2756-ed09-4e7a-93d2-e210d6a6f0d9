<script setup lang="ts">
import URLS from '@api/URLS'
import { get } from '@/utils/http/request'
import { onMounted, ref, watch } from 'vue'

const currentNodeData = defineModel('currentNodeData', {
  type: Object as () => any
})
const props = defineProps({
  companyId: {
    type: String,
    required: true
  }
})
const buildList = ref([])
const initBuildList = () => {
  get(URLS.BUILD + '/build', {
    companyId: props.companyId
  }).then(res => {
    console.log('initBuildList', res)
    if (res && res.success) {
      buildList.value = res.data
    }
  })
}
onMounted(() => initBuildList())

const handleBuildChange = e => {
  console.log('handleBuildChange', e, currentNodeData.value)
  currentNodeData.value.data.buildId = e
  currentNodeData.value.prop(
    'attrs/label/text',
    buildList.value.find(item => item.id === e).name
  )
}
const val = ref('')
const color = ref('')
const textsize = ref('')
watch(
  () => currentNodeData?.value,
  (newVal, oldVal) => {
    console.log('currentNodeData changed', newVal, oldVal)
    if (
      newVal &&
      (newVal?.data?.id === 'build' || newVal?.data?.id === 'buildNum')
    ) {
      console.log('currentNodeData changed', newVal)
      val.value = newVal.data.buildId || ''
      color.value = newVal?.attrs?.label?.fill || ''
      textsize.value = newVal?.attrs?.label?.fontSize || ''
    }
  },
  { immediate: true }
)
watch(
  () => color.value,
  (newVal, oldVal) => {
    console.log('color', newVal, oldVal)
    currentNodeData.value.attr('label', {
      fill: newVal
    })
  }
)
watch(
  () => textsize.value,
  (newVal, oldVal) => {
    console.log('textsize', newVal, oldVal)
    currentNodeData.value.attr('label', {
      fontSize: newVal
    })
  }
)
</script>

<template>
  <div>建筑属性</div>
  <div v-if="currentNodeData?.data?.id === 'buildNum'">
    <el-form ref="formRef" label-width="80px">
      <el-form-item label="建筑物">
        <el-select v-model="val" @change="handleBuildChange">
          <el-option
            v-for="item in buildList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="字体颜色">
        <el-color-picker v-model="color" />
      </el-form-item>
      <el-form-item label="字体大小">
        <el-input v-model="textsize" type="number" />
      </el-form-item>
    </el-form>
  </div>
  <el-empty v-else description="请点击选择设备后设置属性" />
</template>
