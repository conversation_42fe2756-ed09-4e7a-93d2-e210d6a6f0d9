<script setup lang="ts">
import { onMounted, watch, ref } from 'vue'
import { get, del } from '@/utils/http/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import TenantSelectModal from '@v/highRiseMananger/tenant/components/TenantSelectModal.vue'
import FloorTenantEditModal from '@v/highRiseMananger/tenant/components/FloorTenantEditModal.vue'

const props = defineProps<{
  floorId: string
  buildId: string
}>()

const tableData = ref([])
const loading = ref(false)
const modalRef = ref()
const editModalRef = ref()

const loadTableData = () => {
  if (!props.floorId) return
  loading.value = true
  get(`/build/build/floor/tenant/list/${props.floorId}`)
    .then(res => {
      loading.value = false
      if (res && res.success) {
        tableData.value = res.data || []
      } else {
        tableData.value = []
      }
    })
    .catch(() => {
      loading.value = false
      tableData.value = []
    })
}

// 打开新增弹窗
const handleAdd = () => {
  modalRef.value?.handleOpen()
}

// 弹窗保存成功回调
const handleModalOk = () => {
  loadTableData()
}

// 编辑功能
const handleEdit = (row: any) => {
  editModalRef.value?.handleEdit(row)
}

// 查看功能
const handleLook = (row: any) => {
  editModalRef.value?.handleLook(row)
}

// 编辑弹窗保存成功回调
const handleEditOk = () => {
  loadTableData()
}

// 删除功能
const handleDelete = async (row: any) => {
  try {
    const res = await del(`/build/build/floor/tenant/${row.id}`)
    if (res && res.success) {
      ElMessage.success('删除成功')
      loadTableData()
    } else {
      ElMessage.error(res.msg || '删除失败')
    }
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

onMounted(loadTableData)
watch(() => props.floorId, loadTableData)
</script>

<template>
  <div class="mb-2">
    <el-button v-auth="'SettlingUnitsAdd'" type="primary" @click="handleAdd">
      新增
    </el-button>
    <el-button type="primary" @click="loadTableData"> 刷新 </el-button>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    border
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
  >
    <el-table-column label="入驻单位名称" prop="tenant.tenantName" />
    <el-table-column label="入驻单位编码" prop="tenant.tenantCode" />
    <el-table-column label="入驻日期" prop="checkInDate" />
    <el-table-column label="操作" width="200">
      <template #default="scope">
        <el-button
          v-auth="'SettlingUnitsEdit'"
          size="small"
          type="text"
          link
          @click="handleLook(scope.row)"
        >
          查看
        </el-button>
        <el-button
          v-auth="'SettlingUnitsEdit'"
          size="small"
          type="primary"
          link
          @click="handleEdit(scope.row)"
        >
          编辑
        </el-button>
        <el-popconfirm
          class="box-item"
          title="确认删除该入驻单位吗"
          placement="top-start"
          @confirm="handleDelete(scope.row)"
        >
          <template #reference>
            <el-button
              v-auth="'SettlingUnitsDel'"
              size="small"
              type="danger"
              link
            >
              删除
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>

  <!-- 入驻单位选择弹窗 -->
  <tenant-select-modal
    ref="modalRef"
    :build-id="buildId"
    :build-floor-id="floorId"
    @ok="handleModalOk"
  />

  <!-- 入驻单位编辑弹窗 -->
  <floor-tenant-edit-modal ref="editModalRef" @ok="handleEditOk" />
</template>
