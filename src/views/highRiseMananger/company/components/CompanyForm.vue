<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { CascaderProps, FormRules } from 'element-plus'
import { get } from '@/utils/http/request'
import FormComposition from '@/composition/FormCompostion'
import amap from '@/components/map/index.vue'
import useAmap from '@/composition/useAmap'
interface Company {
  id?: string
  name?: string
  code?: string
  province?: string
  city?: string
  area?: string
  street?: string
  areas?: string[]
}
const { getDistrictByAdress } = useAmap('')
const emits = defineEmits(['ok'])
const dealRecord = (record: any) => {
  const areas = []
  if (record.province) {
    areas.push(record.province)
  }
  if (record.city) {
    areas.push(record.city)
  }
  if (record.area) {
    areas.push(record.area)
  }
  if (record.street) {
    areas.push(record.street)
  }
  formData.value.areas = areas
}

const rules = reactive<FormRules<Company>>({
  name: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入单位编码', trigger: 'blur' }],
  areas: [{ required: true, message: '请输选择单位地址', trigger: 'blur' }]
})

const dealSubmitParams = async (data: any) => {
  const areas = formData.value.areas
  if (areas) {
    if (areas.length >= 1) {
      data.province = areas[0]
    }
    if (areas.length >= 2) {
      data.city = areas[1]
    }
    if (areas.length >= 3) {
      data.area = areas[2]
    }
    if (areas.length >= 4) {
      data.street = areas[3]
    }
  }
  valueLalel.value =
    canser?.value?.getCheckedNodes()[0]?.pathLabels.join(',') || []
  const res: any = await getDistrictByAdress(valueLalel.value)
  const location = res?.location?.split(',')
  data.lat = location[0] || ''
  data.lng = location[1] || ''
  return data
}

const areaProp: CascaderProps = {
  lazy: true,
  checkStrictly: true,
  lazyLoad(node, resolve) {
    console.log('lazyLoad', node)
    const { level } = node
    const params = {
      pCode: null
    }
    if (level === 0) {
      params.pCode = '-1'
    } else {
      params.pCode = node.value
    }
    get(`/build/city/children/${params.pCode}`).then(res => {
      console.log('获取到数据:', res)
      if (res && res.success) {
        const nodes = res.data.map(item => ({
          value: item.code,
          label: item.name,
          leaf: level === 3
        }))
        resolve(nodes)
      }
    })
  }
}

const {
  visible,
  title,
  formData,
  formRef,
  loading,
  handleAdd,
  handleEdit,
  handleSubmit,
  handleClose
} = FormComposition({
  urls: {
    normal: '/build/company',
    detail: '/build/company',
    rowKey: 'id'
  },
  dealRecord,
  dealSubmitParams,
  emits
})

watch(
  () => formData['files'],
  () => {
    console.log('on formData files changed', formData)
  }
)
const valueLalel = ref()
const handleChange = value => {
  valueLalel.value = canser.value.getCheckedNodes()[0].pathLabels.join(',')
}
const updateArea = value => {
  console.log(value, 'da')
  formData.value.areas = value
}
const handleChoseMap = () => {
  valueLalel.value =
    canser?.value?.getCheckedNodes()[0]?.pathLabels.join(',') || []
  showArea.value = true
}
const canser = ref()
const showArea = ref(false)
defineExpose({ handleAdd, handleEdit })
</script>

<template>
  <el-dialog v-model="visible" :title="title" width="600">
    <el-form
      ref="formRef"
      v-loading="loading"
      :rules="rules"
      :model="formData"
      label-width="auto"
    >
      <el-form-item label="单位名称" prop="name">
        <el-input v-model="formData.name" />
      </el-form-item>
      <el-form-item label="单位编码" prop="code">
        <el-input v-model="formData.code" />
      </el-form-item>
      <el-form-item label="行政区划" prop="areas">
        <el-cascader
          ref="canser"
          v-model="formData.areas"
          :props="areaProp"
          style="width: 85%; margin-right: 10px"
          @change="handleChange"
        />
        <el-button @click="handleChoseMap">选择</el-button>
      </el-form-item>
      <el-form-item label="全局图">
        <g-upload
          v-model:value="formData.fileIds"
          :file-data-list="formData.companyLayoutImage"
          type="companyLayoutImage"
          list-type="picture-card"
          :limit="1"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 提交 </el-button>
      </div>
    </template>
    <el-dialog v-model="showArea" :title="'选择区域'" width="500">
      <div v-if="showArea">
        <amap :valueLalel="valueLalel" @updateArea="updateArea" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="showArea = false"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>
