<script setup lang="ts">
import { reactive } from 'vue'
import { CascaderProps, FormRules } from 'element-plus'
import { get } from '@/utils/http/request'
import FormComposition from '@/composition/FormCompostion'

interface Floor {
  id?: string
  num?: string
  nature?: number
  remark?: string
}

const props = defineProps<{
  buildId: string
}>()

const rules = reactive({
  num: [{ required: true, message: '请输入层号', trigger: 'blur' }],
  floorName: [{ required: true, message: '请输入楼层名称', trigger: 'blur' }]
})

const dealSubmitParams = (data: any) => {
  data['buildId'] = props.buildId
  const fileIds = []
  if (formData.value.floorLayoutImageIds) {
    fileIds.push(...formData.value.floorLayoutImageIds)
  }
  if (formData.value.floorEmergencyEscapeMapIds) {
    fileIds.push(...formData.value.floorEmergencyEscapeMapIds)
  }
  data['fileIds'] = fileIds
}

const emits = defineEmits(['ok'])

const {
  visible,
  title,
  formData,
  formRef,
  loading,
  handleAdd,
  handleEdit,
  handleSubmit,
  handleClose,
  handleLook,
  showBtn
} = FormComposition({
  urls: {
    normal: '/build/build/floor',
    detail: '/build/build/floor',
    rowKey: 'id'
  },
  dealSubmitParams,
  emits
})

defineExpose({ handleAdd, handleEdit, handleLook })
</script>

<template>
  <el-dialog v-model="visible" :title="title" width="500" class="custom-dialog">
    <el-form
      ref="formRef"
      v-loading="loading"
      :rules="rules"
      :model="formData"
      label-width="auto"
    >
      <el-form-item label="楼层名称" prop="floorName">
        <el-input v-model="formData.floorName" />
      </el-form-item>
      <el-form-item label="层号" prop="num">
        <el-input v-model="formData.num" type="number" />
      </el-form-item>
      <el-form-item label="性质" prop="nature">
        <el-input v-model="formData.nature" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" :rows="3" />
      </el-form-item>
      <el-form-item label="平面图" prop="floorLayoutImageIds">
        <g-upload
          v-model:value="formData.floorLayoutImageIds"
          :file-data-list="formData.floorLayoutImage"
          type="floorLayoutImage"
          list-type="picture-card"
          :limit="1"
        />
      </el-form-item>
      <el-form-item label="应急逃生路线图" prop="floorEmergencyEscapeMapIds">
        <g-upload
          v-model:value="formData.floorEmergencyEscapeMapIds"
          :file-data-list="formData.floorEmergencyEscapeMap"
          type="floorEmergencyEscapeMap"
          list-type="picture-card"
          :limit="1"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div v-show="showBtn" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<style lang="scss">
.el-overlay {
  z-index: 2000 !important;
}
</style>
