<template>
  <el-dialog
    v-model="visible"
    title="楼栋审核"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="audit-content">
      <div v-if="buildName" class="build-info">
        <p class="build-name">楼栋名称：{{ buildName }}</p>
      </div>
      <p class="audit-tip">请选择审核结果：</p>
      <el-radio-group v-model="auditStatus" class="audit-options">
        <div
          class="audit-option"
          :class="{ 'is-checked': auditStatus === 'approved' }"
        >
          <el-radio value="approved">
            <el-icon class="audit-icon success"><Check /></el-icon>
            审核通过
          </el-radio>
        </div>
        <div
          class="audit-option"
          :class="{ 'is-checked': auditStatus === 'rejected' }"
        >
          <el-radio value="rejected">
            <el-icon class="audit-icon danger"><Close /></el-icon>
            审核驳回
          </el-radio>
        </div>
      </el-radio-group>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import { post } from '@/utils/http/request'

interface AuditModalProps {
  modelValue: boolean
  buildId?: string
  buildName?: string
}

const props = defineProps<AuditModalProps>()
const emits = defineEmits(['update:modelValue', 'ok'])

const visible = ref(false)
const loading = ref(false)
const auditStatus = ref('approved')

watch(
  () => props.modelValue,
  newVal => {
    visible.value = newVal
    if (newVal) {
      auditStatus.value = 'approved'
    }
  }
)

watch(visible, newVal => {
  emits('update:modelValue', newVal)
})

const handleClose = () => {
  visible.value = false
}

const handleConfirm = async () => {
  if (!props.buildId) {
    ElMessage.error('楼栋ID不能为空')
    return
  }

  loading.value = true
  try {
    const response = await post(`/build/build/${props.buildId}/audit`, {
      status: auditStatus.value
    })

    if (response.success) {
      ElMessage.success('审核操作成功')
      visible.value = false
      emits('ok')
    } else {
      ElMessage.error(response.msg || '审核操作失败')
    }
  } catch (error) {
    console.error('审核操作失败:', error)
    ElMessage.error('审核操作失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.audit-content {
  padding: 20px 0;
}

.build-info {
  padding: 12px 16px;
  margin-bottom: 20px;
  background-color: #f5f7fa;
  border-left: 4px solid #409eff;
  border-radius: 6px;
}

.build-name {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.audit-tip {
  margin-bottom: 20px;
  font-size: 14px;
  color: #606266;
}

.audit-options {
  display: flex;
  flex-direction: row;
  gap: 16px;
  justify-content: space-between;
}

.audit-option {
  display: flex;
  flex: 1;
  align-items: center;
  min-height: 60px;
  padding: 16px 20px;
  cursor: pointer;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  transition: all 0.3s;
}

.audit-option:hover {
  background-color: #f5f7fa;
  border-color: #409eff;
  box-shadow: 0 4px 12px rgb(64 158 255 / 10%);
  transform: translateY(-2px);
}

.audit-option.is-checked {
  background-color: #ecf5ff;
  border-color: #409eff;
  box-shadow: 0 4px 12px rgb(64 158 255 / 20%);
}

.audit-icon {
  margin-right: 8px;
  font-size: 18px;
}

.audit-icon.success {
  color: #67c23a;
}

.audit-icon.danger {
  color: #f56c6c;
}

:deep(.el-radio) {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  margin-right: 0;
}

:deep(.el-radio__label) {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-radio__input) {
  margin-right: 8px;
}
</style>
