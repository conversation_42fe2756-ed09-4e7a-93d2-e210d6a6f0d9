<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { CascaderProps, ElMessage, FormRules } from 'element-plus'
import { get } from '@/utils/http/request'
import FormComposition from '@/composition/FormCompostion'
import DictSelector from '@c/Dict/dict'

interface Build {
  id?: string
  name?: string
  height?: number
  address?: string
  floor?: number
  buildArea?: number
  type?: string
  isUse?: boolean
  useDate?: string
}

const props = defineProps<{
  companyId: string
}>()

const rules = reactive<FormRules<any>>({
  name: [{ required: true, message: '请输入建筑名称', trigger: 'blur' }],
  height: [{ required: true, message: '请输入建筑高度', trigger: 'blur' }],
  address: [{ required: true, message: '请输入建筑地址', trigger: 'blur' }],
  floor: [{ required: true, message: '请输入建筑层数', trigger: 'blur' }],
  buildArea: [{ required: true, message: '请输入建筑面积', trigger: 'blur' }],
  type: [{ required: true, message: '请输入建筑类型', trigger: 'blur' }],
  isUse: [{ required: true, message: '请输入是否投入使用', trigger: 'blur' }],
  useDate: [
    { required: false, message: '请输入投入使用日期', trigger: 'blur' }
  ],
  fireGeneralLayoutPlanImageIds: [
    { required: true, message: '请上传消防总平面布置图', trigger: 'blur' }
  ],
  fireFacilityPointPlanImageIds: [
    { required: true, message: '请上传消防设施点位平台图', trigger: 'blur' }
  ],
  exteriorWallMaterialImageIds: [
    { required: false, message: '请上传外墙材料信息上传', trigger: 'blur' }
  ],
  standardFloorFirePlanImageIds: [
    { required: true, message: '请上传标准层或首层消防平台图', trigger: 'blur' }
  ],
  emergencyEscapePlanImageIds: [
    { required: true, message: '请上传应急逃生图', trigger: 'blur' }
  ],
  gasPipelineImageIds: [
    { required: true, message: '请上传燃气管道图', trigger: 'blur' }
  ],
  'buildPerson.managerUnit': [
    { required: true, message: '请输入建筑管理单位', trigger: 'blur' }
  ],
  'buildPerson.managerUnitUser': [
    { required: true, message: '请输入建筑管理人', trigger: 'blur' }
  ],
  'buildPerson.managerUnitContact': [
    { required: true, message: '请输入联系方式', trigger: 'blur' }
  ],
  'buildPerson.safetyManagerUser': [
    { required: true, message: '请输入安全管理人', trigger: 'blur' }
  ],
  'buildPerson.safetyManagerContact': [
    { required: true, message: '请输入联系方式', trigger: 'blur' }
  ],
  'buildPerson.safetyManagerPosition': [
    { required: false, message: '请输入职务', trigger: 'blur' }
  ],
  'buildPerson.safetyDutyUser': [
    { required: true, message: '安全负责人', trigger: 'blur' }
  ],
  'buildPerson.safetyDutyContact': [
    { required: true, message: '请输入建筑联系方式', trigger: 'blur' }
  ],
  'buildPerson.safetyDutyPosition': [
    { required: false, message: '请输入职务', trigger: 'blur' }
  ],
  'buildPerson.buildLeader': [
    { required: true, message: '请输入楼长', trigger: 'blur' }
  ],
  'buildPerson.buildLeaderContact': [
    { required: true, message: '请输入联系方式', trigger: 'blur' }
  ],
  'buildOther.maintenanceName': [
    { required: true, message: '请输入维保服务机构', trigger: 'blur' }
  ],
  'buildOther.maintenanceManager': [
    { required: false, message: '请输入单位管理人', trigger: 'blur' }
  ],
  'buildOther.maintenanceContact': [
    { required: true, message: '请输入联系方式', trigger: 'blur' }
  ],
  'buildOther.fireControlRoom': [
    { required: true, message: '请输入是否有消控室', trigger: 'blur' }
  ],
  'buildOther.fireControlRoomDutyNum': [
    { required: false, message: '请输入消控室值班人数/每班', trigger: 'blur' }
  ],
  'buildOther.fireDoorNum': [
    { required: false, message: '请输入建筑常闭防火门数', trigger: 'blur' }
  ],
  'buildOther.fireDoorTagNum': [
    { required: false, message: '请输入已标识化防火门数', trigger: 'blur' }
  ],
  'buildOther.fireDoorCloseNum': [
    { required: false, message: '请输入已落实关闭的防火门数', trigger: 'blur' }
  ]
})

const dealSubmitParams = (data: any) => {
  data['companyId'] = props.companyId
  const fileIds = []
  if (formData.value.fireGeneralLayoutPlanImageIds) {
    fileIds.push(...formData.value.fireGeneralLayoutPlanImageIds)
  }
  if (formData.value.fireFacilityPointPlanImageIds) {
    fileIds.push(...formData.value.fireFacilityPointPlanImageIds)
  }
  if (formData.value.exteriorWallMaterialImageIds) {
    fileIds.push(...formData.value.exteriorWallMaterialImageIds)
  }
  if (formData.value.standardFloorFirePlanImageIds) {
    fileIds.push(...formData.value.standardFloorFirePlanImageIds)
  }
  if (formData.value.emergencyEscapePlanImageIds) {
    fileIds.push(...formData.value.emergencyEscapePlanImageIds)
  }
  if (formData.value.gasPipelineImageIds) {
    fileIds.push(...formData.value.gasPipelineImageIds)
  }
  data['fileIds'] = fileIds

  const deviceList = []
  fireDeviceTypeList.value.forEach(item => {
    console.log(
      'formData.value.deviceStatisticsObj',
      item.code,
      formData.value.deviceStatisticsObj[item.code]
    )
    if (formData.value.deviceStatisticsObj[item.code].checked) {
      deviceList.push({
        type: item.code,
        num: formData.value.deviceStatisticsObj[item.code].num,
        remark: formData.value.deviceStatisticsObj[item.code].remark
      })
    }
  })
  data['deviceStatisticsList'] = deviceList
}

const emits = defineEmits(['ok'])

const fireDeviceTypeList = ref([])
const deviceStatisticsObj = {}
const initFireDeviceTypeList = () => {
  get('/build/dict/p/001000').then(res => {
    if (res && res.success) {
      fireDeviceTypeList.value = res.data
      // const deviceStatisticsObj = {}
      res.data.forEach(item => {
        deviceStatisticsObj[item.code] = {
          checked: false,
          num: null,
          remark: null
        }
      })
      formData.value.deviceStatisticsObj = deviceStatisticsObj
    } else {
      ElMessage({
        message: res.msg,
        type: 'error'
      })
    }
  })
}

onMounted(() => {
  initFireDeviceTypeList()
})

const addInit = () => {
  formData.value = {
    isUse: false,
    buildPerson: {},
    buildOther: {
      fireControlRoom: false
    },
    deviceStatisticsObj: { ...deviceStatisticsObj },
    fireGeneralLayoutPlanImage: [],
    fireGeneralLayoutPlanImageIds: [],
    fireFacilityPointPlanImage: [],
    fireFacilityPointPlanImageIds: [],
    exteriorWallMaterialImage: [],
    exteriorWallMaterialImageIds: [],
    standardFloorFirePlanImage: [],
    standardFloorFirePlanImageIds: [],
    emergencyEscapePlanImage: [],
    emergencyEscapePlanImageIds: [],
    gasPipelineImage: [],
    gasPipelineImageIds: []
  }
}

const dealRecord = record => {
  if (!record.buildPerson) {
    formData.value.buildPerson = {}
  }
  if (!record.buildOther) {
    formData.value.buildOther = {
      fireControlRoom: false
    }
  }
  if (record.deviceStatisticsList && record.deviceStatisticsList.length > 0) {
    const deviceStatisticsObjTemp = { ...deviceStatisticsObj }
    record.deviceStatisticsList.forEach(item => {
      deviceStatisticsObjTemp[item.type] = {
        checked: true,
        num: item.num,
        remark: item.remark
      }
    })
    formData.value.deviceStatisticsObj = deviceStatisticsObjTemp
  } else {
    formData.value.deviceStatisticsObj = { ...deviceStatisticsObj }
  }
}

const {
  visible,
  title,
  formData,
  formRef,
  loading,
  handleAdd,
  handleEdit,
  handleSubmit,
  handleClose
} = FormComposition({
  urls: {
    normal: '/build/build',
    detail: '/build/build',
    rowKey: 'id'
  },
  dealSubmitParams,
  emits,
  addInit,
  dealRecord
})

defineExpose({ handleAdd, handleEdit })
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="1000"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :fullscreen="true"
  >
    <el-form
      ref="formRef"
      v-loading="loading"
      :rules="rules"
      :model="formData"
      label-width="auto"
    >
      <el-row :gutter="20">
        <el-divider content-position="left">基本信息</el-divider>
        <el-col :span="12">
          <el-form-item label="建筑名称" prop="name">
            <el-input v-model="formData.name" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建筑高度" prop="height">
            <el-input-number v-model="formData.height" :precision="2" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建筑地址" prop="address">
            <el-input v-model="formData.address" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建筑层数" prop="floor">
            <el-input-number v-model="formData.floor" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建筑面积" prop="buildArea">
            <el-input-number v-model="formData.buildArea" :precision="2" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建筑类型" prop="type">
            <dict-selector v-model="formData.type" parentKey="002000" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否投入使用" prop="isUse">
            <el-switch
              v-model="formData.isUse"
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="投入使用日期" prop="useDate">
            <el-date-picker
              v-model="formData.useDate"
              type="date"
              placeholder="请选择投入使用日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-divider content-position="left">建筑图片</el-divider>
        <el-col :span="12">
          <el-form-item
            label="消防总平面布置图"
            prop="fireGeneralLayoutPlanImageIds"
          >
            <g-upload
              v-model:value="formData.fireGeneralLayoutPlanImageIds"
              :file-data-list="formData.fireGeneralLayoutPlanImage"
              type="fireGeneralLayoutPlanImage"
              list-type="picture-card"
              :limit="1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="消防设施点位平台图"
            prop="fireFacilityPointPlanImageIds"
          >
            <g-upload
              v-model:value="formData.fireFacilityPointPlanImageIds"
              :file-data-list="formData.fireFacilityPointPlanImage"
              type="fireFacilityPointPlanImage"
              list-type="picture-card"
              :limit="1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="外墙材料信息上传"
            prop="exteriorWallMaterialImageIds"
          >
            <g-upload
              v-model:value="formData.exteriorWallMaterialImageIds"
              :file-data-list="formData.exteriorWallMaterialImage"
              type="exteriorWallMaterialImage"
              list-type="picture-card"
              :limit="1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="标准层或首层消防平台图"
            prop="standardFloorFirePlanImageIds"
          >
            <g-upload
              v-model:value="formData.standardFloorFirePlanImageIds"
              :file-data-list="formData.standardFloorFirePlanImage"
              type="standardFloorFirePlanImage"
              list-type="picture-card"
              :limit="1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="应急逃生图" prop="emergencyEscapePlanImageIds">
            <g-upload
              v-model:value="formData.emergencyEscapePlanImageIds"
              :file-data-list="formData.emergencyEscapePlanImage"
              type="emergencyEscapePlanImage"
              list-type="picture-card"
              :limit="1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="燃气管道图" prop="gasPipelineImageIds">
            <g-upload
              v-model:value="formData.gasPipelineImageIds"
              :file-data-list="formData.gasPipelineImage"
              type="gasPipelineImage"
              list-type="picture-card"
              :limit="1"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-divider content-position="left">
          高层建筑管理单位基本信息
        </el-divider>
        <el-col :span="8">
          <el-form-item label="建筑管理单位" prop="buildPerson.managerUnit">
            <el-input v-model="formData.buildPerson.managerUnit" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="建筑管理人" prop="buildPerson.managerUnitUser">
            <el-input v-model="formData.buildPerson.managerUnitUser" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="管理电话" prop="buildPerson.managerUnitContact">
            <el-input v-model="formData.buildPerson.managerUnitContact" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="安全管理人" prop="buildPerson.safetyManagerUser">
            <el-input v-model="formData.buildPerson.safetyManagerUser" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="联系方式"
            prop="buildPerson.safetyManagerContact"
          >
            <el-input v-model="formData.buildPerson.safetyManagerContact" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="职务" prop="buildPerson.safetyManagerPosition">
            <el-input v-model="formData.buildPerson.safetyManagerPosition" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="安全负责人" prop="buildPerson.safetyDutyUser">
            <el-input v-model="formData.buildPerson.safetyDutyUser" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系方式" prop="buildPerson.safetyDutyContact">
            <el-input v-model="formData.buildPerson.safetyDutyContact" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="职务" prop="buildPerson.safetyDutyPosition">
            <el-input v-model="formData.buildPerson.safetyDutyPosition" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="楼长" prop="buildPerson.buildLeader">
            <el-input v-model="formData.buildPerson.buildLeader" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系方式" prop="buildPerson.buildLeaderContact">
            <el-input v-model="formData.buildPerson.buildLeaderContact" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-divider content-position="left">维保服务机构</el-divider>
        <el-col :span="8">
          <el-form-item label="维保服务单位" prop="buildOther.maintenanceName">
            <el-input v-model="formData.buildOther.maintenanceName" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单位管理人" prop="buildOther.maintenanceManager">
            <el-input v-model="formData.buildOther.maintenanceManager" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系方式" prop="buildOther.maintenanceContact">
            <el-input v-model="formData.buildOther.maintenanceContact" />
          </el-form-item>
        </el-col>
        <el-form-item
          label="消防设施维保记录"
          prop="fireFacilityMaintenanceRecordImage"
        >
          <g-upload
            v-model:value="formData.fireFacilityMaintenanceRecordImageIds"
            :file-data-list="formData.fireFacilityMaintenanceRecordImage"
            type="fireFacilityMaintenanceRecordImage"
            :limit="1"
          />
        </el-form-item>
      </el-row>
      <el-row v-if="formData.deviceStatisticsObj" :gutter="20">
        <el-divider content-position="left">消防设施情况统计</el-divider>
        <template
          v-for="type in fireDeviceTypeList"
          :key="type.code + '_checkbox'"
        >
          <el-col :span="1">
            <el-checkbox
              v-model="formData.deviceStatisticsObj[type.code].checked"
              :value="type.code"
            >
              {{ type.value }}
            </el-checkbox>
          </el-col>
          <el-col :span="4">
            <el-form-item label="数量">
              <el-input-number
                v-model="formData.deviceStatisticsObj[type.code].num"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7" style="padding-right: 100px">
            <el-form-item label="设备现状">
              <el-input
                v-model="formData.deviceStatisticsObj[type.code].remark"
              />
            </el-form-item>
          </el-col>
        </template>
      </el-row>
      <el-row :gutter="20">
        <el-divider content-position="left">消防控制室</el-divider>
        <el-col :span="8">
          <el-form-item label="是否有消控室" prop="buildOther.fireControlRoom">
            <el-switch
              v-model="formData.buildOther.fireControlRoom"
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="消控室值班人数/每班"
            prop="buildOther.fireControlRoomDutyNum"
          >
            <el-input v-model="formData.buildOther.fireControlRoomDutyNum" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-divider content-position="left">防火门</el-divider>
        <el-col :span="8">
          <el-form-item label="建筑常闭防火门数" prop="buildOther.fireDoorNum">
            <el-input v-model="formData.buildOther.fireDoorNum" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="已标识化防火门数"
            prop="buildOther.fireDoorTagNum"
          >
            <el-input v-model="formData.buildOther.fireDoorTagNum" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="已落实关闭的防火门数"
            prop="buildOther.fireDoorCloseNum"
          >
            <el-input v-model="formData.buildOther.fireDoorCloseNum" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
