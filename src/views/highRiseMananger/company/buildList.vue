<script setup lang="ts">
import {
  ElButton,
  ElTable,
  ElTableColumn,
  ElDialog,
  ElMessage
} from 'element-plus'
import TableComposition from '@/composition/TableComposition'
import { onMounted, watch, ref } from 'vue'
import BuildForm from '@v/highRiseMananger/company/components/BuildForm.vue'
import AuditModal from '@v/highRiseMananger/company/components/AuditModal.vue'
import DictSelector from '@c/Dict/dict'
import { get } from '@/utils/http/request'
import URLS from '@/api/URLS'
const emits = defineEmits(['change-tab'])
const toFloor = build => {
  emits('change-tab', 1, build.id, build.name)
}

const props = defineProps({
  companyId: String
})

// 状态统计相关
const statusCount = ref<{
  allCount: number
  pending: number
  approved: number
  rejected: number
}>({ allCount: 0, pending: 0, approved: 0, rejected: 0 })
const statusTabs = [
  { key: 'all', label: '全部' },
  { key: 'pending', label: '待审核' },
  { key: 'approved', label: '通过' },
  { key: 'rejected', label: '驳回' }
]
const currentStatus = ref('all')

const fetchStatusCount = async () => {
  const res = await get('/build/build/statistics/status-count', {
    companyId: props.companyId
  })
  if (res && res.success) {
    statusCount.value = res.data || {
      allCount: 0,
      pending: 0,
      approved: 0,
      rejected: 0
    }
  }
}

const handleStatusTab = status => {
  currentStatus.value = status
  loadTableData(1)
}

// 审核弹框相关
const auditModalVisible = ref(false)
const currentBuild = ref(null)

const handleAudit = build => {
  currentBuild.value = build
  auditModalVisible.value = true
}

const handleAuditSuccess = () => {
  fetchStatusCount()
  loadTableData(1)
}

watch(
  () => props.companyId,
  () => {
    defaultQueryParams['companyId'] = props.companyId
    fetchStatusCount()
    loadTableData(1)
  }
)

onMounted(() => {
  defaultQueryParams['companyId'] = props.companyId
  fetchStatusCount()
  loadTableData(1)
})

let {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  defaultQueryParams,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit
} = TableComposition({
  urls: {
    list: '/build/build',
    normal: '/build/build',
    rowKey: 'id'
  }
})

// 查询方法，处理时间范围映射
const handleQuery = () => {
  if (queryParams.value.dateRange && queryParams.value.dateRange.length === 2) {
    queryParams.value.startUseDate = queryParams.value.dateRange[0]
    queryParams.value.endUseDate = queryParams.value.dateRange[1]
  } else {
    queryParams.value.startUseDate = ''
    queryParams.value.endUseDate = ''
  }
  loadTableData(1)
}

// 修改分页查询参数，增加status
const originLoadTableData = loadTableData
const loadTableDataWithStatus = (pageNum = 1) => {
  if (currentStatus.value === 'all') {
    delete queryParams.value.status
  } else {
    queryParams.value.status = currentStatus.value
  }
  originLoadTableData(pageNum)
}

const qrcodeDialogVisible = ref(false)
const qrcodeUrl = ref('')

const showCode = build => {
  console.log(build, 'showCode')
  if (build.buildQrcode && build.buildQrcode[0] && build.buildQrcode[0].path) {
    buildName.value = build.name
    qrcodeUrl.value = URLS.imgFileUrl(build.buildQrcode[0].id)
    qrcodeDialogVisible.value = true
  } else {
    qrcodeUrl.value = ''
    qrcodeDialogVisible.value = false
    ElMessage.warning('未找到二维码图片')
  }
}
const buildName = ref('')
// 替换原有loadTableData
loadTableData = loadTableDataWithStatus
</script>

<template>
  <div class="mb-3">
    <el-form :inline="true" label-width="auto">
      <el-form-item label="建筑名称/地址">
        <el-input
          v-model="queryParams.nameOrAddress"
          placeholder="请输入建筑名称或地址"
        />
      </el-form-item>
      <el-form-item label="建筑类型" style="min-width: 220px">
        <dict-selector
          v-model="queryParams.types"
          parentKey="002000"
          multiple
          placeholder="请选择建筑类型"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="是否投入使用" style="min-width: 180px">
        <el-select
          v-model="queryParams.isUse"
          placeholder="请选择"
          style="width: 120px"
        >
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item label="投入使用日期">
        <el-date-picker
          v-model="queryParams.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetTable">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
  <div class="mb-2 flex items-center">
    <div class="status-tabs flex items-center mr-4">
      <template v-for="tab in statusTabs" :key="tab.key">
        <el-button
          :type="currentStatus === tab.key ? 'primary' : 'default'"
          :plain="currentStatus !== tab.key"
          size="default"
          class="status-btn"
          @click="handleStatusTab(tab.key)"
        >
          {{ tab.label }}：{{
            tab.key === 'all'
              ? statusCount.allCount || 0
              : statusCount[tab.key] || 0
          }}
        </el-button>
      </template>
    </div>
    <el-button v-auth="'buildAdd'" type="primary" @click="handleAdd">
      新增
    </el-button>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
  >
    <el-table-column label="建筑名称" prop="name" />
    <el-table-column label="建筑地址" prop="address" />
    <el-table-column label="建筑类型" prop="translation.typeStr" />
    <el-table-column label="是否投入使用" prop="isUse">
      <template #default="scope">
        {{ scope.row.isUse ? '是' : '否' }}
      </template>
    </el-table-column>
    <el-table-column label="楼长" prop="buildLeader" />
    <el-table-column label="联系方式" prop="buildLeaderContact" />
    <el-table-column label="状态" prop="status">
      <template #default="scope">
        {{
          scope.row.status === 'pending'
            ? '待审核'
            : scope.row.status === 'approved'
              ? '已通过'
              : scope.row.status === 'rejected'
                ? '已拒绝'
                : '-'
        }}
      </template>
    </el-table-column>
    <el-table-column label="提交时间" prop="createTime" />
    <el-table-column label="操作" width="200">
      <template #default="scope">
        <el-button
          size="small"
          type="success"
          link
          @click="showCode(scope.row)"
        >
          查看二维码
        </el-button>
        <el-button size="small" type="success" link @click="toFloor(scope.row)">
          楼层
        </el-button>
        <el-button
          v-auth="'buildEdit'"
          size="small"
          type="primary"
          link
          @click="handleEdit(scope.row)"
        >
          编辑
        </el-button>
        <!-- 当状态为待审核时显示审核按钮 -->
        <el-button
          v-if="scope.row.status === 'pending'"
          v-auth="'buildExamine'"
          size="small"
          type="warning"
          link
          @click="handleAudit(scope.row)"
        >
          审核
        </el-button>
        <el-popconfirm
          class="box-item"
          title="确认删除该资源吗"
          placement="top-start"
          @confirm="handleDelete(scope.row)"
        >
          <template #reference>
            <el-button v-auth="'buildDel'" size="small" type="danger" link>
              删除
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>
  <build-form ref="formRef" :company-id="companyId" @ok="loadTableData(1)" />

  <!-- 审核弹框 -->
  <audit-modal
    v-model="auditModalVisible"
    :build-id="currentBuild?.id"
    :build-name="currentBuild?.name"
    @ok="handleAuditSuccess"
  />

  <!-- 二维码弹窗 -->
  <el-dialog
    v-model="qrcodeDialogVisible"
    title="建筑二维码"
    width="350px"
    :close-on-click-modal="false"
  >
    <div v-if="qrcodeUrl" class="text-center">
      <img
        :src="qrcodeUrl"
        alt="二维码"
        style="display: block; width: 250px; margin: 0 auto"
      />
      <span>{{ buildName }}</span>
    </div>
    <div v-else class="text-center text-gray-400">暂无二维码</div>
  </el-dialog>
</template>

<style scoped>
.status-tabs {
  display: flex;
  gap: 12px;
}

.status-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 110px;
  padding: 0 18px;
  font-size: 15px;
  font-weight: 500;
}
</style>
