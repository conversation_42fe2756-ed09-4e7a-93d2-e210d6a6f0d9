<script setup lang="ts">
import { ElButton, ElTable, ElTableColumn } from 'element-plus'
import { onMounted } from 'vue'
import CompanyForm from '@v/highRiseMananger/company/components/CompanyForm.vue'
import TableComposition from '@/composition/TableComposition'
import { useRouter } from 'vue-router'

const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit
} = TableComposition({
  urls: {
    list: '/build/company/page',
    normal: '/build/company',
    rowKey: 'id'
  }
})

const emits = defineEmits(['change-tab'])
const toBuild = company => {
  console.log('toBuild', company)
  emits('change-tab', 0, company.id, company.name)
}

onMounted(() => {
  loadTableData(1)
})

const router = useRouter()
const jumpToEditor = (row: any) => {
  const route = router.resolve({
    path: '/editor/deviceEditor',
    query: {
      type: '1',
      businessId: row.id,
      name: row.name
    }
  })
  window.open(route.href, '_blank')
}
</script>

<template>
  <div class="mb-3">
    <el-form :inline="true" label-width="auto">
      <el-form-item label="公司名称">
        <el-input v-model="queryParams.name" placeholder="请输入公司名称" />
      </el-form-item>
      <el-form-item label="公司编码">
        <el-input v-model="queryParams.code" placeholder="请输入公司编码" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="loadTableData(1)"> 查询 </el-button>
        <el-button type="primary" @click="resetTable"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </div>
  <div class="mb-2">
    <el-button v-auth="'companyAdd'" type="primary" @click="handleAdd">
      新增
    </el-button>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
  >
    <el-table-column label="单位名称" prop="name" />
    <el-table-column label="单位代码" prop="code" />
    <el-table-column label="省" prop="translation.provinceStr" />
    <el-table-column label="市" prop="translation.cityStr" />
    <el-table-column label="区" prop="translation.areaStr" />
    <el-table-column label="街道" prop="translation.streetStr" />
    <el-table-column label="创建时间" prop="createTime" />
    <el-table-column label="创建人" prop="translation.createUserStr" />
    <el-table-column label="操作" width="200">
      <template #default="scope">
        <el-button size="small" type="success" link @click="toBuild(scope.row)">
          楼栋
        </el-button>
        <el-button
          v-auth="'companyAnnotation'"
          size="small"
          type="success"
          link
          @click="jumpToEditor(scope.row)"
        >
          标记
        </el-button>
        <el-button
          v-auth="'companyEdit'"
          size="small"
          type="primary"
          link
          @click="handleEdit(scope.row)"
        >
          编辑
        </el-button>
        <el-popconfirm
          class="box-item"
          title="确认删除该资源吗"
          placement="top-start"
          @confirm="handleDelete(scope.row)"
        >
          <template #reference>
            <el-button v-auth="'companyDel'" size="small" type="danger" link>
              删除
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>
  <company-form ref="formRef" @ok="loadTableData(1)" />
</template>
