<script setup lang="ts">
import { ElButton, ElTable, ElTableColumn } from 'element-plus'
import TableComposition from '@/composition/TableComposition'
import { onMounted, watch } from 'vue'
import FloorForm from '@v/highRiseMananger/company/components/FloorForm.vue'
import { ArrowDown } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const emits = defineEmits(['change-tab'])

const props = defineProps({
  buildId: String
})

const router = useRouter()

const jumpToEditor = (type: number, data: any) => {
  const route = router.resolve({
    path: '/editor/deviceEditor',
    query: {
      type,
      businessId: data.id,
      name: data.num
    }
  })
  window.open(route.href, '_blank')
}

watch(
  () => props.buildId,
  () => {
    defaultQueryParams['buildId'] = props.buildId
    loadTableData(1)
  }
)

onMounted(() => {
  defaultQueryParams['buildId'] = props.buildId
  loadTableData(1)
})

const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  defaultQueryParams,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit,
  handleLook
} = TableComposition({
  urls: {
    list: '/build/build/floor/page',
    normal: '/build/build/floor',
    rowKey: 'id'
  }
})
</script>

<template>
  <div class="mb-3">
    <el-form :inline="true" label-width="auto">
      <el-form-item label="层号">
        <el-input v-model="queryParams.num" placeholder="请输入层号" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="loadTableData(1)"> 查询 </el-button>
        <el-button type="primary" @click="resetTable"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </div>
  <div class="mb-2">
    <el-button v-auth="'floorAdd'" type="primary" @click="handleAdd">
      新增
    </el-button>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
  >
    <el-table-column label="楼层名称" prop="floorName" />
    <el-table-column label="层号" prop="num" />
    <el-table-column label="性质" prop="nature" />
    <el-table-column label="备注" prop="remark" />
    <el-table-column label="操作" width="250">
      <template #default="scope">
        <div class="flex items-center h-full">
          <el-button
            size="small"
            type="success"
            link
            @click="emits('change-tab', 2, scope.row.id, scope.row.num)"
          >
            入驻单位
          </el-button>
          <el-button
            v-auth="'floorEdit'"
            size="small"
            type="text"
            link
            @click="handleLook(scope.row)"
          >
            查看
          </el-button>
          <el-button
            v-auth="'floorEdit'"
            size="small"
            type="primary"
            link
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-popconfirm
            class="box-item"
            title="确认删除该资源吗"
            placement="top-start"
            @confirm="handleDelete(scope.row)"
          >
            <template #reference>
              <el-button v-auth="'floorDel'" size="small" type="danger" link>
                删除
              </el-button>
            </template>
          </el-popconfirm>
          <el-dropdown>
            <el-button
              v-auth="'floorEstablishment'"
              size="small"
              type="success"
              link
            >
              编制
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="jumpToEditor(4, scope.row)">
                  物联网设备
                </el-dropdown-item>
                <el-dropdown-item @click="jumpToEditor(5, scope.row)">
                  建筑消防设施
                </el-dropdown-item>
                <!--                <el-dropdown-item @click="jumpToEditor(6, scope.row.id)">
                  应急逃生路线
                </el-dropdown-item>-->
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>
  <floor-form ref="formRef" :build-id="buildId" @ok="loadTableData(1)" />
</template>
