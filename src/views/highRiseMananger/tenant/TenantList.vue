<script setup lang="ts">
import { ElButton, ElTable, ElTableColumn } from 'element-plus'
import { onMounted } from 'vue'
import TenantForm from '@v/highRiseMananger/tenant/components/TenantForm.vue'
import TableComposition from '@/composition/TableComposition'

const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit
} = TableComposition({
  urls: {
    list: '/build/tenant/page',
    normal: '/build/tenant',
    rowKey: 'id'
  }
})

onMounted(() => {
  loadTableData(1)
})
</script>

<template>
  <div>
    <div class="mb-3">
      <el-form :inline="true" label-width="auto">
        <el-form-item label="单位名称">
          <el-input
            v-model="queryParams.tenantName"
            placeholder="请输入单位名称"
          />
        </el-form-item>
        <el-form-item label="单位编码">
          <el-input
            v-model="queryParams.tenantCode"
            placeholder="请输入单位编码"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadTableData(1)"> 查询 </el-button>
          <el-button type="primary" @click="resetTable"> 重置 </el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- <div class="mb-2">
      <el-button type="primary" @click="handleAdd"> 新增 </el-button>
    </div> -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      row-key="id"
      style="width: 100%"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center' }"
    >
      <el-table-column label="单位名称" prop="tenantName" />
      <el-table-column label="单位代码" prop="tenantCode" />
      <el-table-column label="单位类型" prop="translation.tenantTypeStr" />
      <el-table-column label="安全管理员" prop="safetyManagerUser" />
      <el-table-column label="安全管理员联系方式" prop="safetyManagerContact" />
      <el-table-column label="法人" prop="legalPerson" />
      <el-table-column label="法人联系方式" prop="legalPersonContact" />
      <el-table-column label="应急联系方式" prop="emergencyContact" />
      <el-table-column label="创建时间 " prop="createTime" />
      <el-table-column label="创建人" prop="translation.createUserStr" />
      <el-table-column label="更新时间" prop="updateTime" />
      <el-table-column label="更新人" prop="translation.updateUserStr" />
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button
            v-auth="'tenantEdit'"
            size="small"
            type="primary"
            link
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-popconfirm
            class="box-item"
            title="确认删除该资源吗"
            placement="top-start"
            @confirm="handleDelete(scope.row)"
          >
            <template #reference>
              <el-button v-auth="'tenantDel'" size="small" type="danger" link>
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <div class="flex justify-end mt-2">
      <el-pagination
        v-model:current-page="pageInfo.pageNum"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="pageSizeList"
        :page-size="pageInfo.pageSize"
        :total="pageInfo.totalRecords"
        @size-change="handlePaginationSizeChange"
        @current-change="handlePaginationPageChange"
      />
    </div>
    <tenant-form ref="formRef" @ok="loadTableData(1)" />
  </div>
</template>
