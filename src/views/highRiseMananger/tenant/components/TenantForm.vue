<script setup lang="ts">
import { reactive, watch, ref, inject } from 'vue'
import { FormRules } from 'element-plus'
import FormComposition from '@/composition/FormCompostion'
import DictSelector from '@c/Dict/dict'
import { constantMenus } from '@/router'

interface tenant {
  id?: string
  tenantName?: string
  tenantCode?: string
  safetyManagerUser?: string
  safetyManagerContact?: string
  legalPerson?: string
  legalPersonContact?: string
  emergencyContact?: string
  tenantType?: string
}

const currentCompanyId: any = inject('currentCompanyId')
const emits = defineEmits(['ok'])

const rules = reactive<FormRules<tenant>>({
  tenantName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
  safetyManagerUser: [
    { required: true, message: '请输入消防安全管理人', trigger: 'blur' }
  ],
  safetyManagerContact: [
    { required: true, message: '请输入消防安全管理人联系方式', trigger: 'blur' }
  ],
  legalPerson: [{ required: true, message: '请输入法人', trigger: 'blur' }],
  legalPersonContact: [
    { required: true, message: '请输入法人联系方式', trigger: 'blur' }
  ]
})

const dealSubmitParams = (data: any) => {
  const fileIds = []
  if (formData.value.selfManagedFireFacilityListIds) {
    fileIds.push(...formData.value.selfManagedFireFacilityListIds)
  }
  if (formData.value.decorationApprovalFilesIds) {
    fileIds.push(...formData.value.decorationApprovalFilesIds)
  }

  data['fileIds'] = fileIds
  data.companyId = data.companyId || currentCompanyId.value
}

const {
  visible,
  title,
  formData,
  formRef,
  loading,
  handleAdd,
  handleEdit,
  handleSubmit,
  handleClose
} = FormComposition({
  urls: {
    normal: '/build/tenant',
    detail: '/build/tenant',
    rowKey: 'id'
  },
  dealSubmitParams,
  emits
})

watch(
  () => formData['files'],
  () => {
    console.log('on formData files changed', formData)
  }
)

const dictKey = ref(0)

// 包装handleAdd，递增dictKey
const _handleAdd = handleAdd
const handleAddWithDictRefresh = (...args: any[]) => {
  dictKey.value++
  _handleAdd.apply(null, args)
}

defineExpose({ handleAdd: handleAddWithDictRefresh, handleEdit })
</script>

<template>
  <el-dialog v-model="visible" :title="title" width="600">
    <el-form
      ref="formRef"
      v-loading="loading"
      :rules="rules"
      :model="formData"
      label-width="auto"
    >
      <el-form-item label="单位名称" prop="tenantName">
        <el-input v-model="formData.tenantName" />
      </el-form-item>
      <el-form-item label="消防安全管理人" prop="safetyManagerUser">
        <el-input v-model="formData.safetyManagerUser" />
      </el-form-item>
      <el-form-item label="消防安全管理人联系方式" prop="safetyManagerContact">
        <el-input v-model="formData.safetyManagerContact" />
      </el-form-item>
      <el-form-item label="法定责任人" prop="legalPerson">
        <el-input v-model="formData.legalPerson" />
      </el-form-item>
      <el-form-item label="法定责任人联系方式" prop="legalPersonContact">
        <el-input v-model="formData.legalPersonContact" />
      </el-form-item>
      <el-form-item label="单位编码" prop="tenantCode">
        <el-input v-model="formData.tenantCode" />
      </el-form-item>
      <el-form-item label="单位类型" prop="tenantType">
        <dict-selector
          :key="dictKey"
          v-model="formData.tenantType"
          parentKey="004000"
        />
      </el-form-item>

      <el-form-item label="紧急联系方式" prop="emergencyContact">
        <el-input v-model="formData.emergencyContact" />
      </el-form-item>
      <el-form-item
        label="消防设施自管清单"
        prop="selfManagedFireFacilityListIds"
      >
        <g-upload
          v-model:value="formData.selfManagedFireFacilityListIds"
          :file-data-list="formData.selfManagedFireFacilityList"
          type="selfManagedFireFacilityList"
          list-type="picture-card"
        />
      </el-form-item>
      <el-form-item label="装修审批文件" prop="decorationApprovalFilesIds">
        <g-upload
          v-model:value="formData.decorationApprovalFilesIds"
          :file-data-list="formData.decorationApprovalFiles"
          type="decorationApprovalFiles"
          list-type="picture-card"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
