<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, FormRules } from 'element-plus'
import { get, post } from '@/utils/http/request'
import TableComposition from '@/composition/TableComposition'
import TenantForm from '@v/highRiseMananger/tenant/components/TenantForm.vue'

interface TenantSelectForm {
  buildId: string
  buildFloorId: string
  tenantId: string
  checkInDate: string
  fileIds: string[]
}

const props = defineProps<{
  buildId: string
  buildFloorId: string
}>()

const emits = defineEmits(['ok', 'close'])

// 弹窗显示控制
const visible = ref(false)
const title = ref('选择入驻单位')

// 表单数据
const formData = reactive<TenantSelectForm>({
  buildId: '',
  buildFloorId: '',
  tenantId: '',
  checkInDate: '',
  fileIds: []
})

// 文件数据列表
const fileDataList = ref([])

// 表单验证规则
const rules = reactive<FormRules<TenantSelectForm>>({
  tenantId: [{ required: true, message: '请选择入驻单位', trigger: 'change' }],
  checkInDate: [
    { required: true, message: '请选择入驻日期', trigger: 'change' }
  ],
  fileIds: [
    { required: true, message: '请上传建筑消防图纸', trigger: 'change' }
  ]
})

// 表单引用
const formRef = ref()

// 选中的入驻单位
const selectedTenant = ref<any>(null)

// 计算属性处理选中的ID
const selectedTenantId = computed({
  get: () => selectedTenant.value?.id || '',
  set: (value: string) => {
    // 这个setter不会被直接调用，但需要定义
  }
})

// 新增入驻单位弹窗引用
const tenantFormRef = ref()

// 使用TableComposition复用TenantList的功能
const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange
} = TableComposition({
  urls: {
    list: '/build/tenant/page',
    normal: '/build/tenant',
    rowKey: 'id'
  }
})

// 初始化表单数据
const initFormData = () => {
  formData.buildId = props.buildId
  formData.buildFloorId = props.buildFloorId
  formData.tenantId = ''
  formData.checkInDate = ''
  formData.fileIds = []
  fileDataList.value = []
  selectedTenant.value = null
}

// 打开弹窗
const handleOpen = () => {
  visible.value = true
  queryParams.value.tenantName = ''
  queryParams.value.tenantCode = ''
  initFormData()
  loadTableData(1)
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  emits('close')
}

// 选择入驻单位
const handleSelectTenant = (row: any) => {
  selectedTenant.value = row
  formData.tenantId = row.id
}

// 打开新增入驻单位弹窗
const handleAddTenant = () => {
  tenantFormRef.value?.handleAdd()
}

// 新增入驻单位成功回调
const handleTenantFormOk = () => {
  // 刷新入驻单位列表
  loadTableData(1)
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    const submitData = {
      buildId: formData.buildId,
      buildFloorId: formData.buildFloorId,
      tenantId: formData.tenantId,
      checkInDate: formData.checkInDate,
      fileIds: formData.fileIds
    }

    const res = await post('/build/build/floor/tenant', submitData)
    if (res && res.success) {
      ElMessage.success('保存成功')
      handleClose()
      emits('ok')
    } else {
      ElMessage.error(res.msg || '保存失败')
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 处理文件上传变化
const handleFileChange = (fileIds: string[]) => {
  formData.fileIds = fileIds
}

onMounted(() => {
  // loadTableData(1)
})

defineExpose({ handleOpen })
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="1000px"
    @close="handleClose"
  >
    <div class="tenant-select-container">
      <!-- 入驻单位列表 -->
      <div class="tenant-list-section">
        <h3>入驻单位列表</h3>
        <div class="mb-3">
          <el-form :inline="true" label-width="auto">
            <el-form-item label="单位名称">
              <el-input
                v-model="queryParams.tenantName"
                placeholder="请输入单位名称"
              />
            </el-form-item>
            <el-form-item label="单位编码">
              <el-input
                v-model="queryParams.tenantCode"
                placeholder="请输入单位编码"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="loadTableData(1)"
                >查询</el-button
              >
              <el-button @click="resetTable">重置</el-button>
              <el-button type="success" @click="handleAddTenant"
                >新增入驻单位</el-button
              >
            </el-form-item>
          </el-form>
        </div>

        <el-table
          v-loading="loading"
          :data="tableData"
          border
          row-key="id"
          style="width: 100%"
          :row-class-name="
            row => (selectedTenant?.id === row.row.id ? 'selected-row' : '')
          "
          :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ textAlign: 'center' }"
          @row-click="handleSelectTenant"
        >
          <el-table-column label="单位名称" prop="tenantName" />
          <el-table-column label="单位代码" prop="tenantCode" />
          <el-table-column label="安全管理员" prop="safetyManagerUser" />
          <el-table-column label="法人" prop="legalPerson" />
          <el-table-column label="应急联系方式" prop="emergencyContact" />
          <el-table-column label="选择" width="80">
            <template #default="scope">
              <el-radio
                v-model="selectedTenantId"
                :label="scope.row.id"
                @change="handleSelectTenant(scope.row)"
              >
                选择
              </el-radio>
            </template>
          </el-table-column>
        </el-table>

        <div class="flex justify-end mt-2">
          <el-pagination
            v-model:current-page="pageInfo.pageNum"
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="pageSizeList"
            :page-size="pageInfo.pageSize"
            :total="pageInfo.totalRecords"
            @size-change="handlePaginationSizeChange"
            @current-change="handlePaginationPageChange"
          />
        </div>
      </div>

      <!-- 入驻信息表单 -->
      <div v-if="selectedTenant" class="tenant-form-section">
        <h3>入驻信息</h3>
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="120px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="入驻单位">
                <el-input
                  v-model="selectedTenant.tenantName"
                  disabled
                  placeholder="已选择的入驻单位"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="入驻日期" prop="checkInDate">
                <el-date-picker
                  v-model="formData.checkInDate"
                  type="date"
                  placeholder="请选择入驻日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="建筑消防图纸" prop="fileIds">
            <g-upload
              v-model:value="formData.fileIds"
              :file-data-list="fileDataList"
              type="buildingFirePlanImage"
              list-type="picture-card"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :disabled="!selectedTenant"
          @click="handleSubmit"
        >
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 新增入驻单位弹窗 -->
  <tenant-form ref="tenantFormRef" @ok="handleTenantFormOk" />
</template>

<style scoped>
.tenant-select-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tenant-list-section,
.tenant-form-section {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.tenant-list-section h3,
.tenant-form-section h3 {
  margin: 0 0 16px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.selected-row {
  background-color: #f0f9ff !important;
}

:deep(.el-table .selected-row) {
  background-color: #f0f9ff !important;
}

:deep(.el-table .selected-row:hover) {
  background-color: #e0f2fe !important;
}
</style>
