<script setup lang="ts">
import { reactive, ref } from 'vue'
import { FormRules } from 'element-plus'
import FormComposition from '@/composition/FormCompostion'

interface FloorTenantEditForm {
  id?: string
  tenantName?: string
  tenantCode?: string
  checkInDate?: string
  fileIds?: string[]
}

const emits = defineEmits(['ok'])
// 表单验证规则
const rules = reactive<FormRules<FloorTenantEditForm>>({
  checkInDate: [
    { required: true, message: '请选择入驻日期', trigger: 'change' }
  ],
  fileIds: [
    { required: true, message: '请上传建筑消防图纸', trigger: 'change' }
  ]
})

// 处理提交参数
const dealSubmitParams = (data: any) => {
  // 确保fileIds字段存在
  if (!data.fileIds) {
    data.fileIds = []
  }
}

// 处理记录数据
const dealRecord = (record: any) => {
  // 确保文件数据正确初始化
  if (!record.fileIds) {
    record.fileIds = []
  }
  if (!record.buildingFirePlanImage) {
    record.buildingFirePlanImage = []
  }

  // 从tenant对象中获取入驻单位信息
  if (record.tenant) {
    record.tenantName = record.tenant.tenantName
    record.tenantCode = record.tenant.tenantCode
  }
}

const {
  visible,
  title,
  formData,
  formRef,
  loading,
  handleEdit,
  handleSubmit,
  handleClose,
  handleLook,
  showBtn
} = FormComposition({
  urls: {
    normal: '/build/build/floor/tenant',
    detail: '/build/build/floor/tenant',
    rowKey: 'id'
  },
  dealSubmitParams,
  dealRecord,
  emits
})

defineExpose({ handleEdit, handleLook })
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="600px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      v-loading="loading"
      :rules="rules"
      :model="formData"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="入驻单位名称">
            <el-input
              v-model="formData.tenantName"
              disabled
              placeholder="入驻单位名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="入驻单位编码">
            <el-input
              v-model="formData.tenantCode"
              disabled
              placeholder="入驻单位编码"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="入驻日期" prop="checkInDate">
            <el-date-picker
              v-model="formData.checkInDate"
              type="date"
              placeholder="请选择入驻日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="建筑消防图纸" prop="fileIds">
        <g-upload
          v-model:value="formData.fileIds"
          :file-data-list="formData.buildingFirePlanImage"
          type="buildingFirePlanImage"
          list-type="picture-card"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div v-show="showBtn" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
