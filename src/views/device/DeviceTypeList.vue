<script setup lang="ts">
import { ElButton, ElTable, ElTableColumn } from 'element-plus'
import TableComposition from '@/composition/TableComposition'
import { onMounted } from 'vue'
import DeviceTypeForm from '@v/device/components/DeviceTypeForm.vue'
import URLS from '@api/URLS'

const emits = defineEmits(['change-tab'])

onMounted(() => {
  loadTableData(1)
})

const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit
} = TableComposition({
  urls: {
    list: URLS.BUILD + '/build/device-type',
    normal: URLS.BUILD + '/build/device-type',
    rowKey: 'id'
  }
})
</script>

<template>
  <div class="mb-3">
    <el-form :inline="true" label-width="100px">
      <el-form-item label="设备类型名称">
        <el-input
          v-model="queryParams.deviceTypeName"
          placeholder="请输入设备类型名称"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="loadTableData(1)"> 查询 </el-button>
        <el-button type="primary" @click="resetTable"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </div>
  <div class="mb-2">
    <el-button v-auth="'deviceAdd'" type="primary" @click="handleAdd">
      新增
    </el-button>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
  >
    <el-table-column label="类型名称" prop="deviceTypeName" />
    <el-table-column label="类型编码" prop="deviceTypeCode" />
    <el-table-column label="备注" prop="deviceTypeDesc" />
    <el-table-column label="操作" width="250">
      <template #default="scope">
        <div class="flex items-center h-full">
          <el-button
            v-auth="'deviceEdit'"
            size="small"
            type="primary"
            link
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-popconfirm
            class="box-item"
            title="确认删除该资源吗"
            placement="top-start"
            @confirm="handleDelete(scope.row)"
          >
            <template #reference>
              <el-button v-auth="'deviceDel'" size="small" type="danger" link>
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>
  <device-type-form ref="formRef" @ok="loadTableData(1)" />
</template>
