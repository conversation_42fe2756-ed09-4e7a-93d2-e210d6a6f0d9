<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElButton, ElTableColumn } from 'element-plus'
import FormComposition from '@/composition/FormCompostion'
import URLS from '@api/URLS'

const rules = reactive({
  deviceTypeName: [
    { required: true, message: '请输入类型名称', trigger: 'blur' }
  ],
  deviceTypeCode: [
    { required: true, message: '请输入类型编码', trigger: 'blur' }
  ],
  deviceTypeDesc: [{ required: false, message: '请输入备注', trigger: 'blur' }],
  fileIds: [{ required: true, message: '请上传图标', trigger: 'blur' }],
  deviceTypeAttrs: [
    { required: true, message: '请维护设备属性', trigger: 'blur' }
  ]
})

const emits = defineEmits(['ok'])

const generateRandomString = (length: number) => {
  let result = ''
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const charactersLength = characters.length
  let randomValues = new Uint32Array(length)
  window.crypto.getRandomValues(randomValues)
  for (let i = 0; i < length; i++) {
    result += characters.charAt(randomValues[i] % charactersLength)
  }
  return result
}

const handleAddAttr = () => {
  const deviceTypeAttrs = formData.value.deviceTypeAttrs
  const temp = {
    id: generateRandomString(10),
    attrName: '',
    attrCode: ''
  }
  if (deviceTypeAttrs) {
    deviceTypeAttrs.push(temp)
  } else {
    formData.value.deviceTypeAttrs = [temp]
  }
  currentData.value = { ...temp }
  rowEdit.value = true
}

const currentData = ref({
  id: '',
  attrName: '',
  attrCode: ''
})
const rowEdit = ref(false)

const handleDelete = row => {
  const index = formData.value.deviceTypeAttrs.findIndex(
    item => item.id === row.id
  )
  console.log('handleDelete.index', index, row)
  if (index !== -1) {
    formData.value.deviceTypeAttrs.splice(index, 1)
  }
}
const handleEditTableRow = row => {
  currentData.value = { ...row }
  rowEdit.value = true
}

const handleSubmitRow = () => {
  rowEdit.value = false
  const index = formData.value.deviceTypeAttrs.findIndex(
    item => item.id === currentData.value.id
  )
  formData.value.deviceTypeAttrs[index] = {
    ...currentData.value
  }
  currentData.value = null
}

const addInit = () => {
  formData.value.deviceTypeAttrs = [
    {
      id: generateRandomString(10),
      attrName: '设备名称',
      attrCode: 'name',
      readonly: true
    },
    {
      id: generateRandomString(10),
      attrName: '设备编号',
      attrCode: 'code',
      readonly: true
    }
  ]
}
const readonlyItems = ['name', 'code']
const readOnly = data => {
  return readonlyItems.includes(data.attrCode)
}

const {
  visible,
  title,
  formData,
  formRef,
  loading,
  handleAdd,
  handleEdit,
  handleSubmit,
  handleClose
} = FormComposition({
  urls: {
    normal: URLS.BUILD + '/build/device-type',
    detail: URLS.BUILD + '/build/device-type',
    rowKey: 'id'
  },
  emits,
  addInit
})

defineExpose({ handleAdd, handleEdit })
</script>

<template>
  <el-dialog v-model="visible" :title="title" width="600">
    <el-form
      ref="formRef"
      v-loading="loading"
      :rules="rules"
      :model="formData"
      label-width="auto"
    >
      <el-form-item label="类型名称" prop="deviceTypeName">
        <el-input v-model="formData.deviceTypeName" />
      </el-form-item>
      <el-form-item label="类型编码" prop="deviceTypeCode">
        <el-input v-model="formData.deviceTypeCode" />
      </el-form-item>
      <el-form-item label="备注" prop="deviceTypeDesc">
        <el-input v-model="formData.deviceTypeDesc" type="textarea" :rows="3" />
      </el-form-item>
      <el-form-item label="图标" prop="fileIds">
        <g-upload
          v-model:value="formData.fileIds"
          :file-data-list="formData.deviceTypeLogo"
          type="deviceTypeLogo"
          list-type="picture-card"
        />
      </el-form-item>
      <el-form-item label="设备属性" prop="deviceTypeAttrs">
        <el-table :data="formData.deviceTypeAttrs" border row-key="id">
          <el-table-column label="属性名" prop="attrName">
            <template #default="scope">
              <el-input
                v-if="rowEdit && currentData.id === scope.row.id"
                v-model="currentData.attrName"
              />
              <span v-else>
                {{ scope.row.attrName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="属性编码" prop="attrCode">
            <template #default="scope">
              <el-input
                v-if="rowEdit && currentData.id === scope.row.id"
                v-model="currentData.attrCode"
              />
              <span v-else>
                {{ scope.row.attrCode }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <div v-if="!readOnly(scope.row)" class="flex items-center h-full">
                <template v-if="!rowEdit || currentData.id !== scope.row.id">
                  <el-button
                    size="small"
                    type="primary"
                    link
                    @click="handleEditTableRow(scope.row)"
                  >
                    编辑
                  </el-button>
                  <el-divider direction="vertical" />
                  <el-popconfirm
                    class="box-item"
                    title="确认删除该资源吗"
                    placement="top-start"
                    @confirm="handleDelete(scope.row)"
                  >
                    <template #reference>
                      <el-button size="small" type="danger" link>
                        删除
                      </el-button>
                    </template>
                  </el-popconfirm>
                </template>
                <el-button
                  v-else
                  size="small"
                  type="success"
                  link
                  @click="handleSubmitRow"
                >
                  确定
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-button
          link
          type="primary"
          class="mt-4"
          style="width: 100%"
          @click="handleAddAttr"
        >
          添加
        </el-button>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
