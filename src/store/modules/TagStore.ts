import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { RouteConfigs } from '@/layout/types'
import { router } from '@/router' // 假设 router 实例可以这样导入，用于导航

const useTagStore = defineStore('tagStore', () => {
  const tags = ref<RouteConfigs[]>([])
  const currentTag = ref<RouteConfigs | null>(null)

  // 添加标签
  function addTag(tag: RouteConfigs) {
    if (!tag || !tag.path) return // 确保 tag 和 tag.path 有效
    const existingTag = tags.value.find(t => t.path === tag.path)
    if (!existingTag) {
      tags.value.push(tag)
    }
    currentTag.value = tag
  }

  // 关闭标签
  function closeTag(tag: RouteConfigs) {
    if (!tag || !tag.path) return
    const index = tags.value.findIndex(t => t.path === tag.path)
    if (index === -1) return

    tags.value.splice(index, 1)

    // 如果关闭的是当前标签
    if (currentTag.value && currentTag.value.path === tag.path) {
      if (tags.value.length > 0) {
        // 尝试激活右边的标签，如果不存在则激活左边的
        const newCurrentTag = tags.value[index] || tags.value[index - 1]
        currentTag.value = newCurrentTag
        if (newCurrentTag && newCurrentTag.path) {
          router.push(newCurrentTag.path) // 导航到新的当前标签
        }
      } else {
        currentTag.value = null
        // 可选：导航到首页或默认页
        // router.push('/')
      }
    }
  }

  // 关闭当前标签
  function closeCurrentTag() {
    if (currentTag.value) {
      closeTag(currentTag.value)
    }
  }

  // 关闭左侧标签页
  function closeLeftTags() {
    if (!currentTag.value) return
    const currentIndex = tags.value.findIndex(
      t => t.path === currentTag.value!.path
    )
    if (currentIndex > 0) {
      tags.value = tags.value.slice(currentIndex)
    }
  }

  // 关闭右侧标签页
  function closeRightTags() {
    if (!currentTag.value) return
    const currentIndex = tags.value.findIndex(
      t => t.path === currentTag.value!.path
    )
    if (currentIndex !== -1 && currentIndex < tags.value.length - 1) {
      tags.value = tags.value.slice(0, currentIndex + 1)
    }
  }

  // 关闭其他标签页
  function closeOtherTags() {
    if (!currentTag.value) return
    tags.value = [currentTag.value]
  }

  // 关闭全部标签页
  function closeAllTags() {
    tags.value = []
    currentTag.value = null
    // 可选：导航到首页或默认页
    // router.push('/')
  }

  return {
    tags,
    currentTag,
    addTag,
    closeTag,
    closeCurrentTag,
    closeLeftTags,
    closeRightTags,
    closeOtherTags,
    closeAllTags
  }
})

export default useTagStore
