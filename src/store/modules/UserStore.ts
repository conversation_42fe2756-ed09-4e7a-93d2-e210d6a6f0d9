import { defineStore } from 'pinia'
import { ref } from 'vue'
import router from '@/router'
import { type IDataResponse, post } from '@/utils/http/request'

export interface PasswordForm {
  username: string
  password: string
  imageCode?: string
}

export interface SmsForm {
  username: string
  smsCode: string
}

export interface QrcodeForm {
  id4: string
}

export interface LoginResult {
  admin: boolean
  auth: AuthInfo
  currentAuths: AuthObject
  token: string
  user: UserInfo
  extend: UserExtend
}

export interface UserInfo {
  userId: string
  fullName: string
}

export interface AuthInfo {
  privateAuths: AuthGroup[]
  publicAuths: AuthObject
}

export interface AuthGroup {
  auths: AuthObject[]
  groupId: string
  groupName: string
}

export interface AuthObject {
  [key: string]: ObjectInfo[]
}

export interface ObjectInfo {
  objectId: string
  objectName: string
  type: string
  code: string
  status: string
  extend: object
}

export interface UserExtend {
  avatar?: string
  nickname?: string
}

const BASIC_KEY = 'Basic cGNfY2xpZW50OnBjX2NsaWVudA=='

const userStore = defineStore(
  'userStore',
  () => {
    const admin = ref(false)
    const auth = ref<AuthInfo | null>(null)
    const currentAuth = ref<AuthObject | null>(null)
    const token = ref<string | null>()
    const userInfo = ref<UserInfo | null>(null)
    const extend = ref<UserExtend | null>(null)

    const handleLoginSuccess = (res: IDataResponse<LoginResult>) => {
      if (res && res.success) {
        admin.value = res.data.admin
        auth.value = res.data.auth
        currentAuth.value = res.data.currentAuths
        token.value = res.data.token
        userInfo.value = res.data.user
        extend.value = res.data.extend
      } else {
        throw res
      }

      return res
    }

    /**
     * 账号密码登录
     * @param form 登录信息
     * @param deviceId 图片验证码设备ID
     */
    const loginPassword = async (form: PasswordForm, deviceId?: string) => {
      return post('/auth/form', form, {
        headers: {
          Authorization: BASIC_KEY,
          ...(deviceId && { imageDeviceId: deviceId })
        }
      }).then(handleLoginSuccess)
    }

    /**
     * 短信验证码登录
     * @param form 登录信息
     */
    const loginSms = async (form: SmsForm) => {
      return post('/auth/sms', form, {
        headers: {
          Authorization: BASIC_KEY,
          smsCodeDeviceId: form.username
        }
      }).then(handleLoginSuccess)
    }

    /**
     * 二维码登录
     * @param form 登录信息
     */
    const loginQrcode = async (form: QrcodeForm) => {
      return post('/auth/qrcode', form, {
        headers: {
          Authorization: BASIC_KEY
        }
      }).then(handleLoginSuccess)
    }

    /**
     * 选择权限组
     * @param group 权限组ID
     */
    const loginChoice = async (group: string) => {
      return post('/auth/choice', { group }).then(handleLoginSuccess)
    }

    const logout = async () => {
      return post('/logout').then(() => {
        admin.value = false
        auth.value = null
        currentAuth.value = null
        token.value = null
        userInfo.value = null
        extend.value = null
        router.push({ name: 'Login' })
      })
    }

    return {
      admin,
      auth,
      currentAuth,
      token,
      userInfo,
      extend,
      loginPassword,
      loginSms,
      loginQrcode,
      loginChoice,
      logout
    }
  },
  {
    persist: true
  }
)

export default userStore
