import { defineStore } from 'pinia'
import { ref } from 'vue'
import { get, type IDataResponse } from '@/utils/http/request'
// import { useCard } from '@/layout/hooks/useCard'
import { addAsyncRoutes } from '@/router/utils'
import { router } from '@/router'

export interface MenuInfo {
  id: string
  title: string
  component: any
  type: string
  name: string
  path: string
  redirect: string
  meta: {
    icon: string
    show: boolean
    extraIcon: string
    backstage?: boolean
    frameSrc?: string
    externalLink?: string
    blank?: boolean
    auths: string[]
  }
  children?: MenuInfo[]
}

const usePermissionStore = defineStore('permissionStore', () => {
  // const { isCard } = useCard()

  // 所有路由信息
  const allPermissions = ref<MenuInfo[]>([])
  // 所有路由展平为一维数组
  const flatteningPermissions = ref<MenuInfo[]>([])
  // 所有路由key
  const permissionKeys = ref<string[]>([])
  // 当前显示的card key
  // const showCard = ref<string | undefined>()
  // 当前显示的菜单
  const showPermissions = ref<MenuInfo[]>([])
  const firstMenu = ref<MenuInfo | undefined>()
  // 缓存页面keepAlive
  // const cachePageList = ref<string[]>()

  const initialState = ref(false)

  // const refreshDate: number = new Date().getTime()

  const getPermission = async (parentKey?: string) => {
    allPermissions.value = []
    flatteningPermissions.value = []
    permissionKeys.value = []
    showPermissions.value = []
    firstMenu.value = undefined

    const flatten = (
      permissions: MenuInfo[],
      first: boolean = true,
      parent?: MenuInfo
    ): MenuInfo[] => {
      const menus: MenuInfo[] = []

      permissions.forEach(item => {
        const menuInfo: MenuInfo = { ...item }
        flatteningPermissions.value.push(item)
        permissionKeys.value.push(item.name)

        // 处理子集
        const children = item.children
        if (children && children.length > 0) {
          menuInfo.children = flatten(children, first, item)
        }

        // 处理按钮 权限
        if (item.type === '00019') {
          if (parent.meta.auths) {
            parent.meta.auths.push(item.name)
          } else {
            parent.meta.auths = [item.name]
          }
          return
        }

        // 记录第一个菜单
        if (!firstMenu.value && item.meta.show) {
          firstMenu.value = item
        }

        // push menu
        if (item.type === '00015' && item.meta.show) {
          menus.push(menuInfo)
        }

        // 最外层添加所有路由
        if (first && item.meta?.externalLink !== '1') {
          router.addRoute(item as any)
        }
      })

      return menus
    }

    return get('/voucher/s/res/my', { ...(parentKey && { parentKey }) }).then(
      (res: IDataResponse<MenuInfo[]>) => {
        console.log('获取菜单数据', res)
        if (res && res.success) {
          addAsyncRoutes(res.data as any)
          console.log('111', res.data)
          allPermissions.value = res.data
          showPermissions.value = flatten(res.data)
          initialState.value = true
          console.log('显示的菜单', showPermissions.value)
          return res
        }
        initialState.value = false
        throw res
      }
    )
  }

  const checkAuths = (name: string) => {
    return permissionKeys.value.find(item => item === name)
  }

  return {
    allPermissions,
    showPermissions,
    firstMenu,
    initialState,
    checkAuths,
    getPermission
  }
})

export default usePermissionStore
