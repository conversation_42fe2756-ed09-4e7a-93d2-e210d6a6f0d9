<template>
  <div
    ref="container"
    class="jessibuca"
    style="
      position: relative;
      width: 100%;
      height: 100%;
      margin: 0 auto;
      background-color: #000;
    "
    @dblclick="fullscreenSwich"
  >
    <div style=" position: relative;width: 100%; padding-top: 56.25%" />
    <div id="buttonsBox" class="buttons-box">
      <div class="buttons-box-left">
        <i
          v-if="!playing"
          class="iconfont icon-play jessibuca-btn"
          @click="playBtnClick"
        />
        <i
          v-if="playing"
          class="iconfont icon-pause jessibuca-btn"
          @click="pause"
        />
        <i class="iconfont icon-stop jessibuca-btn" @click="destroy" />
        <i
          v-if="isNotMute"
          class="iconfont icon-audio-high jessibuca-btn"
          @click="mute()"
        />
        <i
          v-if="!isNotMute"
          class="iconfont icon-audio-mute jessibuca-btn"
          @click="cancelMute()"
        />
        <el-popover v-if="showSetting" placement="top-start" trigger="hover">
          <div style="display: flex; justify-content: left">
            <div class="control-wrapper">
              <div
                class="control-btn jessibuca-control-top"
                @mousedown="ptzCamera('up')"
                @mouseup="ptzCamera('stop')"
              >
                <i class="el-icon-caret-top" />
                <div class="jessibuca-control-inner-btn control-inner" />
              </div>
              <div
                class="control-btn jessibuca-control-left"
                @mousedown="ptzCamera('left')"
                @mouseup="ptzCamera('stop')"
              >
                <i class="el-icon-caret-left" />
                <div class="jessibuca-control-inner-btn control-inner" />
              </div>
              <div
                class="control-btn jessibuca-control-bottom"
                @mousedown="ptzCamera('down')"
                @mouseup="ptzCamera('stop')"
              >
                <i class="el-icon-caret-bottom" />
                <div class="jessibuca-control-inner-btn control-inner" />
              </div>
              <div
                class="control-btn jessibuca-control-right"
                @mousedown="ptzCamera('right')"
                @mouseup="ptzCamera('stop')"
              >
                <i class="el-icon-caret-right" />
                <div class="jessibuca-control-inner-btn control-inner" />
              </div>
              <div class="jessibuca-control-round">
                <div class="control-round-inner">
                  <i class="fa fa-pause-circle" />
                </div>
              </div>
              <div
                style="position: absolute; top: 1.25rem; left: 7.25rem"
                @mousedown="ptzCamera('zoomin')"
                @mouseup="ptzCamera('stop')"
              >
                <i
                  class="el-icon-zoom-in control-zoom-btn"
                  style="font-size: 1.875rem; color: #fff"
                />
              </div>
              <div
                style="position: absolute; top: 3.25rem; left: 7.25rem"
                @mousedown="ptzCamera('zoomout')"
                @mouseup="ptzCamera('stop')"
              >
                <i
                  class="el-icon-zoom-out control-zoom-btn"
                  style="font-size: 1.875rem; color: #fff"
                />
              </div>
              <div
                class="contro-speed"
                style="position: absolute; top: 7rem; left: 4px; width: 9rem"
              >
                <el-slider v-model="controSpeed" :max="255" />
              </div>
            </div>
          </div>
          <template v-slot:reference>
            <i class="el-icon-setting jessibuca-btn" />
          </template>
        </el-popover>
      </div>
      <div class="buttons-box-right">
        <span class="jessibuca-btn">{{ kBps }} kb/s</span>
        <!--          <i class="iconfont icon-file-record1 jessibuca-btn"></i>-->
        <!--          <i class="iconfont icon-xiangqing2 jessibuca-btn" ></i>-->
        <i
          class="iconfont icon-camera1196054easyiconnet jessibuca-btn"
          style="font-size: 1rem !important"
          @click="screenshot"
        />
        <i
          class="iconfont icon-shuaxin11 jessibuca-btn"
          @click="playBtnClick"
        />
        <i
          v-if="!fullscreen"
          class="iconfont icon-weibiaoti10 jessibuca-btn"
          @click="fullscreenSwich"
        />
        <i
          v-if="fullscreen"
          class="iconfont icon-weibiaoti11 jessibuca-btn"
          @click="fullscreenSwich"
        />
      </div>
    </div>
  </div>
</template>

<script>
let jessibucaPlayer = {}
export default {
  name: 'jessibuca',
  props: {
    deviceId: {},
    channelId: {},
    videoUrl: {
      default: '',
      type: String
    },
    error: {},
    hasAudio: {},
    height: {},
    showSetting: {
      default: false
    }
  },
  data() {
    return {
      playing: false,
      isNotMute: false,
      quieting: false,
      fullscreen: false,
      loaded: false, // mute
      speed: 0,
      performance: '', // 工作情况
      kBps: 0,
      btnDom: null,
      videoInfo: null,
      volume: 1,
      rotate: 0,
      vod: true, // 点播
      forceNoOffscreen: false,
      controSpeed: 30
    }
  },
  watch: {
    videoUrl: {
      handler(val, _) {
        this.$nextTick(() => {
          this.play(val)
        })
      },
      immediate: true
    }
  },
  created() {
    let paramUrl = decodeURIComponent(this.$route.params.url)
    this.$nextTick(() => {
      console.log(2222)
      this.updatePlayerDomSize()
      window.onresize = this.updatePlayerDomSize
      if (typeof this.videoUrl == 'undefined') {
        // eslint-disable-next-line vue/no-mutating-props
        this.videoUrl = paramUrl
      }
      this.btnDom = document.getElementById('buttonsBox')
    })
  },
  // mounted() {
  //   const ro = new ResizeObserver(entries => {
  //     entries.forEach(entry => {
  //       this.updatePlayerDomSize()
  //     });
  //   });
  //   ro.observe(this.$refs.container);
  // },
  mounted() {
    this.updatePlayerDomSize()
  },
  unmounted() {
    if (jessibucaPlayer[this._uid]) {
      jessibucaPlayer[this._uid].destroy()
    }
    this.playing = false
    this.loaded = false
    this.performance = ''
  },
  methods: {
    ptzCamera(command) {
      this.$axios({
        method: 'post',
        url:
          '/api/ptz/control/' +
          //  "69292299001110000000"+
          this.deviceId +
          '/' +
          // "69292299001310000001"+
          this.channelId +
          '?command=' +
          command +
          '&horizonSpeed=' +
          this.controSpeed +
          '&verticalSpeed=' +
          this.controSpeed +
          '&zoomSpeed=' +
          this.controSpeed
      }).then(function (res) {})
    },
    updatePlayerDomSize() {
      let dom = this.$refs.container
      let width = dom.parentNode.clientWidth
      let height = (9 / 16) * width
      console.log(height)

      console.log(dom.clientHeight)
      if (height > dom.clientHeight) {
        height = dom.clientHeight
        width = (16 / 9) * height
      }
      if (width > 0 && height > 0) {
        dom.style.width = width + 'px'
        dom.style.height = height + 'px'
        dom.style.paddingTop = 0
        console.log(width)
        console.log(height)
      }
    },
    create() {
      let options = {
        container: this.$refs.container,
        autoWasm: true,
        background: '',
        controlAutoHide: false,
        debug: false,
        decoder: '/static/js/jessibuca/decoder.js',
        forceNoOffscreen: false,
        hasAudio: typeof this.hasAudio == 'undefined' ? true : this.hasAudio,
        heartTimeout: 5,
        heartTimeoutReplay: true,
        heartTimeoutReplayTimes: 3,
        hiddenAutoPause: false,
        hotKey: true,
        isFlv: false,
        isFullResize: false,
        isNotMute: this.isNotMute,
        isResize: false,
        keepScreenOn: true,
        loadingText: '请稍等, 视频加载中......',
        loadingTimeout: 10,
        loadingTimeoutReplay: true,
        loadingTimeoutReplayTimes: 3,
        openWebglAlignment: false,
        operateBtns: {
          fullscreen: false,
          screenshot: false,
          play: false,
          audio: false,
          record: false
        },
        recordType: 'mp4',
        rotate: 0,
        showBandwidth: false,
        supportDblclickFullscreen: false,
        timeout: 10,
        useMSE: true,
        useWCS:
          location.hostname === 'localhost' || location.protocol === 'https:',
        useWebFullScreen: true,
        videoBuffer: 0.1,
        wasmDecodeErrorReplay: true,
        wcsUseVideoRender: true
      }
      console.log('Jessibuca -> options: ', options)
      jessibucaPlayer[this._uid] = new window.Jessibuca({ ...options })

      let jessibuca = jessibucaPlayer[this._uid]
      let _this = this
      jessibuca.on('pause', function () {
        _this.playing = false
      })
      jessibuca.on('play', function () {
        _this.playing = true
      })
      jessibuca.on('fullscreen', function (msg) {
        _this.fullscreen = msg
      })
      jessibuca.on('mute', function (msg) {
        _this.isNotMute = !msg
      })
      jessibuca.on('performance', function (performance) {
        let show = '卡顿'
        if (performance === 2) {
          show = '非常流畅'
        } else if (performance === 1) {
          show = '流畅'
        }
        _this.performance = show
      })
      jessibuca.on('kBps', function (kBps) {
        _this.kBps = Math.round(kBps)
      })
      jessibuca.on('videoInfo', function (msg) {
        console.log('Jessibuca -> videoInfo: ', msg)
      })
      jessibuca.on('audioInfo', function (msg) {
        console.log('Jessibuca -> audioInfo: ', msg)
      })
      jessibuca.on('error', function (msg) {
        console.log('Jessibuca -> error: ', msg)
      })
      jessibuca.on('timeout', function (msg) {
        console.log('Jessibuca -> timeout: ', msg)
      })
      jessibuca.on('loadingTimeout', function (msg) {
        console.log('Jessibuca -> timeout: ', msg)
      })
      jessibuca.on('delayTimeout', function (msg) {
        console.log('Jessibuca -> timeout: ', msg)
      })
      jessibuca.on('playToRenderTimes', function (msg) {
        console.log('Jessibuca -> playToRenderTimes: ', msg)
      })
    },
    playBtnClick: function (event) {
      this.play(this.videoUrl)
    },
    play: function (url) {
      console.log('Jessibuca -> url: ', url)
      if (jessibucaPlayer[this._uid]) {
        this.destroy()
      }
      this.create()
      jessibucaPlayer[this._uid].on('play', () => {
        this.playing = true
        this.loaded = true
        this.quieting = jessibuca.quieting
      })
      if (jessibucaPlayer[this._uid].hasLoaded()) {
        jessibucaPlayer[this._uid].play(url)
      } else {
        jessibucaPlayer[this._uid].on('load', () => {
          jessibucaPlayer[this._uid].play(url)
        })
      }
    },
    pause: function () {
      if (jessibucaPlayer[this._uid]) {
        jessibucaPlayer[this._uid].pause()
      }
      this.playing = false
      this.err = ''
      this.performance = ''
    },
    screenshot: function () {
      if (jessibucaPlayer[this._uid]) {
        jessibucaPlayer[this._uid].screenshot()
      }
    },
    mute: function () {
      if (jessibucaPlayer[this._uid]) {
        jessibucaPlayer[this._uid].mute()
      }
    },
    cancelMute: function () {
      if (jessibucaPlayer[this._uid]) {
        jessibucaPlayer[this._uid].cancelMute()
      }
    },
    destroy: function () {
      if (jessibucaPlayer[this._uid]) {
        jessibucaPlayer[this._uid].destroy()
      }
      if (document.getElementById('buttonsBox') == null) {
        this.$refs.container.appendChild(this.btnDom)
      }
      jessibucaPlayer[this._uid] = null
      this.playing = false
      this.err = ''
      this.performance = ''
    },
    fullscreenSwich: function () {
      let isFull = this.isFullscreen()
      jessibucaPlayer[this._uid].setFullscreen(!isFull)
      this.fullscreen = !isFull
    },
    isFullscreen: function () {
      return (
        document.fullscreenElement ||
        document.msFullscreenElement ||
        document.mozFullScreenElement ||
        document.webkitFullscreenElement ||
        false
      )
    }
  }
}
</script>

<style>
.buttons-box {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 10;
  display: -webkit-box;
  display: flexbox;
  display: flex;
  width: 100%;
  height: 28px;
  user-select: none;
  background-color: rgb(43 51 63 / 70%);
}

.jessibuca-btn {
  width: 20px;
  padding: 0 2px;
  margin: 0 10px;
  font-size: 0.8rem !important;
  line-height: 27px;
  color: rgb(255 255 255);
  text-align: center;
  cursor: pointer;
}

.buttons-box-right {
  position: absolute;
  right: 0;
}
</style>

<style>
.el-popover {
  background-color: unset;
  border: unset;
}

.control-wrapper {
  position: relative;
  float: left;
  width: 6.25rem;
  max-width: 6.25rem;
  height: 6.25rem;
  max-height: 6.25rem;

  /* margin-top: 1.5rem; */
  margin-left: 0.5rem;
  border-radius: 100%;
}

.control-panel {
  position: relative;
  top: 0;
  left: 5rem;
  height: 11rem;
  max-height: 11rem;
}

.control-btn {
  position: absolute;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  width: 44%;
  height: 44%;
  border: 1px solid #78aee4;
  border-radius: 5px;
  transition: all 0.3s linear;
}

.control-btn:hover {
  cursor: pointer;
}

.control-btn i {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #78aee4;
}

.control-btn i:hover {
  cursor: pointer;
}

.control-zoom-btn:hover {
  cursor: pointer;
}

.jessibuca-control-round {
  position: absolute;
  top: 21%;
  left: 21%;
  width: 58%;
  height: 58%;
  background: #fff;
  border-radius: 100%;
}

.control-round-inner {
  position: absolute;
  top: 13%;
  left: 13%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70%;
  height: 70%;
  font-size: 40px;
  color: #78aee4;
  border: 1px solid #78aee4;
  border-radius: 100%;
  transition: all 0.3s linear;
}

.jessibuca-control-inner-btn {
  position: absolute;
  width: 60%;
  height: 60%;

  /* background: #000000; */
}

.jessibuca-control-top,
.jessibuca-control-left,
.jessibuca-control-right,
.jessibuca-control-bottom {
  background-color: #000;
}

.jessibuca-control-top {
  top: -8%;
  left: 27%;
  border-radius: 5px 100% 5px 0;
  transform: rotate(-45deg);
}

.jessibuca-control-top i {
  border-radius: 5px 100% 5px 0;
  transform: rotate(45deg);
}

.jessibuca-control-top .control-inner {
  bottom: -2px;
  left: -2px;
  border-top: 1px solid #78aee4;
  border-right: 1px solid #78aee4;
  border-radius: 0 100% 0 0;
}

.jessibuca-control-top .fa {
  transform: rotate(45deg) translateY(-7px);
}

.jessibuca-control-left {
  top: 27%;
  left: -8%;
  border-radius: 5px 0 5px 100%;
  transform: rotate(45deg);
}

.jessibuca-control-left i {
  transform: rotate(-45deg);
}

.jessibuca-control-left .control-inner {
  top: -2px;
  right: -2px;
  border-bottom: 1px solid #78aee4;
  border-left: 1px solid #78aee4;
  border-radius: 0 0 0 100%;
}

.jessibuca-control-left .fa {
  transform: rotate(-45deg) translateX(-7px);
}

.jessibuca-control-right {
  top: 27%;
  right: -8%;
  border-radius: 5px 100% 5px 0;
  transform: rotate(45deg);
}

.jessibuca-control-right i {
  transform: rotate(-45deg);
}

.jessibuca-control-right .control-inner {
  bottom: -2px;
  left: -2px;
  border-top: 1px solid #78aee4;
  border-right: 1px solid #78aee4;
  border-radius: 0 100% 0 0;
}

.jessibuca-control-right .fa {
  transform: rotate(-45deg) translateX(7px);
}

.jessibuca-control-bottom {
  bottom: -8%;
  left: 27%;
  border-radius: 0 5px 100%;
  transform: rotate(45deg);
}

.jessibuca-control-bottom i {
  transform: rotate(-45deg);
}

.jessibuca-control-bottom .control-inner {
  top: -2px;
  left: -2px;
  border-right: 1px solid #78aee4;
  border-bottom: 1px solid #78aee4;
  border-radius: 0 0 100%;
}

.jessibuca-control-bottom .fa {
  transform: rotate(-45deg) translateY(7px);
}

.trank {
  width: 80%;
  height: 180px;
  padding: 0 10%;
  overflow: auto;
  text-align: left;
}

.trankInfo {
  width: 80%;
  padding: 0 10%;
}
</style>
