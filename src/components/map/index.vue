<template>
  <div id="my-map" style="width: 100%; height: 300px" />
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import useAmap from '@/composition/useAmap'
const emits = defineEmits(['updateArea'])
const props = defineProps({
  valueLalel: {
    type: String,
    required: false
  }
})
const { initMap, getLatInfo, getDistrictByAdress } = useAmap('my-map', {
  zoom: 13,
  center: [116.39, 39.9]
})

const mapInstance = ref(null)
const AMapRef = ref(null)
watch(
  () => props.valueLalel,
  async newVal => {
    console.log('地址:', newVal)
    if (newVal) {
      const locDetail: any = await getDistrictByAdress(newVal)
      console.log(locDetail)
      mapInstance?.value?.setCenter(locDetail.location.split(','))
      addMarker(locDetail.location.split(','))
    }
    // 可以在这里处理区域代码变化后的逻辑
  },
  { immediate: true }
)
onMounted(async () => {
  const { AMap, map } = await initMap()
  AMapRef.value = AMap
  mapInstance.value = map

  // 监听地图点击事件，添加标记点并获取省市区信息
  map.on('click', async e => {
    if (!AMapRef.value || !mapInstance.value) return
    const lnglat = e.lnglat
    console.log('地图点击位置:', e)
    mapInstance.value.clearMap()
    const marker = new AMapRef.value.Marker({
      position: [lnglat.lng, lnglat.lat],
      title: `标记点`
    })
    mapInstance.value.add(marker)
    const info: any = await getLatInfo(lnglat)
    const arr = [
      info.regeocode.addressComponent.towncode.slice(0, 2),
      info.regeocode.addressComponent.towncode.slice(0, 4),
      info.regeocode.addressComponent.towncode.slice(0, 6),
      info.regeocode.addressComponent.towncode.slice(0, 9)
    ]
    console.log(arr)
    emits('updateArea', arr)
    console.log('逆地理编码结果:', info)
  })
})

const addMarker = lat => {
  if (!mapInstance.value || !AMapRef.value) return
  mapInstance.value.clearMap()
  const marker = new AMapRef.value.Marker({
    position: lat
  })

  mapInstance.value.add(marker)

  // 点击标记事件
  marker.on('click', () => {
    alert('你点击了标记!')
  })
}

defineExpose({ addMarker })
</script>
