<script setup lang="ts">
import { ref, watch } from 'vue'

interface Column {
  prop: string
  label: string
  type?: 'index' | 'drag' | 'default'
}

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  columns: {
    type: Array as () => Column[],
    required: true
  },
  rowKey: {
    type: String,
    default: 'id'
  }
})
const emit = defineEmits(['update:data', 'change'])

const innerData = ref([...props.data])
watch(
  () => props.data,
  val => {
    innerData.value = [...val]
  }
)

let dragStartIndex = -1
const tbodyRef = ref<HTMLElement | null>(null)

function onDragStart(index: number) {
  dragStartIndex = index
}
function onDragOver(index: number) {
  // 可加高亮等效果
}
function onDrop(index: number) {
  if (dragStartIndex === -1 || dragStartIndex === index) return
  const moved = innerData.value.splice(dragStartIndex, 1)[0]
  innerData.value.splice(index, 0, moved)
  emit('update:data', innerData.value)
  emit('change', innerData.value)
  dragStartIndex = -1
}
function onDragEnd() {
  dragStartIndex = -1
}
</script>

<template>
  <div
    class="el-table el-table--fit el-table--border el-table--enable-row-hover el-table--medium draggable-table-root"
  >
    <div class="el-table__header-wrapper">
      <table class="el-table__header">
        <thead>
          <tr>
            <th v-for="col in columns" :key="col.prop" class="el-table__cell">
              <div class="cell">{{ col.label }}</div>
            </th>
          </tr>
        </thead>
      </table>
    </div>
    <div
      class="el-table__body-wrapper"
      style="max-height: 400px; overflow-y: auto"
    >
      <table class="el-table__body">
        <tbody ref="tbodyRef">
          <tr
            v-if="!innerData.length"
            class="el-table__row el-table__empty-block"
          >
            <td
              :colspan="columns.length"
              class="el-table__cell el-table__empty-text"
              style=" width: 100%;text-align: center"
            >
              暂无数据
            </td>
          </tr>
          <tr
            v-for="(row, rowIndex) in innerData"
            v-else
            :key="row[rowKey]"
            class="el-table__row"
            draggable="true"
            @dragstart="onDragStart(rowIndex)"
            @dragover.prevent="onDragOver(rowIndex)"
            @drop.prevent="onDrop(rowIndex)"
            @dragend="onDragEnd"
          >
            <td v-for="col in columns" :key="col.prop" class="el-table__cell">
              <span v-if="col.type === 'index'">{{ rowIndex + 1 }}</span>
              <span v-else-if="col.type === 'drag'">
                <span class="drag-handle"
                  ><svg viewBox="0 0 24 24" width="18" height="18">
                    <circle cx="5" cy="7" r="1.5" />
                    <circle cx="5" cy="12" r="1.5" />
                    <circle cx="5" cy="17" r="1.5" />
                    <circle cx="12" cy="7" r="1.5" />
                    <circle cx="12" cy="12" r="1.5" />
                    <circle cx="12" cy="17" r="1.5" />
                    <circle cx="19" cy="7" r="1.5" />
                    <circle cx="19" cy="12" r="1.5" />
                    <circle cx="19" cy="17" r="1.5" /></svg
                ></span>
              </span>
              <span v-else>{{ row[col.prop] }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<style scoped>
.draggable-table-root {
  width: 100%;
  overflow: hidden;
  font-size: 14px;
  background: #fff;
  border-radius: 4px;
}

.el-table__header-wrapper,
.el-table__body-wrapper {
  width: 100%;
  overflow-x: auto;
}

.el-table__header,
.el-table__body {
  width: 100%;
  border-spacing: 0;
  border-collapse: separate;
}

.el-table__cell {
  padding: 14px 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 15px;
  vertical-align: middle;
  color: #333;
  white-space: nowrap;
}

.el-table__row:nth-child(odd) {
  background: #fcfcfc;
}

.el-table__row:hover {
  background: #e6f7ff;
}

.el-table__header .el-table__cell {
  font-weight: 600;
  color: #666;
  background: #f2f6fc;
}

.discount-label {
  padding: 2px 6px;
  margin-left: 4px;
  font-size: 13px;
  font-weight: bold;
  color: #e4393c;
  background: #fff0f0;
  border-radius: 3px;
}

.drag-handle {
  margin-right: 4px;
  vertical-align: middle;
  color: #999;
  cursor: move;
}

.el-table__empty-block {
  background: #fff;
}

.el-table__empty-text {
  padding: 32px 0;
  font-size: 15px;
  color: #999;
}
</style>
