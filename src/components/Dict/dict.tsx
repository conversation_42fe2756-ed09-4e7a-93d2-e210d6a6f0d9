import { get } from '@/utils/http/request'
import { defineComponent, onMounted, ref, computed } from 'vue'

interface DictProps {
  /**
   * 绑定值
   */
  value?: string | string[]
  /**
   * 请求数据字典URL前缀
   */
  urlPrefix?: string
  /**
   * 请求数据字典URL
   * 如果设置了此属性，则忽略urlPrefix
   */
  url?: string
  /**
   * 请求数据字典URL参数
   */
  params?: object
  /**
   * 数据字典父级key
   */
  parentKey?: string
  /**
   * 显示类型
   */
  showType?: string
  /**
   * 是否多选
   * 如果showType为select时有效
   */
  multiple?: boolean
  /**
   * 选择框的占位符
   * 如果showType为select时有效
   */
  placeholder?: string
  /**
   * 数据字典字段映射
   */
  fields?: {
    label: string
    value: string
    children?: string
  }
}

interface DictItem {
  label: string
  value: string
  children?: DictItem[]

  [key: string]: any
}

const DictSelector = defineComponent(
  (props: DictProps, { slots, emit }) => {
    const loading = ref(false)
    const options = ref<DictItem[]>([])

    onMounted(() => {
      loading.value = true
      initDicts()
    })

    // 使用计算属性来处理 v-model
    const value = computed({
      get: () => props.value,
      set: val => emit('update:value', val)
    })

    const getUrl = () => {
      if (props.url) {
        return props.url
      }
      return `${props.urlPrefix}/dict/p/${props.parentKey}`
    }

    const initDicts = () => {
      const url = getUrl()
      get(url, props.params)
        .then(res => {
          if (res && res.success) {
            options.value =
              res.data.map(item => ({
                ...item,
                label: item[props.fields.label],
                value: item[props.fields.value],
                children: item[props.fields.children]
              })) || []
          } else {
            options.value = []
            console.error('请求数据字典信息错误', url, res)
          }
        })
        .finally(() => {
          loading.value = false
        })
    }

    const radioContent = () => {
      return (
        <el-radio-group v-model={value.value}>
          {options.value.map(item => (
            <el-radio label={item.value} key={item.value}>
              {item.label}
            </el-radio>
          ))}
        </el-radio-group>
      )
    }

    const checkboxContent = () => {
      return (
        <el-checkbox-group v-model={value.value}>
          {options.value.map(item => (
            <el-checkbox label={item.value} key={item.value}>
              {item.label}
            </el-checkbox>
          ))}
        </el-checkbox-group>
      )
    }

    const selectContent = () => {
      return (
        <el-select
          v-model={value.value}
          multiple={props.multiple}
          placeholder={props.placeholder}
        >
          {options.value.map(item => (
            <el-option label={item.label} value={item.value} key={item.value} />
          ))}
        </el-select>
      )
    }

    const getContent = () => {
      if (loading.value) {
        if (slots && slots.loading) {
          return slots.loading()
        }
        return <span>加载中...</span>
      }
      switch (props.showType) {
        case 'radio':
          return radioContent()
        case 'checkbox':
          return checkboxContent()
        case 'select':
          return selectContent()
        default:
          console.error('不支持的showType:', props.showType)
          return selectContent()
      }
    }

    return () => {
      return getContent()
    }
  },
  {
    props: {
      value: {
        type: [String, Array],
        default: ''
      },
      urlPrefix: {
        type: String,
        default: 'build'
      },
      url: {
        type: String
      },
      params: {
        type: Object,
        default: () => {}
      },
      parentKey: {
        type: String,
        default: ''
      },
      showType: {
        type: String,
        default: 'select'
      },
      multiple: {
        type: Boolean,
        default: false
      },
      placeholder: {
        type: String,
        default: '请选择'
      },
      fields: {
        type: Object,
        default: () => {
          return {
            label: 'value',
            value: 'code',
            children: 'children'
          }
        }
      }
    },
    emits: ['update:value']
  }
)

export default DictSelector
