<template>
  <div class="mb-3" style="margin-top: 18px">
    <el-form v-if="searchParams" :inline="true" label-width="auto">
      <el-form-item
        v-for="(item, index) in searchParams"
        :key="index"
        :label="item.label"
        :prop="item.prop"
      >
        <el-input
          v-model="queryParams[item.prop]"
          :placeholder="'请输入' + item.label"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()"> 查询 </el-button>
        <el-button type="primary" @click="resetTable"> 重置 </el-button>
      </el-form-item>
    </el-form>
  </div>
  <el-table
    ref="tableRef"
    v-loading="loading"
    :data="tableData"
    border
    row-key="id"
    style="width: 100%"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
    class="check-table"
    @selection-change="handleSelectionChange"
    @select="handleSelect"
    @select-all="handleSelectAll"
  >
    <el-table-column type="selection" width="50" align="center" />
    <el-table-column
      v-for="item in column"
      :key="item.prop"
      :label="item.label"
      :prop="item.prop"
    />
  </el-table>
  <div class="flex justify-end mt-2">
    <el-pagination
      v-model:current-page="pageInfo.pageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="pageSizeList"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.totalRecords"
      @size-change="handlePaginationSizeChange"
      @current-change="handlePaginationPageChange"
    />
  </div>
</template>
<script setup lang="ts">
import { ElButton, ElTable, ElTableColumn } from 'element-plus'
import { ref, onMounted, watch, nextTick, inject } from 'vue'
import TableComposition from '@/composition/TableComposition'
import { useRouter } from 'vue-router'
const router = useRouter()
const props = defineProps<{
  tableDataChose: any[]
  url: string
  column: any[]
  searchParams: any
}>()
const {
  tableData,
  pageInfo,
  queryParams,
  loading,
  pageSizeList,
  formRef,
  loadTableData,
  resetTable,
  handlePaginationSizeChange,
  handlePaginationPageChange,
  handleAdd,
  handleDelete,
  handleEdit
} = TableComposition({
  urls: {
    list: props.url,
    normal: props.url,
    rowKey: 'id'
  }
})
// 搜索
const search = () => {
  console.log('search', queryParams.value)
  loadTableData(1)
}
onMounted(() => {
  loadTableData(1)
  selectedRows.value = props.tableDataChose || []
  console.log('checkItemsTable mounted', props.tableDataChose)
  // 首次加载后自动回显勾选
  restoreSelection()
})

// 监听 tableData 变化，自动回显勾选
watch(tableData, () => {
  restoreSelection()
})

const tableRef = ref()
// 分页勾选核心：全局维护已选项
const selectedRows = ref<any[]>([])
function uniqueByRowKey(arr: any[], rowKey = 'id') {
  const map = new Map()
  arr.forEach(item => {
    if (item && item[rowKey] !== undefined) {
      map.set(item[rowKey], item)
    }
  })
  return Array.from(map.values())
}
function handleSelectionChange(selection: any[]) {
  const currentPageIds = tableData.value.map((row: any) => row.id)
  selectedRows.value = selectedRows.value.filter(
    row => !currentPageIds.includes(row.id)
  )
  selectedRows.value = uniqueByRowKey([...selectedRows.value, ...selection])
  console.log('handleSelectionChange', selection, selectedRows.value)
}
function handleSelect(selection: any[], row: any) {
  handleSelectionChange(selection)
}
function handleSelectAll(selection: any[]) {
  handleSelectionChange(selection)
}
function restoreSelection() {
  if (!tableRef.value) return
  const currentPageIds = tableData.value.map((row: any) => row.id)
  const needSelect = selectedRows.value.filter(row =>
    currentPageIds.includes(row.id)
  )
  tableRef.value.clearSelection && tableRef.value.clearSelection()
  nextTick(() => {
    needSelect.forEach(row => {
      const realRow = tableData.value.find((r: any) => r.id === row.id)
      if (realRow) tableRef.value.toggleRowSelection(realRow, true)
    })
  })
}

const getSelection = () => {
  return selectedRows.value
}

defineExpose({ getSelection })
</script>
