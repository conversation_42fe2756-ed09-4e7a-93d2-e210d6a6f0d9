<script lang="ts" setup>
import { request } from '@/utils/http/request'
import { onMounted, ref, watch } from 'vue'
import { v4 as uuid } from 'uuid'

const deviceId = defineModel('deviceId', {
  type: String,
  required: true
})

const props = defineProps({
  height: {
    type: Number,
    default: 32
  },
  width: {
    type: Number,
    default: 136
  }
})

const captchaImg = ref()

onMounted(() => {
  getCaptcha()
})

watch(deviceId, () => {
  getCaptcha()
})

const refreshCaptcha = () => {
  deviceId.value = uuid()
}

const getCaptcha = () => {
  request(
    'GET',
    '/verify/image',
    {
      width: 136,
      height: 38
    },
    {
      headers: {
        imageDeviceId: deviceId.value
      }
    }
  )
    .then(response => response.blob())
    .then(
      data =>
        (captchaImg.value = (window.URL || window.webkitURL).createObjectURL(
          data
        ))
    )
}
</script>

<template>
  <img
    v-if="captchaImg"
    :src="captchaImg"
    :style="{ width: `${props.width}px`, height: `${props.height}px` }"
    alt="图片验证码"
    @click="refreshCaptcha"
  />
  <span
    v-else
    :style="{ width: `${props.width}px`, height: `${props.height}px` }"
    class="inline-block"
  >
    loading
  </span>
</template>
