<script setup lang="ts">
import { ref, watch } from 'vue'
import { saveFile } from '@/utils/http/request'
import { ElMessage } from 'element-plus'
import { api as viewerApi } from 'v-viewer'
import { saveAs } from 'file-saver'
import { Plus } from '@element-plus/icons-vue'
import URLS from '@/api/URLS'

interface FileRes {
  businessType: string
  fileType: string
  id: string
  mimeType: string
  originalName: string
  path: string
}

const props = defineProps({
  type: {
    required: false,
    default: 'normal'
  },
  size: {
    required: false,
    default: 1
  },
  fileDataList: {
    type: Array,
    required: false,
    default: () => []
  },
  classfiy: {
    required: false,
    default: 'sever'
  },
  delIds: {
    required: false,
    default: null
  },
  disabled: {
    type: Boolean,
    default: false
  },
  listType: {
    type: String,
    default: 'text'
  },
  multiple: {
    type: Boolean,
    default: false
  },
  mask: {
    type: Boolean,
    default: false
  },
  maskKey: {
    type: String,
    default: null
  },
  businessId: {
    required: false,
    type: String,
    default: null
  },
  limit: {
    type: Number,
    default: 10
  }
})

const fileList = ref([])

watch(
  () => props.fileDataList,
  () => {
    if (!props.fileDataList) {
      fileList.value = []
      return
    }
    const prefix = import.meta.env.VITE_DEV_PROXY_PREFIX
    const apiPrefix = import.meta.env.VITE_API_PREFIX || ''
    fileList.value = props.fileDataList.map((item: FileRes) => {
      let url
      if (prefix) {
        url = `${prefix}${apiPrefix}/file-manager/file/minio/${item.id}`
      } else {
        url = `${apiPrefix}/file-manager/file/minio/${item.id}`
      }
      return {
        url: url,
        name: item.originalName,
        fileId: item.id
      }
    })
    modelValue.value = props.fileDataList.map((item: FileRes) => item.id)
  }
)

const modelValue = defineModel('value', {
  type: Array,
  default: () => []
})

const handleChange = (file, fileList) => {
  console.log('handleChange', file, fileList)
  modelValue.value = fileList.map(item => item.fileId)
}

const convertToBase64 = file => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}
const handleUpload = (option: any) => {
  console.log('handleUpload', option)
  const { file, onSuccess, onError } = option
  saveFile(`/file-manager/upload/minio/${props.type}`, file).then(res => {
    if (res && res.success) {
      onSuccess(res, file, fileList.value)
    } else {
      onError(res, file)
    }
  })
  return new Promise((resolve, reject) => {
    resolve(true)
  })
}
const handleSuccess = (res, file, fileList) => {
  console.log('handleSuccess', res, file, fileList)
  if (!res || !res.success || !res.data) {
    return
  }
  const data = res.data[0]
  if (!data) {
    return
  }
  file.fileId = data.id
  file.name = data.originalName
  const prefix = import.meta.env.VITE_DEV_PROXY_PREFIX
  const apiPrefix = import.meta.env.VITE_API_PREFIX || ''
  if (prefix) {
    file.url = `${prefix}${apiPrefix}/file-manager/file/minio/${data.id}`
  } else {
    file.url = apiPrefix + `/file-manager/file/minio/${data.id}`
  }
}
const handlePictureRemove = (file, fileList) => {
  console.log('handlePictureRemove', file, fileList)
  modelValue.value = fileList.map(item => item.fileId)
  // fileList.value = fileList
}
const onPreview = file => {
  let index = 0
  const imgUrls = []
  const prefix = import.meta.env.VITE_DEV_PROXY_PREFIX
  const apiPrefix = import.meta.env.VITE_API_PREFIX || ''
  fileList.value.forEach((a, i) => {
    if (a.url.indexOf(file.url) >= 0) {
      index = i
    }
    if (prefix) {
      imgUrls.push(`${prefix}${apiPrefix}/file-manager/file/minio/${a.fileId}`)
    } else {
      imgUrls.push(apiPrefix + `/file-manager/file/minio/${a.fileId}`)
    }
  })
  viewerApi({
    options: {
      toolbar: {
        flipHorizontal: true,
        flipVertical: true,
        next: true,
        oneToOne: true,
        play: true,
        prev: true,
        reset: true,
        rotateLeft: true,
        rotateRight: true,
        zoomIn: true,
        zoomOut: true
      },
      initialViewIndex: index
    },
    images: imgUrls
  })
}
const handlePictureExceed = (files, fileList) => {
  ElMessage({
    message: `当前限制最多选择 ${props.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
      files.length + fileList.length
    } 个文件`,
    type: 'warning'
  })
}

const handleFilePreview = file => {
  console.log('handleFilePreview', file)
  const url = URLS.imgFileUrl(file.fileId)
  saveAs(url, file.name)
}
</script>
<template>
  <el-upload
    v-if="listType === 'text'"
    v-model:file-list="fileList"
    :http-request="handleUpload"
    :on-change="handleChange"
    :on-success="handleSuccess"
    :on-preview="handleFilePreview"
    :on-remove="handlePictureRemove"
    :multiple="multiple || true"
    :on-exceed="handlePictureExceed"
    :disabled="disabled"
    :limit="limit"
    style="width: 100%"
  >
    <el-button type="primary">上传</el-button>
  </el-upload>
  <el-upload
    v-else-if="listType === 'slot'"
    v-model:file-list="fileList"
    :http-request="handleUpload"
    :on-change="handleChange"
    :on-success="handleSuccess"
    :on-preview="handleFilePreview"
    :on-remove="handlePictureRemove"
    :multiple="multiple || true"
    :on-exceed="handlePictureExceed"
    :disabled="disabled"
    style="width: 100%"
    :limit="limit"
  >
    <slot />
  </el-upload>

  <el-upload
    v-else
    v-model:file-list="fileList"
    list-type="picture-card"
    :http-request="handleUpload"
    :on-change="handleChange"
    :on-success="handleSuccess"
    :on-preview="onPreview"
    :on-remove="handlePictureRemove"
    :multiple="multiple || true"
    :on-exceed="handlePictureExceed"
    :disabled="disabled"
    :limit="limit"
  >
    <el-icon><Plus /></el-icon>
  </el-upload>
</template>
