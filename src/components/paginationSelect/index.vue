<template>
  <div class="pagination-select relative inline-block w-full">
    <el-select
      v-model="value"
      :placeholder="placeholder"
      :disabled="disabled"
      :size="size"
      class="w-full"
      style="width: 100%"
      :multiple="multiple"
      :filterable="filterable"
      remote
      :remote-method="remoteMethod"
      @visible-change="handleVisibleChange"
    >
      <el-option
        v-for="item in currentPageData"
        :key="item[valueKey]"
        :label="item[labelKey]"
        :value="item[valueKey]"
      />
      <div
        v-if="total > 0"
        class="pagination-container flex justify-center mt-2 px-2"
      >
        <el-pagination
          background
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="pageSizes"
          :total="total"
          layout="prev, pager, next, sizes"
          size="small"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-select>
  </div>
</template>

<script setup lang="ts">
/*
 * 
 *   <el-form-item label="所属行业">
        <pagination-select
          v-model="queryParams.industry"
          label-key="name"
          value-key="id"
          placeholder="请选择所属行业"
          :url="'/build/company/page'"
        />
      </el-form-item>
 */

import { ref, watch, onMounted, defineModel } from 'vue'
import { ElSelect, ElOption, ElPagination } from 'element-plus'
import { get } from '@/utils/http/request'
const props = defineProps({
  url: {
    type: String,
    default: ''
  },
  // 标签字段名
  labelKey: {
    type: String,
    default: 'label'
  },
  // 值字段名
  valueKey: {
    type: String,
    default: 'value'
  },
  // 占位符
  placeholder: {
    type: String,
    default: '请选择'
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 尺寸
  size: {
    type: String as () => 'large' | 'default' | 'small',
    default: 'default'
  },
  multiple: {
    type: Boolean,
    default: false
  },
  searchKey: {
    type: String,
    default: ''
  },
  filterable: {
    type: Boolean,
    default: false
  }
})
const value = defineModel({})

// 当前页码
const currentPage = ref(1)
const pageSize = ref(10)
const pageSizes = ref([5, 10, 20, 50])
// 总条数
const total = ref(0)
// 当前页数据
const currentPageData = ref([])
const searchKeyword = ref('')

const loadData = async (keyword = '') => {
  let url =
    props.url + '?pageNum=' + currentPage.value + '&pageSize=' + pageSize.value
  if (keyword) {
    url += '&' + props.searchKey + '=' + encodeURIComponent(keyword)
  }
  const res = await get(url)
  if (res.success) {
    currentPageData.value = res.data
    total.value = res.totalRecords
  }
}

const remoteMethod = (queryString: string) => {
  searchKeyword.value = queryString
  currentPage.value = 1
  loadData(queryString)
}

onMounted(() => {
  loadData()
})

watch(currentPage, val => {
  loadData(searchKeyword.value)
})

// 处理页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  currentPage.value = 1
  pageSize.value = size
  loadData(searchKeyword.value)
}

// 处理下拉框显示状态变化
const handleVisibleChange = (visible: boolean) => {
  if (visible) {
    // 下拉框显示时重置到第一页
    currentPage.value = 1
    searchKeyword.value = ''
    loadData()
  }
}

defineExpose({
  currentPage
})
</script>

<style scoped lang="scss">
.pagination-select {
  .el-select-dropdown {
    max-height: none;
  }

  .pagination-container {
    padding-top: 8px;
    margin-top: 8px;
    border-top: 1px solid #e4e7ed;
  }
}
</style>
