<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import URLS from '@api/URLS'
import { get } from '@/utils/http/request'

const value = defineModel({})
const props = defineProps({
  multiple: {
    type: Boolean,
    default: false
  },
  companyId: {
    type: String,
    required: false
  },
  buildId: {
    type: String,
    required: false
  }
})
const emits = defineEmits(['change'])
const handleChange = e => {
  console.log(e)
  emits('change', e)
}
const floorList = ref([])
const init = () => {
  get(URLS.BUILD + '/build/floor/list', {
    buildId: props.buildId
  }).then(res => {
    if (res && res.success) {
      floorList.value = res.data
    }
  })
}
onMounted(() => init())
watch(
  () => props.buildId,
  () => {
    init()
  }
)
</script>

<template>
  <el-select
    v-model="value"
    :multiple="multiple"
    clearable
    filterable
    placeholder="选择建筑楼层"
    @change="handleChange"
  >
    <el-option
      v-for="item in floorList"
      :key="item.id"
      :value="item.id"
      :label="item.num"
    />
  </el-select>
</template>
