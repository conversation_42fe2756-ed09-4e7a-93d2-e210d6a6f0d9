<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { get } from '@/utils/http/request'
import URLS from '@api/URLS'

const value = defineModel({})

const loading = ref(false)

const companyList = ref([])

const companyName = ref('')

const handleFilter = (v: string) => {
  init(v)
}

defineProps({
  multiple: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['change'])
const handleChange = e => {
  emits('change', e)
  companyName.value = companyList.value.find(item => item.id === e)?.name || ''
}

const init = (params?: string) => {
  loading.value = true
  get(URLS.BUILD + '/company/page', {
    name: params
  }).then(res => {
    loading.value = false
    if (res && res.success) {
      companyList.value = res.data
    }
  })
}
onMounted(() => init())
defineExpose({
  companyName
})
</script>

<template>
  <el-select
    v-model="value"
    style="width: 200px"
    filterable
    remote
    :remote-method="handleFilter"
    :multiple="multiple"
    :loading="loading"
    placeholder="选择建筑管理单位"
    @change="handleChange"
  >
    <el-option
      v-for="item in companyList"
      :key="item.id"
      :label="item.name"
      :value="item.id"
    />
  </el-select>
</template>
