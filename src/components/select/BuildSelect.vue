<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import URLS from '@api/URLS'
import { get } from '@/utils/http/request'

const value = defineModel({})
const props = defineProps({
  multiple: {
    type: Boolean,
    default: false
  },
  companyId: {
    type: String,
    required: false
  }
})
const emits = defineEmits(['change'])
const handleChange = e => {
  console.log(e)
  emits('change', e)
}
const buildList = ref([])
const init = () => {
  get(URLS.BUILD + '/build', {
    companyId: props.companyId,
    pageNum: 1,
    pageSize: 1000
  }).then(res => {
    if (res && res.success) {
      buildList.value = res.data
    }
  })
}
onMounted(() => init())
watch(
  () => props.companyId,
  () => {
    init()
  }
)
</script>

<template>
  <el-select
    v-model="value"
    :multiple="multiple"
    clearable
    filterable
    style="width: 200px"
    placeholder="选择建筑"
    @change="handleChange"
  >
    <el-option
      v-for="item in buildList"
      :key="item.id"
      :value="item.id"
      :label="item.name || ''"
    />
  </el-select>
</template>
