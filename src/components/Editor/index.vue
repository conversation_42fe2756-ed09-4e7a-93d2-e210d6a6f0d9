<template>
  <div style="border: 1px solid #ccc">
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="'default'"
    />
    <Editor
      v-model="value"
      style="height: 500px; overflow-y: hidden"
      :defaultConfig="editorConfig"
      :mode="'default'"
      @onCreated="handleCreated"
    />
  </div>
</template>
<script lang="ts" setup>
import '@wangeditor/editor/dist/css/style.css' // 引入 css

import { onBeforeUnmount, ref, shallowRef, onMounted, watch } from 'vue'
import { message } from '@/utils/message'
import { getToken } from '@/utils/auth'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { saveFile } from '@/utils/http/request'
import URLS from '@/api/URLS'
import { ElMessage } from 'element-plus'
// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

const value = defineModel('modelValue', {
  type: String,
  default: ''
})

// 模拟 ajax 异步获取内容
onMounted(() => {})

const toolbarConfig = {}
const editorConfig = {
  placeholder: '请输入内容...',
  MENU_CONF: {}
}
editorConfig.MENU_CONF['uploadImage'] = {
  // 上传图片的配置
  fieldName: 'file', //参数类型要写成这样要不然报错400
  customUpload(file, insertFn) {
    console.log(file, 'customUpload')
    saveFile(`/file-manager/upload/minio/article`, file).then(res => {
      if (res && res.success) {
        console.log(res, 'adsds')
        const id = res.data[0].id
        const url = URLS.imgFileUrl(id)
        console.log(url, 'url')
        insertFn(url)
      } else {
        ElMessage({
          message: '上传失败',
          type: 'warning'
        })
      }
    })
  }
}
// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})

const handleCreated = editor => {
  editorRef.value = editor // 记录 editor 实例，重要！
}
</script>
