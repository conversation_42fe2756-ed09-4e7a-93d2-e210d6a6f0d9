import { type Ref, ref } from 'vue'
import { del, get } from '@/utils/http/request'
import { ElMessage } from 'element-plus'
interface TableUrlBean {
  list: string
  normal: string
  rowKey?: string
}
interface TableCompositionBean {
  urls: TableUrlBean
}
export default function TableComposition(params: TableCompositionBean) {
  const defaultQueryParams: any = {}
  const pageSizeList = [10, 20, 30, 50]
  const tableData = ref([])
  const loading = ref(false)

  const queryParams: Ref<any> = ref({})

  const formRef = ref()
  const handleAdd = () => {
    formRef.value.handleAdd()
  }
  const handleEdit = record => {
    formRef.value.handleEdit(record)
  }
  const handleLook = record => {
    formRef.value.handleLook(record)
  }

  const handleDelete = record => {
    del(`${params.urls.normal}/${record[params.urls.rowKey || 'id']}`).then(
      res => {
        if (res && res.success) {
          ElMessage({
            message: '删除成功',
            type: 'success'
          })
          loadTableData(1)
        } else {
          ElMessage({
            message: res?.msg || '删除失败',
            type: 'error'
          })
        }
      }
    )
  }

  const pageInfo = ref({
    pageNum: 1,
    pageSize: 10,
    totalPages: 0,
    totalRecords: 0
  })

  const loadTableData = (page: number) => {
    loading.value = true
    get(params.urls.list, {
      ...queryParams.value,
      ...defaultQueryParams,
      pageNum: page || 1,
      pageSize: pageInfo.value.pageSize
    }).then(res => {
      console.log('获取到数据:', res)
      loading.value = false
      if (res && res.success) {
        tableData.value = res.data
        pageInfo.value.totalPages = res.totalPages
        pageInfo.value.totalRecords = res.totalRecords
        pageInfo.value.pageNum = res.pageNum
      } else {
        ElMessage({
          message: res.msg,
          type: 'error'
        })
      }
    })
  }

  const resetTable = () => {
    queryParams.value = {}
    loadTableData(1)
  }

  const handlePaginationSizeChange = (pageSize: number) => {
    pageInfo.value.pageSize = pageSize
    loadTableData(1)
  }
  const handlePaginationPageChange = (page: number) => {
    pageInfo.value.pageNum = page
    loadTableData(page)
  }

  return {
    tableData,
    pageInfo,
    queryParams,
    formRef,
    loading,
    pageSizeList,
    defaultQueryParams,
    loadTableData,
    resetTable,
    handlePaginationSizeChange,
    handlePaginationPageChange,
    handleAdd,
    handleDelete,
    handleEdit,
    handleLook
  }
}
