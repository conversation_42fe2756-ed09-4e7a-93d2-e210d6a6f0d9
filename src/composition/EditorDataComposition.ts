import { get } from '@/utils/http/request'
import URLS from '@api/URLS'
import { ElMessage } from 'element-plus'
import type { Ref } from 'vue'
import { ref } from 'vue'

export default function EditorDataComposition(
  initBackgroundNode: Function,
  fromJson: Function,
  dataId?: Ref<string>
) {
  let businessId
  let backGroupImageType
  const bgFlag = ref(true)
  const loadConfig = async (
    _businessId: string,
    businessType: string,
    _backGroupImageType: string,
    call?: Function
  ) => {
    businessId = _businessId
    backGroupImageType = _backGroupImageType
    await get(URLS.BUILD + '/build/config/detail', {
      businessId: businessId,
      type: businessType
    }).then(res => {
      console.log('获取到数据:', res)
      if (res && res.success && res.data) {
        if (dataId) {
          dataId.value = res.data.id
        }
        const config = res.data.config
          ? JSON.parse(res.data.config)
          : {
              cells: []
            }
        getBackgroundImage(config.cells, call)
      } else {
        getBackgroundImage([], call)
      }
    })
  }
  const getBackgroundImage = (cells: [], call: Function) => {
    get(URLS.MANAGER_FILE + '/fileResource/record/list', {
      businessType: backGroupImageType,
      businessId: businessId
    }).then(res => {
      console.log('res', res)
      if (res && res.success) {
        bgFlag.value = true
        if (!res.data || res.data.length === 0) {
          bgFlag.value = false
          ElMessage({
            message: '请上传平面图',
            type: 'error'
          })
          fromJson([])
          call && call()
          return
        }
        if (cells.length === 0) {
          fromJson(cells)
          initBackgroundNode(res.data[0].id)
          call && call()
          return
        }
        console.log('cells', cells)
        cells.forEach((item: any) => {
          if (item.data.type === 'background') {
            item.data.fileId = res.data[0].id
            item.imageUrl = URLS.imgFileUrl(item.data.fileId)
          }
        })
        fromJson(cells)
        call && call()
      }
    })
  }
  return {
    loadConfig,
    bgFlag
  }
}
