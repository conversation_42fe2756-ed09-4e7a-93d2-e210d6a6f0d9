// hooks/useAmap.js
import AMapLoader from '@amap/amap-jsapi-loader'

export default function useAmap(containerId, options: any = {}) {
  const key = options.key || '291cba32216a5c9f068c7368cc7819d6'
  const initMap = async () => {
    try {
      const AMap = await AMapLoader.load({
        key,
        version: '2.0',
        plugins: ['AMap.Marker']
      })

      const map = new AMap.Map(containerId, {
        zoom: options.zoom || 11,
        center: options.center || [116.397428, 39.90923],
        viewMode: '2D',
        crs: 'GCJ02', // 显式指定GCJ-02坐标系
        ...options.mapOptions
      })
      return { AMap, map }
    } catch (error) {
      console.error('高德地图加载失败:', error)
      throw error
    }
  }
  const getLatInfo = lnglat => {
    if (!lnglat || !lnglat.lng || !lnglat.lat) {
      console.error('无效的经纬度信息:', lnglat)
      return
    }
    return new Promise((resolve, reject) => {
      const url = `https://restapi.amap.com/v3/geocode/regeo?key=${key}&location=${lnglat.lng},${lnglat.lat}`
      fetch(url)
        .then(res => res.json())
        .then(result => {
          console.log('逆地理编码结果:', result)
          // 处理 result.regeocode.addressComponent
          resolve(result)
        })
        .catch(error => {
          console.error('逆地理编码请求失败:', error)
          reject(error)
        })
    })
  }

  // 使用高德Web服务API
  const getDistrictByAdcode = async adcode => {
    if (!adcode) {
      return
    }
    return new Promise<void>(async (resolve, reject) => {
      const response = await fetch(
        `https://restapi.amap.com/v3/config/district?key=${key}&keywords=${adcode}&subdistrict=0&extensions=all`
      )
      const data = await response.json()

      if (data.status === '1' && data.districts.length > 0) {
        const district = data.districts[0]
        resolve(district)
      } else {
        reject(data)
      }
    })
  }

  // 使用高德Web服务API
  const getDistrictByAdress = async address => {
    if (!address) {
      return
    }
    return new Promise<void>(async (resolve, reject) => {
      const response = await fetch(
        `https://restapi.amap.com/v3/geocode/geo?address=${address}&key=${key}`
      )
      const data = await response.json()

      if (data.status === '1' && data.geocodes.length > 0) {
        const district = data.geocodes[0]
        resolve(district)
      } else {
        reject(data)
      }
    })
  }

  return { initMap, getLatInfo, getDistrictByAdcode, getDistrictByAdress }
}
