import { Graph } from '@antv/x6'
import { Transform } from '@antv/x6-plugin-transform'
import { Selection } from '@antv/x6-plugin-selection'
import { Snapline } from '@antv/x6-plugin-snapline'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Clipboard } from '@antv/x6-plugin-clipboard'
import { History } from '@antv/x6-plugin-history'
import { ref } from 'vue'
import type { StencilNode } from '@/types/editor/EditorTypes'
import { Stencil } from '@antv/x6-plugin-stencil'
import URLS from '@api/URLS'
export default function AntvX6Composition(
  handleNodeClick?: Function,
  readonly?: boolean,
  deviceTypeListArr?: any
) {
  let graph: any
  let stencil
  /**
   * 初始化画布
   */
  const initGraph = async () => {
    const options = {
      container: document.getElementById('graph-container')!,
      grid: !readonly,
      panning: readonly,
      background: {
        color: '#ffffff'
      },
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        modifiers: 'ctrl',
        minScale: 0.5,
        maxScale: 3
      },
      highlighting: {
        magnetAdsorbed: {
          name: 'stroke',
          args: {
            attrs: {
              fill: '#5F95FF',
              stroke: '#5F95FF'
            }
          }
        }
      },
      interacting: {
        nodeMovable: !readonly // 禁止节点移动
      }
    }
    graph = new Graph(options)
    // #region 使用插件
    if (!readonly) {
      graph
        .use(
          new Transform({
            resizing: true,
            rotating: true
          })
        )
        .use(
          new Selection({
            rubberband: true,
            showNodeSelectionBox: true
          })
        )
        .use(new Snapline())
        .use(new Keyboard())
        .use(new Clipboard())
        .use(new History())
    }

    // #endregion
  }

  const currentNode = ref()

  /**
   * 初始化事件监听
   */
  const initEventListener = () => {
    graph.on('node:added', ({ node }) => {
      console.log('node:added', node)
    })
    graph.on('node:click', ({ node }) => {
      console.log('node:click', node, deviceTypeListArr?.value)
      currentNode.value = node
      if (handleNodeClick) {
        handleNodeClick(node.data)
      }
    })
    graph.on('node:changed', ({ node }) => {
      console.log('node:changed', node, deviceTypeListArr?.value)
      node.attr('image', {
        width: node.getSize().width,
        height: node.getSize().height
      })
    })
  }
  /**
   * 初始化stencil
   */
  const initStencil = () => {
    stencil = new Stencil({
      title: '设备类型',
      target: graph,
      stencilGraphWidth: 0,
      stencilGraphHeight: 0,
      collapsable: false,
      groups: [
        {
          title: '系统设计图',
          name: 'group2',
          graphHeight: 0,
          layoutOptions: {
            rowHeight: 80
          }
        }
      ],
      layoutOptions: {
        columns: 2,
        columnWidth: 90,
        rowHeight: 55
      }
    })
    const container = stencil.container
    Object.assign(container.style, {
      width: '100%',
      height: '100%',
      background: '#ffffff'
    })
    document.getElementById('stencil')!.appendChild(container)
  }

  /**
   * 绑定快捷键
   */
  const bindKey = () => {
    // #region 快捷键与事件
    graph.bindKey(['meta+c', 'ctrl+c'], () => {
      const cells = graph.getSelectedCells()
      if (cells.length) {
        graph.copy(cells)
      }
      return false
    })
    graph.bindKey(['meta+x', 'ctrl+x'], () => {
      const cells = graph.getSelectedCells()
      if (cells.length) {
        graph.cut(cells)
      }
      return false
    })
    graph.bindKey(['meta+v', 'ctrl+v'], () => {
      if (!graph.isClipboardEmpty()) {
        const cells = graph.paste({ offset: 32 })
        graph.cleanSelection()
        graph.select(cells)
      }
      return false
    })

    // undo redo
    graph.bindKey(['meta+z', 'ctrl+z'], () => {
      if (graph.canUndo()) {
        graph.undo()
      }
      return false
    })
    graph.bindKey(['meta+shift+z', 'ctrl+shift+z'], () => {
      if (graph.canRedo()) {
        graph.redo()
      }
      return false
    })

    // select all
    graph.bindKey(['meta+a', 'ctrl+a'], () => {
      const nodes = graph.getNodes()
      if (nodes) {
        graph.select(nodes)
      }
    })

    // delete
    graph.bindKey('backspace', () => {
      const cells = graph.getSelectedCells()
      if (cells.length) {
        graph.removeCells(cells)
      }
    })

    graph.bindKey('delete', () => {
      const cells = graph.getSelectedCells()
      if (cells.length) {
        graph.removeCells(cells)
      }
    })

    // zoom
    graph.bindKey(['ctrl+1', 'meta+1'], () => {
      const zoom = graph.zoom()
      if (zoom < 1.5) {
        graph.zoom(0.1)
      }
    })
    graph.bindKey(['ctrl+2', 'meta+2'], () => {
      const zoom = graph.zoom()
      if (zoom > 0.5) {
        graph.zoom(-0.1)
      }
    })
  }

  /**
   * 注册自定义节点
   */
  const registerNode = () => {
    Graph.registerNode(
      'custom-image',
      {
        inherit: 'rect',
        width: 52,
        height: 70,
        markup: [
          {
            tagName: 'rect',
            selector: 'body'
          },
          {
            tagName: 'image'
          },
          {
            tagName: 'text',
            selector: 'label'
          }
        ],
        attrs: {
          body: {
            stroke: '#5491c800',
            fill: '#5491c800',
            color: 'red'
          },
          image: {
            width: 52,
            refY: 16,
            textAnchor: 'middle'
          },
          label: {
            refY: 0,
            textAnchor: 'middle',
            textVerticalAnchor: 'top',
            fontSize: 12,
            fill: '#000000'
          }
        }
      },
      true
    )
  }

  /**
   * 初始化可拖拽添加的节点
   * @param nodeList
   * @param dataFun data参数构建函数
   */
  const initStencilNode = (nodeList: StencilNode[]) => {
    const imageNodes = nodeList.map(item =>
      graph.createNode({
        shape: 'custom-image',
        label: item.label,
        attrs: {
          image: {
            'xlink:href': item.image
          }
        },
        data: { ...item },
        props: item.attrs
      })
    )
    stencil.load(imageNodes, 'group2')
  }

  /**
   * 初始化背景图节点
   * @param fileId
   */
  const initBackgroundNode = (fileId: string) => {
    graph.addNode({
      shape: 'image',
      x: 300,
      y: 100,
      width: 800,
      height: 600,
      imageUrl: URLS.imgFileUrl(fileId),
      attrs: {
        image: {
          // preserveAspectRatio: 'none' // 图片铺满
        }
      },
      draggable: false,
      movable: false,
      data: {
        fileId: fileId,
        type: 'background'
      },
      zIndex: -1
    })
  }
  /**
   * 初始化
   * @param call
   */
  const init = async (call: Function) => {
    await initGraph()
    initEventListener()
    if (!readonly) {
      initStencil()
    }
    bindKey()
    registerNode()
    call(graph)
  }
  /**
   * 获取json
   */
  const getJson = () => {
    return graph.toJSON()
  }
  /**
   * 导入json
   * @param json
   */
  const fromJson = (json: any) => {
    graph.fromJSON(json)
  }
  /**
   * 获取所有node节点
   * @param json
   */
  const getAllNodes = () => {
    return graph.getNodes()
  }
  /**
   * 设置设备节点标签
   * @param text
   */
  const setLabel = (text: string) => {
    if (!currentNode.value) {
      return
    }
    currentNode.value.prop('attrs/label/text', text)
  }
  return {
    init,
    getJson,
    fromJson,
    initStencilNode,
    initBackgroundNode,
    setLabel,
    currentNode,
    getAllNodes
  }
}
