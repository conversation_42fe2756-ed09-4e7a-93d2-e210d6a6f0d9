import { type Ref, ref } from 'vue'
import { get, post, put } from '@/utils/http/request'
import { ElMessage } from 'element-plus'

interface UrlBean {
  normal: string
  // 详情请求路径
  detail?: string
  // 保存请求路径
  save?: string
  // 修改请求路径
  update?: string
  // 数据主键字段
  rowKey?: string
}
interface FormCompositionBean {
  // 请求路径集合
  urls: UrlBean
  // 编辑页面时数据处理方法
  dealRecord?: Function
  // 提交参数处理方法
  dealSubmitParams?: Function
  // 添加时数据初始化方法
  addInit?: Function
  emits?: any
  validate?: any
  // 表单验证规则
}
export default function FormComposition(params: FormCompositionBean) {
  // 可见状态
  const visible = ref(false)
  // 标题
  const title = ref('')
  // 表单数据
  const formData: Ref<any> = ref({})
  // 表单引用
  const formRef = ref()
  // 加载中
  const loading = ref(false)
  let isEdit = false
  // 是否显示按钮
  const showBtn = ref(true)
  /**
   * 新增处理方法
   */
  const handleAdd = () => {
    showBtn.value = true
    visible.value = true
    title.value = '新增'
    formData.value = {}
    if (params.addInit) {
      params.addInit()
    }
    isEdit = false
  }

  /**
   * 编辑处理方法
   * @param record
   */
  const handleEdit = record => {
    showBtn.value = true
    visible.value = true
    title.value = '编辑'
    if (params.addInit) {
      params.addInit()
    }
    // 如果配置了详情接口，则请求详情接口作为表单数据
    if (params.urls.detail) {
      loading.value = true
      get(`${params.urls.detail}/${record[params.urls.rowKey ?? 'id']}`)
        .then(res => {
          loading.value = false
          if (res && res.success) {
            formData.value = res.data
            if (params.dealRecord) {
              params.dealRecord(res.data)
            }
          } else {
            formData.value = record
            if (params.dealRecord) {
              params.dealRecord(record)
            }
          }
        })
        .catch(e => {
          loading.value = false
          console.error('请求出现异常', e)
          formData.value = record
          if (params.dealRecord) {
            params.dealRecord(record)
          }
        })
    } else {
      formData.value = record
      if (params.dealRecord) {
        params.dealRecord(record)
      }
    }
    isEdit = true
  }
  const handleLook = record => {
    handleEdit(record)
    showBtn.value = false
  }
  /**
   * 提交表单
   */
  const handleSubmit = () => {
    console.log('handleSubmit', formData.value)
    let valid = true
    if (params.validate) {
      valid = params.validate(formData.value)
    }
    console.log('valid', valid)
    valid &&
      formRef.value.validate(async valid => {
        if (valid) {
          loading.value = true
          try {
            let data: any = { ...formData.value }
            if (params.dealSubmitParams) {
              const res = await params.dealSubmitParams(data)
              if (res) {
                data = { ...data, ...res }
              }
            }
            let action
            if (isEdit) {
              action = put(
                params.urls.update ??
                  params.urls.normal +
                    '/' +
                    formData.value[params.urls.rowKey || 'id'],
                data
              )
            } else {
              action = post(params.urls.save ?? params.urls.normal, data)
            }
            action.then(res => {
              if (res && res.success) {
                loading.value = false
                visible.value = false
                ElMessage({
                  message: res.msg,
                  type: 'success'
                })
                if (params.emits) {
                  params.emits('ok')
                }
              } else {
                loading.value = false
                ElMessage({
                  message: res.msg,
                  type: 'error'
                })
              }
            })
          } catch (e) {
            console.error('出现异常', e)
            loading.value = false
          }
        }
      })
  }
  /**
   * 关闭弹窗
   */
  const handleClose = () => {
    visible.value = false
    formRef.value.resetFields()
    formData.value = {}
    if (params.addInit) {
      params.addInit()
    }
  }
  return {
    visible,
    title,
    formData,
    formRef,
    loading,
    handleAdd,
    handleEdit,
    handleSubmit,
    handleClose,
    showBtn,
    handleLook
  }
}
