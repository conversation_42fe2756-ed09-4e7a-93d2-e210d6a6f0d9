.wave {
  position: fixed;
  height: 100%;
  width: 100%;
  left: 0;
  bottom: 0;
  z-index: -1;
}

.login-container {
  width: 100vw;
  height: 100vh;
  max-width: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 18rem;
  padding: 0 2rem;
  position: relative;
}

.img {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.img img {
  width: 500px;
}

.login-box {
  display: flex;
  align-items: center;
  text-align: center;
  overflow: hidden;
  justify-content: center;
}

.login-form {
  width: 392px;
  max-width: 392px;
  min-width: 392px;
  height: 520px;
  max-height: 520px;
  min-height: 520px;
  overflow: hidden;

  background: linear-gradient(180deg, #ffffff 6%, #ecf3fa 100%);
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.08);
  border-radius: 8px;

  padding: 80px 33px 49px 39px;
}

.avatar {
  width: 350px;
  height: 80px;
}

.login-title {
  width: 100%;
}

.login-title-bottom {
  margin-bottom: 40px;
}

.login-form h2 {
  text-transform: uppercase;
  margin: 15px 0;
  color: #999;
  font:
    bold 200% Consolas,
    Monaco,
    monospace;
}

@media screen and (max-width: 1180px) {
  .login-container {
    grid-gap: 9rem;
  }

  .login-form {
    width: 290px;
  }

  .login-form h2 {
    font-size: 2.4rem;
    margin: 8px 0;
  }

  .img img {
    width: 360px;
  }

  .avatar {
    width: 280px;
    height: 80px;
  }
}

@media screen and (max-width: 968px) {
  .wave {
    display: none;
  }

  .img {
    display: none;
  }

  .login-container {
    grid-template-columns: 1fr;
  }

  .login-box {
    justify-content: center;
  }
}
