export function getNow(): string {
  const now = new Date()
  const yyyy = now.getFullYear()
  const mm = String(now.getMonth() + 1).padStart(2, '0')
  const dd = String(now.getDate()).padStart(2, '0')
  return `${yyyy}-${mm}-${dd}`
}

/**
 * 从日期时间字符串中安全提取时间部分 (HH:MM 格式)
 * @param dateTimeStr 日期时间字符串，格式如 "2024-01-01 14:30:00"
 * @returns 时间字符串 "HH:MM" 或空字符串
 */
export function getTimeFromDateTime(
  dateTimeStr: string | null | undefined
): string {
  if (!dateTimeStr || typeof dateTimeStr !== 'string') {
    return ''
  }
  const parts = dateTimeStr.split(' ')
  return parts.length > 1 ? parts[1].substring(0, 5) : ''
}

/**
 * 从日期时间字符串中安全提取日期部分 (YYYY-MM-DD 格式)
 * @param dateTimeStr 日期时间字符串，格式如 "2024-01-01 14:30:00"
 * @returns 日期字符串 "YYYY-MM-DD" 或空字符串
 */
export function getDateFromDateTime(
  dateTimeStr: string | null | undefined
): string {
  if (!dateTimeStr || typeof dateTimeStr !== 'string') {
    return ''
  }
  const parts = dateTimeStr.split(' ')
  return parts.length > 0 ? parts[0] : ''
}

/**
 * 格式化日期对象为指定格式
 * @param date Date对象
 * @param format 格式字符串，支持: YYYY-MM-DD, YYYY/MM/DD, MM-DD, HH:mm, YYYY-MM-DD HH:mm:ss
 * @returns 格式化后的日期字符串
 */
export function formatDate(
  date: Date | string | null | undefined,
  format: string = 'YYYY-MM-DD'
): string {
  if (!date) return ''

  const dateObj = typeof date === 'string' ? new Date(date) : date
  if (isNaN(dateObj.getTime())) return ''

  const yyyy = dateObj.getFullYear()
  const mm = String(dateObj.getMonth() + 1).padStart(2, '0')
  const dd = String(dateObj.getDate()).padStart(2, '0')
  const hh = String(dateObj.getHours()).padStart(2, '0')
  const min = String(dateObj.getMinutes()).padStart(2, '0')
  const ss = String(dateObj.getSeconds()).padStart(2, '0')

  switch (format) {
    case 'YYYY-MM-DD':
      return `${yyyy}-${mm}-${dd}`
    case 'YYYY/MM/DD':
      return `${yyyy}/${mm}/${dd}`
    case 'MM-DD':
      return `${mm}-${dd}`
    case 'HH:mm':
      return `${hh}:${min}`
    case 'HH:mm:ss':
      return `${hh}:${min}:${ss}`
    case 'YYYY-MM-DD HH:mm':
      return `${yyyy}-${mm}-${dd} ${hh}:${min}`
    case 'YYYY-MM-DD HH:mm:ss':
      return `${yyyy}-${mm}-${dd} ${hh}:${min}:${ss}`
    default:
      return `${yyyy}-${mm}-${dd}`
  }
}

/**
 * 获取当前时间的指定格式
 * @param format 格式字符串
 * @returns 格式化后的当前时间字符串
 */
export function getNowFormatted(format: string = 'YYYY-MM-DD'): string {
  return formatDate(new Date(), format)
}

/**
 * 判断日期字符串是否有效
 * @param dateStr 日期字符串
 * @returns 是否为有效日期
 */
export function isValidDate(dateStr: string | null | undefined): boolean {
  if (!dateStr) return false
  const date = new Date(dateStr)
  return !isNaN(date.getTime())
}
