import useUserStore from '@s/modules/UserStore'
import { ElNotification } from 'element-plus'
import router from '@/router'
import NProgress from '@/utils/progress'

const OriginFetch = window.fetch

const whiteList = [/\/auth\//, /\/verify\//]

/**
 * 自定义fetch请求
 * @param input 请求地址
 * @param init 请求配置
 * @param timeout 超时时间
 * @constructor
 */
export default function custom_fetch(
  input: RequestInfo | URL,
  init: RequestInit = {},
  timeout: number = 60000,
  isJson: boolean = true
): Promise<Response> {
  if (!init.headers) {
    init.headers = {}
  }

  //不存在Authorization头且不在白名单
  if (
    !init.headers['Authorization'] &&
    !whiteList.find(white => input.toString().match(white))
  ) {
    // 获取token
    const userStore = useUserStore()
    if (userStore.token) {
      init.headers['Authorization'] = 'Bearer ' + userStore.token
    }
  }

  // 请求body
  if (isJson && !init.headers?.['Content-Type']) {
    init.headers['Content-Type'] = 'application/json'
  }

  //重定向跟随
  init.redirect = 'follow'

  const controller = new AbortController()
  const id = setTimeout(() => controller.abort(), timeout)

  NProgress.start()

  return new Promise<Response>((resolve, reject) => {
    OriginFetch(
      joinPaths(
        import.meta.env.VITE_DEV_PROXY_PREFIX || '',
        import.meta.env.VITE_API_PREFIX || '',
        input
      ),
      init
    )
      .then(res => {
        clearTimeout(id)
        // HTTP 状态码 2xx 状态入口
        if (res.status >= 200 && res.status < 300) {
          resolve(res)
        } else if (res.status == 401) {
          // 跳转登录
          router.push('/login')
        } else if (res.status == 403) {
          // 无权限提示
          ElNotification({
            title: 'forbidden',
            message: '没有访问权限',
            type: 'error'
          })
        } else {
          // 非 2xx 状态入口
          reject(res)
        }
      })
      .catch(err => {
        clearTimeout(id)
        if (err.name === 'AbortError') {
          //超时
          reject(new Error(`请求超时：${input}，超时时间：${timeout}ms`))
        } else {
          reject(err)
        }
      })
      .finally(() => NProgress.done())
  })
}

function joinPaths(...paths) {
  return paths
    .filter(path => path)
    .map((p, i) => {
      // 去掉开头的 /（第一个保留）
      if (i !== 0) p = p.replace(/^\/+/, '')
      // 去掉结尾的 /
      return p.replace(/\/+$/, '')
    })
    .join('/')
}
