import custom_fetch from './custom_fetch'

export interface IResponse {
  success: boolean
  code: string
  msg: string
  timestamp: number
}

export interface IDataResponse<T> extends IResponse {
  data: T
}

export interface IPageResponse<T> extends IDataResponse<T> {
  totalPages: number
  totalRecords: number
  pageSize: number
  pageNum: number
}

export async function request(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  path: RequestInfo | URL,
  data?: object,
  config: RequestInit = {}
) {
  config.method = method

  if (data) {
    if (method === 'GET') {
      config.body = null

      let params = ''
      for (const key in data) {
        params += '&' + key + '=' + encodeURIComponent(data[key])
      }

      return custom_fetch(`${path}?${params.substring(1)}`, config)
    } else {
      config.body = JSON.stringify(data)
    }
  }

  return custom_fetch(path, config)
}

export async function saveFile(url, file, params?) {
  const formData = new FormData()
  formData.append('file', file)
  if (params) {
    for (const key in params) {
      formData.append(key, params[key])
    }
  }
  const config = {
    method: 'POST',
    body: formData
  }
  return custom_fetch(url, config, 60000, false).then(response =>
    response.json()
  )
}

export async function get(
  path: RequestInfo | URL,
  data?: object,
  config?: RequestInit
) {
  return request('GET', path, data, config).then(response => response.json())
}

export async function post(
  path: RequestInfo | URL,
  data?: object,
  config?: RequestInit
) {
  return request('POST', path, data, config).then(response => response.json())
}

export async function put(
  path: RequestInfo | URL,
  data?: object,
  config?: RequestInit
) {
  return request('PUT', path, data, config).then(response => response.json())
}

export async function del(
  path: RequestInfo | URL,
  data?: object,
  config?: RequestInit
) {
  return request('DELETE', path, data, config).then(response => response.json())
}

export async function stream(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  path: RequestInfo | URL,
  data?: object,
  config?: RequestInit
) {
  return request(method, path, data, config).then(response =>
    response.body.getReader().read()
  )
}
