import { request } from '../utils/http/request.js'

const MANAGER_FILE = '/file-manager'
const FILE_KEY = 'simple'

const fileReqUrl = function (id) {
  return `${MANAGER_FILE}/file/${FILE_KEY}/${id}`
}

export const URLS_MANAGER_FILE = {
  MANAGER_FILE: MANAGER_FILE,
  uploadFile: type => `${MANAGER_FILE}/upload/${FILE_KEY}/${type}`,
  getFile: id => `${fileReqUrl(id)}`,
  deleteFile: id => `${MANAGER_FILE}/delete/${FILE_KEY}/${id}`,
  delFileByIds: `${MANAGER_FILE}/deleteAll/${FILE_KEY}`,
  imgFileUrl: id => {
    const prefix = import.meta.env.VITE_DEV_PROXY_PREFIX || ''
    const apiPrefix = import.meta.env.VITE_API_PREFIX || ''
    return `${prefix}${apiPrefix}/${fileReqUrl(id)}`
  },
  async getImgFile(id, mimeType) {
    if (!id) {
      return
    }
    if (!mimeType) {
      mimeType = 'image/png'
    }
    const response = await request(
      'GET',
      `${MANAGER_FILE}/file/${FILE_KEY}/${id}`
    ).then(res => res.blob())

    return (window.URL || window.webkitURL).createObjectURL(response)
  }
}
