<script setup lang="ts">
import { computed } from 'vue'
import { isUrl } from '@pureadmin/utils'
import { menuType } from '@/layout/types'

const props = defineProps<{
  to: menuType
}>()

const isExternalLink = computed(() => {
  return isUrl(props.to.name) || props.to.meta?.externalLink === '1'
})
const target = computed(() =>
  String(props.to.meta?.blank) === '1' ? '_blank' : ''
)
const getLinkProps = (item: menuType) => {
  if (isExternalLink.value) {
    console.log('getLinkProps:', item, isExternalLink.value)
    return {
      href: item.path,
      target: '_blank',
      rel: 'noopener'
    }
  }
  return {
    to: item
  }
}
</script>

<template>
  <component
    :is="isExternalLink ? 'a' : 'router-link'"
    v-bind="getLinkProps(to)"
    :target="target"
  >
    <slot />
  </component>
</template>
