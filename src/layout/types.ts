import type { FunctionalComponent } from 'vue'

const { VITE_HIDE_HOME } = import.meta.env

export const routerArrays: Array<RouteConfigs> =
  VITE_HIDE_HOME === 'false'
    ? [
        {
          path: '/welcome',
          meta: {
            title: 'menus.pureHome',
            icon: 'ep/home-filled'
          }
        }
      ]
    : []

export type routeMetaType = {
  title?: string
  icon?: string | FunctionalComponent
  showLink?: boolean
  savedPosition?: boolean
  auths?: Array<string>
}

export type RouteConfigs = {
  path?: string
  query?: object
  params?: object
  meta?: routeMetaType
  children?: RouteConfigs[]
  name?: string
}

export type multiTagsType = {
  tags: Array<RouteConfigs>
}

export type tagsViewsType = {
  icon: string | FunctionalComponent
  text: string
  divided: boolean
  disabled: boolean
  show: boolean
}

export interface setType {
  sidebar: {
    opened: boolean
    withoutAnimation: boolean
    isClickCollapse: boolean
  }
  device: string
  fixedHeader: boolean
  classes: {
    hideSidebar: boolean
    openSidebar: boolean
    withoutAnimation: boolean
    mobile: boolean
  }
  hideTabs: boolean
}

export type menuType = {
  id?: number
  name?: string
  path?: string
  noShowingChildren?: boolean
  children?: menuType[]
  value: unknown
  meta?: {
    icon?: string
    title?: string
    rank?: number
    showParent?: boolean
    extraIcon?: string
    blank?: string
    externalLink?: string
  }
  showTooltip?: boolean
  parentId?: number
  pathList?: number[]
  redirect?: string
}

export type themeColorsType = {
  color: string
  themeColor: string
}

export interface scrollbarDomType extends HTMLElement {
  wrap?: {
    offsetWidth: number
  }
}
