export const COMPLAIN_STATUS = {
  PENDING: {
    value: '0',
    label: '待处理',
    tagType: 'danger',
    color: '#F56C6C'
  },
  PROCESSING: {
    value: '1',
    label: '处理中',
    tagType: 'warning',
    color: '#E6A23C'
  },
  COMPLETED: {
    value: '2',
    label: '处理完成',
    tagType: 'success',
    color: '#67C23A'
  }
}

// 获取状态配置
export const getStatusConfig = status => {
  return (
    Object.values(COMPLAIN_STATUS).find(item => item.value === status) || {}
  )
}

// 获取状态标签类型
export const getStatusTagType = status => getStatusConfig(status).tagType

// 获取状态文本
export const getStatusText = status => getStatusConfig(status).label
