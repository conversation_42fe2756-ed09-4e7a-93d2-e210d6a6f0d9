import { $t } from '@/plugins/i18n'

export default [
  {
    path: '/error/403',
    name: '403',
    component: () => import('@/views/error/403.vue'),
    meta: {
      title: $t('menus.pureFourZeroOne'),
      permit: true,
      showLink: false
    }
  },
  {
    path: '/error/404',
    name: '404',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: $t('menus.pureFourZeroFour'),
      permit: true,
      showLink: false
    }
  },
  {
    path: '/error/500',
    name: '500',
    component: () => import('@/views/error/500.vue'),
    meta: {
      title: $t('menus.pureFive'),
      permit: true,
      showLink: false
    }
  }
] satisfies Array<RouteConfigsTable>
